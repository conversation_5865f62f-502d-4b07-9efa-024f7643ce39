{"name": "next-init", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 80", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.0.1", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@next/third-parties": "15.3.1", "@paddle/paddle-js": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-toast": "^1.2.13", "@stripe/stripe-js": "^4.1.0", "@tailwindcss/typography": "^0.5.13", "ahooks": "^3.8.4", "ai": "^3.3.0", "antd": "^5.25.1", "axios": "^1.7.3", "canvas-confetti": "^1.9.3", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.12", "devtools-detector": "^2.0.20", "dotenv": "^16.5.0", "google-auth-library": "^9.13.0", "lucide-react": "^0.439.0", "moment": "^2.30.1", "next": "^15.3.2", "next-auth": "^4.24.11", "next-intl": "^3.26.5", "pg": "^8.12.0", "react": "^18.3.1", "react-colorful": "^5.6.1", "react-copy-to-clipboard": "^5.1.0", "react-cropper": "^2.3.3", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-markdown-editor-lite": "^1.3.4", "react-share": "^5.2.2", "react-share-social": "^0.1.60", "react-syntax-highlighter": "^15.5.0", "react-tailwindcss-datepicker": "^2.0.0", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.0", "react-turnstile": "^1.1.4", "remark-gfm": "^4.0.0", "replicate": "^1.0.1", "short-unique-id": "^5.2.2", "short-uuid": "^5.2.0", "slugify": "^1.6.6", "stripe": "^16.6.0", "swiper": "^11.1.14", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}