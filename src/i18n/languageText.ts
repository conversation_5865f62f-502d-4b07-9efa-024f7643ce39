import { getTranslations } from "next-intl/server";
import { policyEffectiveDate, siteNameConfig, supportEmail, websiteUrl } from "~/configs/globalConfig";
import { getIndexPageData } from "~/servers/manage/page/page";

export const getAuthText = async () => {
  const tAuth = await getTranslations("AuthText");
  return {
    loginText: tAuth("loginText"),
    logoutText: tAuth("logoutText"),
    loginModalDesc: tAuth("loginModalDesc"),
    loginModalButtonText: tAuth("loginModalButtonText"),
    logoutModalDesc: tAuth("logoutModalDesc"),
    confirmButtonText: tAuth("confirmButtonText"),
    cancelButtonText: tAuth("cancelButtonText"),
  };
};

export const getCommonText = async () => {
  const tCommon = await getTranslations("CommonText");
  return {
    loadingText: tCommon("loadingText"),
    generateText: tCommon("generateText"),
    downloadingText: tCommon("downloadingText"),
    deleteText: tCommon("deleteText"),
    deleteModalDesc: tCommon("deleteModalDesc"),
    download: tCommon("download"),
    generateNew: tCommon("generateNew"),
    creditsHaveText: tCommon("creditsHaveText"),
    firstCreditsText: tCommon("firstCreditsText"),
    inviteLinkText: tCommon("inviteLinkText"),
  };
};

export const getPrivacyPolicyText = async () => {
  const tPrivacyPolicy = await getTranslations("PrivacyPolicyText");
  const detailText = tPrivacyPolicy("detailText")
    .replace(/%Email_Address_Config%/g, supportEmail)
    .replace(/%Site_Name_Config%/g, siteNameConfig)
    .replace(/%Policy_Effective_Date%/g, policyEffectiveDate)
    .replace(/%Domain_Name_Https%/g, websiteUrl);

  return {
    title: tPrivacyPolicy("title"),
    description: tPrivacyPolicy("description"),
    h1Text: tPrivacyPolicy("h1Text"),
    detailText: detailText,
  };
};

export const getTermsOfServiceText = async () => {
  const tTermsOfService = await getTranslations("TermsOfServiceText");
  const indexPageData = await getIndexPageData();

  const detailText = tTermsOfService("detailText")
    .replace(/%Email_Address_Config%/g, supportEmail)
    .replace(/%Site_Name_Config%/g, siteNameConfig)
    .replace(/%Policy_Effective_Date%/g, policyEffectiveDate)
    .replace(/%Domain_Name_Https%/g, websiteUrl)
    .replace(/%Site_Index_Description_Text%/g, indexPageData?.page_description || "");
  return {
    title: tTermsOfService("title"),
    description: tTermsOfService("description"),
    h1Text: tTermsOfService("h1Text"),
    detailText: detailText,
  };
};

export const getRefundPolicyText = async () => {
  const tRefundPolicy = await getTranslations("RefundPolicyText");
  const detailText = tRefundPolicy("detailText")
    .replace(/%Email_Address_Config%/g, supportEmail)
    .replace(/%Site_Name_Config%/g, siteNameConfig);

  return {
    title: tRefundPolicy("title"),
    description: tRefundPolicy("description"),
    h1Text: tRefundPolicy("h1Text"),
    detailText: detailText,
  };
};

export const getCongratulationsText = async () => {
  const tCongratulations = await getTranslations("CongratulationsText");

  return {
    title: tCongratulations("title"),
    description: tCongratulations("description"),
    h1Text: tCongratulations("h1Text"),
    h2Text: tCongratulations("h2Text"),
  };
};

export const getPricingText = async () => {
  const tPricing = await getTranslations("PricingText");
  const tPricingAddition = await getTranslations("PricingTextAddition");

  return {
    basic: tPricing("basic"),
    essential: tPricing("essential"),
    growth: tPricing("growth"),
    buyText: tPricing("buyText"),
    popularText: tPricing("popularText"),
    monthText: tPricing("monthText"),
    monthlyText: tPricing("monthlyText"),
    annualText: tPricing("annualText"),
    annuallyText: tPricing("annuallyText"),
    alreadySubscribed: tPricing("alreadySubscribed"),
    subscribeHasCanceled: tPricing("subscribeHasCanceled"),
    creditText: tPricing("creditText"),
    creditsText: tPricing("creditsText"),
    creditsText2: tPricing("creditsText2"),
    creditsText3: tPricing("creditsText3"),
    creditsTextPerDay: tPricing("creditsTextPerDay"),
    creditsTextFree: tPricing("creditsTextFree"),
    creditsTextPayMonth: tPricing("creditsTextPayMonth"),
    creditsTextPayYear: tPricing("creditsTextPayYear"),
    free: tPricing("free"),
    free0: tPricing("free0"),
    freeText: tPricing("freeText"),
    freeIntro0: tPricing("freeIntro0"),
    freeIntro1: tPricing("freeIntro1"),
    freeIntro2: tPricing("freeIntro2"),
    freeIntro3: tPricing("freeIntro3"),
    freeIntro4: tPricing("freeIntro4"),
    payIntro1: tPricing("payIntro1"),
    payIntro2: tPricing("payIntro2"),
    payIntro3: tPricing("payIntro3"),
    payIntro4: tPricing("payIntro4"),
    payIntro5: tPricing("payIntro5"),
    payIntro6: tPricing("payIntro6"),
    payIntro7: tPricing("payIntro7"),
    oneTimePurchase: tPricing("oneTimePurchase"),
    loginToGetText: tPricing("loginToGetText"),
    contactTip: tPricing("contactTip"),

    title: tPricingAddition("title"),
    description: tPricingAddition("description"),
    h1Text: tPricingAddition("h1Text"),
  };
};

export const getMenuText = async () => {
  const tMenu = await getTranslations("MenuText");
  return {
    header0: tMenu("header0"),
    header1: tMenu("header1"),
    header2: tMenu("header2"),
    footerLegal: tMenu("footerLegal"),
    footerLegal0: tMenu("footerLegal0"),
    footerLegal1: tMenu("footerLegal1"),
    footerLegal2: tMenu("footerLegal2"),
    footerSupport: tMenu("footerSupport"),
    footerSupport0: tMenu("footerSupport0"),
    footerSupport1: tMenu("footerSupport1"),
    footerSupport2: tMenu("footerSupport2"),
  };
};

export const getIndexPageText = async () => {
  const tIndex = await getTranslations("IndexPageText");

  return {
    title: tIndex("title"),
    description: tIndex("description"),
    h1Text: tIndex("h1Text"),
    descriptionBelowH1Text: tIndex("descriptionBelowH1Text"),
  };
};

export const getMyPageText = async () => {
  const myPageText = await getTranslations("myPageText");
  return {
    title: myPageText("title"),
    description: myPageText("description"),
    h1Text: myPageText("h1Text"),
    descriptionBelowH1Text: myPageText("descriptionBelowH1Text"),
    h2Text: myPageText("h2Text"),
  };
};

export const getDetailPageText = async () => {
  const detailPageText = await getTranslations("DetailPageText");
  return {
    title: detailPageText("title"),
    description: detailPageText("description"),
    h1Text: detailPageText("h1Text"),
    descriptionBelowH1Text: detailPageText("descriptionBelowH1Text"),
    numberText: detailPageText("numberText"),
    h2Text: detailPageText("h2Text"),
  };
};
