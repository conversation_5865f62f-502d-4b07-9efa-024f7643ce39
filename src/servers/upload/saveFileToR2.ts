import { handleWebhook, r2Bucket, saveFileWorker, storageUrl } from "~/configs/globalConfig";

// 调用上传文件到 R2，R2 上传成功之后回调结果

// 上传单个文件
export const uploadFileToR2 = async (uid, fileObj: { fileKey: string; fileUrl: string; fileContentType: string }, needCallback = false, callbackUrl = "") => {
  const fileList = [fileObj];
  return uploadFileListToR2(uid, fileList, needCallback, callbackUrl);
};

// 上传多个文件，参数参考 上传单个文件的入参
export const uploadFileListToR2 = async (uid, fileList, needCallback = false, callbackUrl = "") => {
  // 如果需要回调，则生成回调地址
  const saveCompleteUrl = needCallback ? `${handleWebhook}/api/generate/saveFileComplete?uid=${uid}` : "";

  const resultCallbackUrl = callbackUrl || saveCompleteUrl;

  // 请求数据
  const requestJson = {
    bucketName: r2Bucket,
    files: fileList,
    callbackUrl: resultCallbackUrl,
    storageUrl: storageUrl,
  };

  // 调用 worker 上传文件
  const responseData = await fetch(saveFileWorker, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(requestJson),
  });
  const responseDataJson = await responseData.json();
  console.log("responseData-=-=->", responseDataJson);
};
