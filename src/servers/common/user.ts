import { getDb } from "~/libs/db/db";
import { getServerSession } from "next-auth";
import { generateNewUID } from "~/utils/uidUtil";
import { checkRegisterAndAddAvailableTimes } from "../invite/inviteRecord";
import { freeGenerateTimes } from "~/configs/globalConfig";

const db = getDb();

export const checkAndSaveUser = async (json) => {
  const name = json.name;
  const email = json.email;
  const image = json.image;
  const last_login_ip = json.userIp;
  const user_agent = json.user_agent;
  const country = json.country;
  let city = json.city;
  // URL解码city的字符串
  try {
    city = decodeURI(json.city);
  } catch (e) {
    city = json.city;
  }

  const { rows: resultList } = await db.query(
    `
    select ui.*,ua.stripe_customer_id, ua.paddle_customer_id, ua.paypro_customer_id, ua.available_times, ua.download_times
        from user_info ui
        left join user_available ua on ua.user_id=ui.user_id
        where ui.email=$1 and ui.is_delete=false
    `,
    [email]
  );
  if (resultList.length <= 0) {
    const result = {
      user_id: "",
      name: "",
      email: "",
      image: "",
      stripe_customer_id: "",
      paddle_customer_id: "",
      paypro_customer_id: "",
      available_times: 0,
      download_times: 0,
    };
    // 新增
    const newUserId = generateNewUID();
    await db.query(
      `
      insert into user_info(user_id,name,email,image,last_login_ip, user_agent, country, city)
      values($1,$2,$3,$4,$5,$6,$7,$8)
      `,
      [newUserId, name, email, image, last_login_ip, user_agent, country, city]
    );

    // 免费生成次数
    const freeTimes = freeGenerateTimes;
    await db.query(
      `
      insert into user_available(user_id,available_times) values($1, $2)
      `,
      [newUserId, freeTimes]
    );

    result.user_id = newUserId;
    result.name = name;
    result.email = email;
    result.image = image;
    result.available_times = freeTimes;
    return result;
  } else {
    // 更新
    const user = resultList[0];
    await db.query(
      `
      update user_info set image=$2,last_login_ip=$3,updated_at=now(),user_agent=$4,country=$5,city=$6 where id=$1
      `,
      [user.id, image, last_login_ip, user_agent, country, city]
    );
    return user;
  }
};

export const getUserById = async (user_id) => {
  const { rows: resultList } = await db.query(
    `
    select ui.*,ua.stripe_customer_id,ua.paddle_customer_id,ua.paypro_customer_id, ua.available_times, ua.download_times from user_info ui
        left join user_available ua on ua.user_id=ui.user_id
        where ui.user_id=$1
        limit 1
    ;
    `,
    [user_id]
  );
  if (resultList.length > 0) {
    const user = resultList[0];
    return {
      user_id: user_id,
      name: user.name,
      email: user.email,
      image: user.image,
      status: 1,
      stripe_customer_id: user.stripe_customer_id,
      paddle_customer_id: user.paddle_customer_id,
      paypro_customer_id: user.paypro_customer_id,
      available_times: user.available_times,
      download_times: user.download_times,
    };
  }
  return {
    user_id: user_id,
    name: "",
    email: "",
    image: "",
    status: 0,
    stripe_customer_id: "",
    paddle_customer_id: "",
    paypro_customer_id: "",
    available_times: 0,
    download_times: 0,
  };
};

export const getUserByEmail = async (json) => {
  let email = json.email;
  const { rows: resultList } = await db.query(
    `
    select ui.*,ua.stripe_customer_id, ua.available_times, ua.download_times, ua.paddle_customer_id, ua.paypro_customer_id from user_info ui
        left join user_available ua on ua.user_id=ui.user_id
        where ui.email=$1
        limit 1
    ;
  `,
    [email]
  );
  if (resultList.length > 0) {
    const user = resultList[0];

    return {
      user_id: user.user_id,
      name: user.name,
      email: email,
      image: user.image,
      status: 1,
      stripe_customer_id: user.stripe_customer_id,
      paddle_customer_id: user.paddle_customer_id,
      paypro_customer_id: user.paypro_customer_id,
      available_times: user.available_times,
      download_times: user.download_times,
      created_at: user.created_at,
      role: user.role,
      is_banned: user.is_banned,
    };
  }
  return {
    user_id: "",
    name: "",
    email: email,
    image: "",
    status: 0,
    stripe_customer_id: "",
    paddle_customer_id: "",
    paypro_customer_id: "",
    available_times: 0,
    download_times: 0,
    role: "user",
    is_banned: false,
  };
};

export const updateUserLoginFrom = async (json) => {
  const user_id = json.user_id;
  const login_from = json.login_from;
  await db.query(
    `
    update user_info set login_from=$2 where user_id=$1
    `,
    [user_id, login_from]
  );
};

export const updateUserInfo = async (json) => {
  const user_id = json.user_id;
  const userIp = json.userIp;
  const user_agent = json.user_agent;
  await db.query(
    `
    update user_info set last_login_ip=$2,user_agent=$3 where user_id=$1
    `,
    [user_id, userIp, user_agent]
  );
  return json;
};

// 服务端根据 session 获取用户信息，直接 getServerSession 获取到的没有 user_id
export const getUserByServerSession = async () => {
  const session = await getServerSession();
  if (session) {
    const email = session?.user.email;
    if (email) {
      return await getUserByEmail({ email });
    }
  }
  return {
    user_id: "",
    name: "",
    email: "",
    image: "",
    status: 0,
    role: "user",
    is_banned: false,
  };
};

export const getUserByEmailAndCode = async (json, user_info) => {
  let code = json.code;
  if (code) {
    // 判断用户注册时间，如果注册时间在当前时间1分钟内，增加可用次数
    const existTime = new Date(user_info.created_at).getTime();
    const currentTime = new Date().getTime();
    const resultTime = (currentTime - existTime) / 1000;
    if (resultTime < 1 * 60) {
      await checkRegisterAndAddAvailableTimes(code, user_info);
    }
  }

  let login_from = json.login_from;
  if (login_from) {
    if (!user_info.login_from) {
      // 判断用户注册时间，如果注册时间在当前时间1分钟内，更新登录来源
      const existTime = new Date(user_info.created_at).getTime();
      const currentTime = new Date().getTime();
      const resultTime = (currentTime - existTime) / 1000;
      if (resultTime < 1 * 60) {
        await updateUserLoginFrom({ user_id: user_info.user_id, login_from: login_from });
      }
    }
  }

  return {
    user_id: user_info.user_id,
    name: user_info.name,
    email: user_info.email,
    image: user_info.image,
    status: 1,
    stripe_customer_id: user_info.stripe_customer_id,
    paddle_customer_id: user_info.paddle_customer_id,
    paypro_customer_id: user_info.paypro_customer_id,
    available_times: user_info.available_times,
    download_times: user_info.download_times,
    created_at: user_info.created_at,
  };
};
