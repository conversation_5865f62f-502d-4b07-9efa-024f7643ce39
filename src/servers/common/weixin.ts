import { wecomNotifyUrl } from "~/configs/globalConfig";

export const notifyToWeixin = async (message: string) => {
  const requestUrl = wecomNotifyUrl;
  const data = {
    msgtype: "text",
    text: {
      content: message,
    },
  };
  const response = await fetch(requestUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  const result = await response.json();
  console.log("notifyToWeixin result", result);
  return true;
};
