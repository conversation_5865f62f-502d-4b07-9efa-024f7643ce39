import { getDb } from "~/libs/db/db";

const db = getDb();

export const getExploreMode = async () => {
  const { rows: results } = await db.query("select * from key_value where key=$1 limit 1", ["explore_mode"]);
  if (results.length > 0) {
    const origin = results[0];
    if (origin.value === "1") {
      return true;
    }
    return false;
  }
  return false;
};

export const setExploreMode = async (mode) => {
  const value = mode ? "1" : "0";
  await db.query("update key_value set value=$1 where key=$2", [value, "explore_mode"]);
};

// choose api
export const getAPI = async () => {
  const { rows: results } = await db.query("select * from key_value where key=$1 limit 1", ["api"]);
  if (results.length > 0) {
    const origin = results[0];
    return origin.value;
  }
  return "runway";
};
