import { getDb } from "~/libs/db/db";
import { generateNewUID } from "~/utils/uidUtil";

// 获取所有页面带分页和筛选条件
export async function getPages(params: { 
  pageSize?: number; 
  skipSize?: number; 
  gameUid?: string;
  siteUid?: string;
  status?: number;
  keyword?: string;
}): Promise<{
  resultList: any[];
  totalPage: number;
  countTotal: number;
}> {
  const db = getDb();
  
  const pageSize = params.pageSize || 10;
  const skipSize = params.skipSize || 0;
  
  let whereConditions = [];
  let queryParams = [];
  let paramIndex = 1;
  
  // 添加筛选条件
  if (params.gameUid) {
    whereConditions.push(`game_uid = $${paramIndex++}`);
    queryParams.push(params.gameUid);
  }
  
  if (params.siteUid) {
    whereConditions.push(`site_uid = $${paramIndex++}`);
    queryParams.push(params.siteUid);
  }
  
  if (params.status !== undefined) {
    whereConditions.push(`status = $${paramIndex++}`);
    queryParams.push(params.status);
  }
  
  if (params.keyword) {
    whereConditions.push(`(title ILIKE $${paramIndex} OR slug ILIKE $${paramIndex})`);
    queryParams.push(`%${params.keyword}%`);
    paramIndex++;
  }
  
  // 构建WHERE子句
  const whereClause = whereConditions.length > 0 
    ? `WHERE ${whereConditions.join(' AND ')}` 
    : '';
  
  // 查询总数
  const countQuery = `
    SELECT COUNT(*) as count
    FROM pages
    ${whereClause}
  `;
  
  // 查询页面列表
  const pagesQuery = `
    SELECT p.*, p.public_url, g.keyword as game_keyword, s.name as site_name
    FROM pages p
    LEFT JOIN games g ON p.game_uid = g.uid
    LEFT JOIN sites s ON p.site_uid = s.uid
    ${whereClause}
    ORDER BY p.created_at DESC
    LIMIT $${paramIndex++} OFFSET $${paramIndex++}
  `;
  
  try {
    const [countResult, pagesResult] = await Promise.all([
      db.query(countQuery, queryParams),
      db.query(pagesQuery, [...queryParams, pageSize, skipSize])
    ]);
    
    const total = parseInt(countResult.rows[0].count);
    
    return {
      resultList: pagesResult.rows,
      totalPage: Math.ceil(total / pageSize),
      countTotal: total
    };
  } catch (error) {
    console.error("Error fetching pages:", error);
    throw error;
  }
}

// 根据UID获取页面详情
export async function getPageByUid(uid: string): Promise<any | null> {
  const db = getDb();
  
  try {
    const query = `
      SELECT p.*, g.keyword as game_keyword, s.name as site_name
      FROM pages p
      LEFT JOIN games g ON p.game_uid = g.uid
      LEFT JOIN sites s ON p.site_uid = s.uid
      WHERE p.uid = $1
    `;
    
    const { rows } = await db.query(query, [uid]);
    
    if (rows.length === 0) {
      return null;
    }
    
    return rows[0];
  } catch (error) {
    console.error(`Error fetching page with UID ${uid}:`, error);
    throw error;
  }
}

// 更新页面
export async function updatePage(uid: string, data: {
  title?: string;
  slug?: string;
  json_content?: any;
  status?: number;
  iframe_url?: string;
  public_url?: string;
}): Promise<any | null> {
  const db = getDb();
  
  try {
    // 检查页面是否存在
    const existingPage = await getPageByUid(uid);
    if (!existingPage) {
      return null;
    }
    
    // 构建更新字段
    const updates = [];
    const values = [];
    let paramIndex = 1;
    
    if (data.title !== undefined) {
      updates.push(`title = $${paramIndex++}`);
      values.push(data.title);
    }
    
    if (data.slug !== undefined) {
      updates.push(`slug = $${paramIndex++}`);
      values.push(data.slug);
    }
    
    if (data.json_content !== undefined) {
      updates.push(`json_content = $${paramIndex++}`);
      values.push(JSON.stringify(data.json_content));
    }
    
    if (data.status !== undefined) {
      updates.push(`status = $${paramIndex++}`);
      values.push(data.status);
    }
    
    if (data.iframe_url !== undefined) {
      updates.push(`iframe_url = $${paramIndex++}`);
      values.push(data.iframe_url);
    }
    
    if (data.public_url !== undefined) {
      updates.push(`public_url = $${paramIndex++}`);
      values.push(data.public_url);
    }
    
    // 总是更新updated_at字段
    updates.push(`updated_at = NOW()`);
    
    // 如果没有要更新的字段，则直接返回现有页面
    if (updates.length === 1) {
      return existingPage;
    }
    
    // 构建并执行更新查询
    const query = `
      UPDATE pages 
      SET ${updates.join(', ')} 
      WHERE uid = $${paramIndex}
      RETURNING *
    `;
    
    values.push(uid);
    
    const { rows } = await db.query(query, values);
    
    return rows[0];
  } catch (error) {
    console.error(`Error updating page with UID ${uid}:`, error);
    throw error;
  }
}

// 删除页面
export async function deletePage(uid: string): Promise<boolean> {
  const db = getDb();
  
  try {
    const query = `DELETE FROM pages WHERE uid = $1 RETURNING uid`;
    const { rows } = await db.query(query, [uid]);
    
    return rows.length > 0;
  } catch (error) {
    console.error(`Error deleting page with UID ${uid}:`, error);
    throw error;
  }
}

// 更改页面状态
export async function changePageStatus(uid: string, status: number): Promise<any | null> {
  return updatePage(uid, { status });
}

// 批量更改页面状态
export async function batchChangePageStatus(uids: string[], status: number): Promise<{ 
  success: boolean; 
  updatedCount: number;
  failedUids: string[];
}> {
  const db = getDb();
  
  try {
    const placeholders = uids.map((_, i) => `$${i + 1}`).join(',');
    const query = `
      UPDATE pages
      SET status = $${uids.length + 1}, updated_at = NOW()
      WHERE uid IN (${placeholders})
      RETURNING uid
    `;
    
    const { rows } = await db.query(query, [...uids, status]);
    
    const updatedUids = rows.map(row => row.uid);
    const failedUids = uids.filter(uid => !updatedUids.includes(uid));
    
    return {
      success: updatedUids.length > 0,
      updatedCount: updatedUids.length,
      failedUids
    };
  } catch (error) {
    console.error(`Error batch updating page status:`, error);
    throw error;
  }
} 