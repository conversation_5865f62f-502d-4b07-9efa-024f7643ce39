import { getDb } from "~/libs/db/db";
import { Game } from "~/utils/types/game";
import { generateNewUID } from "~/utils/uidUtil";

// Create a new game
export async function createGame(gameData: Omit<Game, "id" | "created_at" | "updated_at" | "is_delete">): Promise<Game> {
  const db = getDb();

  try {
    // 生成唯一的uid
    const uid = generateNewUID();
    
    const result = await db.query(
      `INSERT INTO games (
        uid, keyword, iframe_url, reference_data, tags
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING *`,
      [uid, gameData.keyword, gameData.iframe_url, gameData.reference_data, gameData.tags ? JSON.stringify(gameData.tags) : null]
    );

    return result.rows[0];
  } catch (error) {
    console.error("Error creating game:", error);
    throw error;
  }
}

// Get game by ID - 保留但不推荐使用
export async function getGameById(id: number): Promise<Game | null> {
  const db = getDb();

  try {
    const result = await db.query(`SELECT * FROM games WHERE id = $1 AND is_delete = false`, [id]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error fetching game by ID:", error);
    throw error;
  }
}

// Get game by UID - 推荐使用
export async function getGameByUid(uid: string): Promise<Game | null> {
  const db = getDb();

  try {
    const result = await db.query(`SELECT * FROM games WHERE uid = $1 AND is_delete = false`, [uid]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error fetching game by UID:", error);
    throw error;
  }
}

// Get games list with pagination and filtering
export async function getGames(page: number = 1, pageSize: number = 10, keyword?: string): Promise<{ games: Game[]; total: number }> {
  const db = getDb();
  const offset = (page - 1) * pageSize;

  let query = `SELECT * FROM games WHERE is_delete = false`;
  let countQuery = `SELECT COUNT(*) FROM games WHERE is_delete = false`;
  const params = [];
  let paramIndex = 1;

  if (keyword) {
    query += ` AND (keyword ILIKE $${paramIndex} OR name ILIKE $${paramIndex})`;
    countQuery += ` AND (keyword ILIKE $${paramIndex} OR name ILIKE $${paramIndex})`;
    params.push(`%${keyword}%`);
    paramIndex++;
  }

  query += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
  params.push(pageSize, offset);

  try {
    const [gamesResult, countResult] = await Promise.all([db.query(query, params), db.query(countQuery, params.slice(0, paramIndex - 1))]);

    return {
      games: gamesResult.rows,
      total: parseInt(countResult.rows[0].count, 10),
    };
  } catch (error) {
    console.error("Error fetching games:", error);
    throw error;
  }
}

// Update game by ID - 保留但不推荐使用
export async function updateGame(id: number, gameData: Partial<Omit<Game, "id" | "created_at" | "updated_at" | "is_delete">>): Promise<Game> {
  const db = getDb();

  // Build update query dynamically based on provided fields
  const updateFields = [];
  const params = [];
  let paramIndex = 1;

  if (gameData.keyword !== undefined) {
    updateFields.push(`keyword = $${paramIndex++}`);
    params.push(gameData.keyword);
  }

  if (gameData.iframe_url !== undefined) {
    updateFields.push(`iframe_url = $${paramIndex++}`);
    params.push(gameData.iframe_url);
  }

  if (gameData.reference_data !== undefined) {
    updateFields.push(`reference_data = $${paramIndex++}`);
    params.push(gameData.reference_data);
  }

  if (gameData.tags !== undefined) {
    updateFields.push(`tags = $${paramIndex++}`);
    params.push(gameData.tags ? JSON.stringify(gameData.tags) : null);
  }

  updateFields.push(`updated_at = NOW()`);

  // If no fields to update, return the existing game
  if (updateFields.length === 1) {
    // Only updated_at
    return getGameById(id) as Promise<Game>;
  }

  params.push(id);

  try {
    const result = await db.query(
      `UPDATE games 
      SET ${updateFields.join(", ")} 
      WHERE id = $${paramIndex} AND is_delete = false
      RETURNING *`,
      params
    );

    if (result.rows.length === 0) {
      throw new Error(`Game with ID ${id} not found or is deleted`);
    }

    return result.rows[0];
  } catch (error) {
    console.error("Error updating game:", error);
    throw error;
  }
}

// Update game by UID - 推荐使用
export async function updateGameByUid(uid: string, gameData: Partial<Omit<Game, "id" | "uid" | "created_at" | "updated_at" | "is_delete">>): Promise<Game> {
  const db = getDb();

  // First check if game exists
  const existingGame = await getGameByUid(uid);
  if (!existingGame) {
    throw new Error(`Game with UID ${uid} not found or is deleted`);
  }

  // Build update query dynamically based on provided fields
  const updateFields = [];
  const params = [];
  let paramIndex = 1;

  if (gameData.keyword !== undefined) {
    updateFields.push(`keyword = $${paramIndex++}`);
    params.push(gameData.keyword);
  }

  if (gameData.iframe_url !== undefined) {
    updateFields.push(`iframe_url = $${paramIndex++}`);
    params.push(gameData.iframe_url);
  }

  if (gameData.reference_data !== undefined) {
    updateFields.push(`reference_data = $${paramIndex++}`);
    params.push(gameData.reference_data);
  }

  if (gameData.tags !== undefined) {
    updateFields.push(`tags = $${paramIndex++}`);
    params.push(gameData.tags ? JSON.stringify(gameData.tags) : null);
  }

  updateFields.push(`updated_at = NOW()`);

  // If no fields to update, return the existing game
  if (updateFields.length === 1) {
    // Only updated_at
    return existingGame;
  }

  params.push(uid);

  try {
    const result = await db.query(
      `UPDATE games 
      SET ${updateFields.join(", ")} 
      WHERE uid = $${paramIndex} AND is_delete = false
      RETURNING *`,
      params
    );

    if (result.rows.length === 0) {
      throw new Error(`Game with UID ${uid} not found or is deleted`);
    }

    return result.rows[0];
  } catch (error) {
    console.error("Error updating game by UID:", error);
    throw error;
  }
}

// Delete game by ID (soft delete) - 保留但不推荐使用
export async function deleteGame(id: number): Promise<boolean> {
  const db = getDb();

  try {
    const result = await db.query(
      `UPDATE games 
      SET is_delete = true, updated_at = NOW() 
      WHERE id = $1 AND is_delete = false`,
      [id]
    );

    return result.rowCount > 0;
  } catch (error) {
    console.error("Error deleting game:", error);
    throw error;
  }
}

// Delete game by UID (soft delete) - 推荐使用
export async function deleteGameByUid(uid: string): Promise<boolean> {
  const db = getDb();

  try {
    const result = await db.query(
      `UPDATE games 
      SET is_delete = true, updated_at = NOW() 
      WHERE uid = $1 AND is_delete = false`,
      [uid]
    );

    return result.rowCount > 0;
  } catch (error) {
    console.error("Error deleting game by UID:", error);
    throw error;
  }
}
