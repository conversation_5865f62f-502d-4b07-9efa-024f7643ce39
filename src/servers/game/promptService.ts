import { getDb } from "~/libs/db/db";
import { Prompt } from "~/utils/types/game";

// Mock data for testing when database is not available
const mockPrompts = [
  {
    id: 1,
    key: "game_description_v1",
    description: "Generates engaging game descriptions with key features and gameplay highlights",
    llm_model_compatibility: ["gpt-3.5-turbo", "gpt-4"],
    version: "1.0",
    content_template: "Generate an engaging description for a game called '{{game_name}}' in {{language}}...",
    created_at: new Date("2023-07-15T00:00:00Z"),
    updated_at: new Date("2023-07-15T00:00:00Z"),
    is_delete: false,
  },
  {
    id: 2,
    key: "seo_landing_page",
    description: "Generates an optimized SEO landing page for games with structured sections",
    llm_model_compatibility: ["gpt-4", "claude-2"],
    version: "1.0",
    content_template: "Please create an optimized SEO landing page refer to the provided content...",
    created_at: new Date("2023-06-10T00:00:00Z"),
    updated_at: new Date("2023-06-10T00:00:00Z"),
    is_delete: false,
  },
];

// Flag to use mock data instead of database
const USE_MOCK_DATA = true;

/**
 * Retrieves a list of prompts with pagination and optional filtering by key or description
 */
export async function getPrompts(page = 1, pageSize = 10, keyFilter?: string, descriptionFilter?: string) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {

    let filteredPrompts = [...mockPrompts];

    // Apply filters if provided
    if (keyFilter) {
      filteredPrompts = filteredPrompts.filter((p) => p.key.toLowerCase().includes(keyFilter.toLowerCase()));
    }

    if (descriptionFilter) {
      filteredPrompts = filteredPrompts.filter((p) => p.description && p.description.toLowerCase().includes(descriptionFilter.toLowerCase()));
    }

    // Calculate pagination
    const totalCount = filteredPrompts.length;
    const offset = (page - 1) * pageSize;
    const paginatedPrompts = filteredPrompts.slice(offset, offset + pageSize);

    return {
      prompts: paginatedPrompts,
      total: totalCount,
    };
  }

  // Original database implementation
  const db = getDb();
  const offset = (page - 1) * pageSize;

  try {
    let totalCountQuery = "SELECT COUNT(*) FROM prompts WHERE is_delete = false";
    let queryParams: any[] = [];

    if (keyFilter) {
      totalCountQuery += " AND key ILIKE $1";
      queryParams.push(`%${keyFilter}%`);
    }

    if (descriptionFilter) {
      totalCountQuery += ` AND description ILIKE $${queryParams.length + 1}`;
      queryParams.push(`%${descriptionFilter}%`);
    }

    const totalCountResult = await db.query(totalCountQuery, queryParams);
    const totalCount = parseInt(totalCountResult.rows[0].count, 10);

    let promptsQuery = `
      SELECT 
        id, key, description, llm_model_compatibility, 
        version, content_template, created_at, updated_at
      FROM prompts 
      WHERE is_delete = false
    `;

    if (keyFilter) {
      promptsQuery += " AND key ILIKE $1";
    }

    if (descriptionFilter) {
      promptsQuery += ` AND description ILIKE $${queryParams.length + 1}`;
    }

    promptsQuery += " ORDER BY created_at DESC LIMIT $" + (queryParams.length + 1) + " OFFSET $" + (queryParams.length + 2);
    queryParams.push(pageSize, offset);

    const promptsResult = await db.query(promptsQuery, queryParams);

    return {
      prompts: promptsResult.rows,
      total: totalCount,
    };
  } catch (error) {
    console.error("Error in getPrompts:", error);
    throw error;
  }
}

/**
 * Retrieves a single prompt by its ID
 */
export async function getPromptById(id: number) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {
    return mockPrompts.find((p) => p.id === id) || null;
  }

  // Original database implementation
  const db = getDb();

  try {
    const result = await db.query(
      `
      SELECT 
        id, key, description, llm_model_compatibility, 
        version, content_template, created_at, updated_at
      FROM prompts 
      WHERE id = $1 AND is_delete = false
      `,
      [id]
    );

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error in getPromptById:", error);
    throw error;
  }
}

/**
 * Retrieves a single prompt by its key
 */
export async function getPromptByKey(key: string) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {
    return mockPrompts.find((p) => p.key === key) || null;
  }

  // Original database implementation
  const db = getDb();

  try {
    const result = await db.query(
      `
      SELECT 
        id, key, description, llm_model_compatibility, 
        version, content_template, created_at, updated_at
      FROM prompts 
      WHERE key = $1 AND is_delete = false
      `,
      [key]
    );

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error in getPromptByKey:", error);
    throw error;
  }
}

/**
 * Creates a new prompt
 */
export async function createPrompt(prompt: { key: string; description?: string; llm_model_compatibility?: string[]; version?: string; content_template: string }) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {

    // Check if key already exists
    if (mockPrompts.some((p) => p.key === prompt.key)) {
      throw new Error(`Prompt with key '${prompt.key}' already exists`);
    }

    const newPrompt = {
      id: mockPrompts.length + 1,
      key: prompt.key,
      description: prompt.description || null,
      llm_model_compatibility: prompt.llm_model_compatibility || null,
      version: prompt.version || "1.0",
      content_template: prompt.content_template,
      created_at: new Date(),
      updated_at: new Date(),
      is_delete: false,
    };

    mockPrompts.push(newPrompt);
    return newPrompt;
  }

  // Original database implementation
  const db = getDb();

  try {
    // Check if key already exists
    const existingPrompt = await getPromptByKey(prompt.key);
    if (existingPrompt) {
      throw new Error(`Prompt with key '${prompt.key}' already exists`);
    }

    const result = await db.query(
      `
      INSERT INTO prompts (
        key, description, llm_model_compatibility, 
        version, content_template
      ) 
      VALUES ($1, $2, $3, $4, $5) 
      RETURNING 
        id, key, description, llm_model_compatibility, 
        version, content_template, created_at, updated_at
      `,
      [prompt.key, prompt.description || null, prompt.llm_model_compatibility ? JSON.stringify(prompt.llm_model_compatibility) : null, prompt.version || "1.0", prompt.content_template]
    );

    return result.rows[0];
  } catch (error) {
    console.error("Error in createPrompt:", error);
    throw error;
  }
}

/**
 * Updates an existing prompt
 */
export async function updatePrompt(
  id: number,
  promptData: {
    key?: string;
    description?: string;
    llm_model_compatibility?: string[];
    version?: string;
    content_template?: string;
  }
) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {

    // Find prompt index
    const promptIndex = mockPrompts.findIndex((p) => p.id === id);
    if (promptIndex === -1) {
      return null;
    }

    const existingPrompt = mockPrompts[promptIndex];

    // If key is being changed, check if the new key already exists
    if (promptData.key && promptData.key !== existingPrompt.key) {
      if (mockPrompts.some((p) => p.key === promptData.key)) {
        throw new Error(`Prompt with key '${promptData.key}' already exists`);
      }
    }

    // Update prompt
    const updatedPrompt = {
      ...existingPrompt,
      ...(promptData.key !== undefined && { key: promptData.key }),
      ...(promptData.description !== undefined && { description: promptData.description }),
      ...(promptData.llm_model_compatibility !== undefined && { llm_model_compatibility: promptData.llm_model_compatibility }),
      ...(promptData.version !== undefined && { version: promptData.version }),
      ...(promptData.content_template !== undefined && { content_template: promptData.content_template }),
      updated_at: new Date(),
    };

    mockPrompts[promptIndex] = updatedPrompt;
    return updatedPrompt;
  }

  // Original database implementation
  const db = getDb();

  try {
    // Check if prompt exists
    const existingPrompt = await getPromptById(id);
    if (!existingPrompt) {
      return null;
    }

    // If key is being changed, check if the new key already exists
    if (promptData.key && promptData.key !== existingPrompt.key) {
      const promptWithSameKey = await getPromptByKey(promptData.key);
      if (promptWithSameKey) {
        throw new Error(`Prompt with key '${promptData.key}' already exists`);
      }
    }

    const updateFields = [];
    const queryParams = [];
    let paramCounter = 1;

    if (promptData.key !== undefined) {
      updateFields.push(`key = $${paramCounter++}`);
      queryParams.push(promptData.key);
    }

    if (promptData.description !== undefined) {
      updateFields.push(`description = $${paramCounter++}`);
      queryParams.push(promptData.description);
    }

    if (promptData.llm_model_compatibility !== undefined) {
      updateFields.push(`llm_model_compatibility = $${paramCounter++}`);
      queryParams.push(JSON.stringify(promptData.llm_model_compatibility));
    }

    if (promptData.version !== undefined) {
      updateFields.push(`version = $${paramCounter++}`);
      queryParams.push(promptData.version);
    }

    if (promptData.content_template !== undefined) {
      updateFields.push(`content_template = $${paramCounter++}`);
      queryParams.push(promptData.content_template);
    }

    // Add updated_at
    updateFields.push(`updated_at = $${paramCounter++}`);
    queryParams.push(new Date());

    // Add id for WHERE clause
    queryParams.push(id);

    if (updateFields.length === 0) {
      return existingPrompt; // Nothing to update
    }

    const result = await db.query(
      `
      UPDATE prompts 
      SET ${updateFields.join(", ")} 
      WHERE id = $${paramCounter} AND is_delete = false
      RETURNING 
        id, key, description, llm_model_compatibility, 
        version, content_template, created_at, updated_at
      `,
      queryParams
    );

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error in updatePrompt:", error);
    throw error;
  }
}

/**
 * Deletes a prompt (soft delete)
 */
export async function deletePrompt(id: number) {
  // TEMPORARY: Use mock data for testing
  if (USE_MOCK_DATA) {

    const promptIndex = mockPrompts.findIndex((p) => p.id === id);
    if (promptIndex === -1) {
      return false;
    }

    mockPrompts[promptIndex] = {
      ...mockPrompts[promptIndex],
      is_delete: true,
      updated_at: new Date(),
    };

    return true;
  }

  // Original database implementation
  const db = getDb();

  try {
    // Check if prompt exists
    const existingPrompt = await getPromptById(id);
    if (!existingPrompt) {
      return false;
    }

    await db.query("UPDATE prompts SET is_delete = true, updated_at = $1 WHERE id = $2", [new Date(), id]);

    return true;
  } catch (error) {
    console.error("Error in deletePrompt:", error);
    throw error;
  }
}
