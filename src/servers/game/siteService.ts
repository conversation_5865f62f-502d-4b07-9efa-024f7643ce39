import { getDb } from "~/libs/db/db";
import { Site, SiteConfig } from "~/types/site";
import { generateNewUID } from "~/utils/uidUtil";

// Get sites with pagination and filtering
export async function getSites(page: number = 1, pageSize: number = 10, name?: string): Promise<{ sites: Site[]; total: number }> {
  const db = getDb();
  const offset = (page - 1) * pageSize;

  try {
    let queryParams = [];
    let queryConditions = [];  // Removed is_delete condition

    // Add name filter if provided
    if (name) {
      queryParams.push(`%${name}%`);
      queryConditions.push(`(name ILIKE $${queryParams.length} OR domain ILIKE $${queryParams.length})`);
    }

    // Build the WHERE clause
    const whereClause = queryConditions.length > 0 ? `WHERE ${queryConditions.join(" AND ")}` : "";

    // Count total records
    const countQuery = `
      SELECT COUNT(*) as total
      FROM sites
      ${whereClause}
    `;

    const countResult = await db.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    queryParams.push(pageSize);
    queryParams.push(offset);

    const sitesQuery = `
      SELECT *
      FROM sites
      ${whereClause}
      ORDER BY id DESC
      LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length}
    `;

    const sitesResult = await db.query(sitesQuery, queryParams);

    return {
      sites: sitesResult.rows,
      total,
    };
  } catch (error) {
    //console.error("Error fetching sites:", error);
    throw error;
  }
}

// Get site by ID - 保留但不推荐使用
export async function getSiteById(id: number): Promise<Site | null> {
  const db = getDb();

  try {
    // Remove the is_delete condition
    const result = await db.query(`SELECT * FROM sites WHERE id = $1`, [id]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    //console.error("Error fetching site by ID:", error);
    throw error;
  }
}

// Get site by UID - 推荐使用
export async function getSiteByUid(uid: string): Promise<Site | null> {
  const db = getDb();

  try {
    // Remove the is_delete condition since it might not exist in the table
    const result = await db.query(`SELECT * FROM sites WHERE uid = $1`, [uid]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    //console.error("Error fetching site by UID:", error);
    throw error;
  }
}

// Create a new site
export async function createSite(siteData: {
  name: string;
  domain: string;
  path_prefix?: string;
  config?: SiteConfig;
  default_template_key?: string;
  default_llm_model?: string;
  default_llm_prompt_key?: string;
}): Promise<Site> {
  const db = getDb();

  try {
    // 生成唯一的uid
    const uid = generateNewUID();
    
    const result = await db.query(
      `INSERT INTO sites(
        uid, name, domain, config, default_template_key, default_llm_model, default_llm_prompt_key
      ) VALUES($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        uid,
        siteData.name,
        siteData.domain,
        siteData.config ? JSON.stringify(siteData.config) : null,
        siteData.default_template_key || null,
        siteData.default_llm_model || null,
        siteData.default_llm_prompt_key || null,
      ]
    );

    return result.rows[0];
  } catch (error) {
    //console.error("Error creating site:", error);
    throw error;
  }
}

// Update a site by ID - 保留但不推荐使用
export async function updateSite(
  id: number,
  siteData: {
    name?: string;
    domain?: string;
    config?: SiteConfig | null;
    default_template_key?: string | null;
    default_llm_model?: string | null;
    default_llm_prompt_key?: string | null;
  }
): Promise<Site | null> {
  const db = getDb();

  try {
    // First check if site exists
    const existingSite = await getSiteById(id);

    if (!existingSite) {
      return null;
    }

    // Build the update fields
    const updates = [];
    const values = [];

    if (siteData.name !== undefined) {
      updates.push(`name = $${values.length + 1}`);
      values.push(siteData.name);
    }

    if (siteData.domain !== undefined) {
      updates.push(`domain = $${values.length + 1}`);
      values.push(siteData.domain);
    }

    if (siteData.config !== undefined) {
      updates.push(`config = $${values.length + 1}`);
      values.push(siteData.config ? JSON.stringify(siteData.config) : null);
    }

    if (siteData.default_template_key !== undefined) {
      updates.push(`default_template_key = $${values.length + 1}`);
      values.push(siteData.default_template_key);
    }

    if (siteData.default_llm_model !== undefined) {
      updates.push(`default_llm_model = $${values.length + 1}`);
      values.push(siteData.default_llm_model);
    }

    if (siteData.default_llm_prompt_key !== undefined) {
      updates.push(`default_llm_prompt_key = $${values.length + 1}`);
      values.push(siteData.default_llm_prompt_key);
    }

    // Always update the updated_at timestamp
    updates.push(`updated_at = CURRENT_TIMESTAMP`);

    // If no updates, return the existing site
    if (updates.length === 0) {
      return existingSite;
    }

    // Add the ID as the last parameter
    values.push(id);

    const result = await db.query(`UPDATE sites SET ${updates.join(", ")} WHERE id = $${values.length} RETURNING *`, values);

    return result.rows[0];
  } catch (error) {
    //console.error("Error updating site:", error);
    throw error;
  }
}

// Update a site by UID - 推荐使用
export async function updateSiteByUid(
  uid: string,
  siteData: {
    name?: string;
    domain?: string;
    config?: SiteConfig | null;
    default_template_key?: string | null;
    default_llm_model?: string | null;
    default_llm_prompt_key?: string | null;
  }
): Promise<Site | null> {
  const db = getDb();

  try {
    // First check if site exists
    const existingSite = await getSiteByUid(uid);

    if (!existingSite) {
      return null;
    }

    // Build the update fields
    const updates = [];
    const values = [];

    if (siteData.name !== undefined) {
      updates.push(`name = $${values.length + 1}`);
      values.push(siteData.name);
    }

    if (siteData.domain !== undefined) {
      updates.push(`domain = $${values.length + 1}`);
      values.push(siteData.domain);
    }

    if (siteData.config !== undefined) {
      updates.push(`config = $${values.length + 1}`);
      values.push(siteData.config ? JSON.stringify(siteData.config) : null);
    }

    if (siteData.default_template_key !== undefined) {
      updates.push(`default_template_key = $${values.length + 1}`);
      values.push(siteData.default_template_key);
    }

    if (siteData.default_llm_model !== undefined) {
      updates.push(`default_llm_model = $${values.length + 1}`);
      values.push(siteData.default_llm_model);
    }

    if (siteData.default_llm_prompt_key !== undefined) {
      updates.push(`default_llm_prompt_key = $${values.length + 1}`);
      values.push(siteData.default_llm_prompt_key);
    }

    // Always update the updated_at timestamp
    updates.push(`updated_at = CURRENT_TIMESTAMP`);

    // If no updates, return the existing site
    if (updates.length === 0) {
      return existingSite;
    }

    // Add the UID as the last parameter
    values.push(uid);

    const result = await db.query(`UPDATE sites SET ${updates.join(", ")} WHERE uid = $${values.length} RETURNING *`, values);

    return result.rows[0];
  } catch (error) {
    //console.error("Error updating site by UID:", error);
    throw error;
  }
}

// Delete a site by ID (soft delete) - 保留但不推荐使用
export async function deleteSite(id: number): Promise<boolean> {
  const db = getDb();

  try {
    // First check if site exists
    const existingSite = await getSiteById(id);

    if (!existingSite) {
      return false;
    }

    // Use a DELETE query instead of setting is_delete flag
    await db.query(`DELETE FROM sites WHERE id = $1`, [id]);

    return true;
  } catch (error) {
    //console.error("Error deleting site:", error);
    throw error;
  }
}

// Delete a site by UID (soft delete) - 推荐使用
export async function deleteSiteByUid(uid: string): Promise<boolean> {
  const db = getDb();

  try {
    // First check if site exists
    const existingSite = await getSiteByUid(uid);

    if (!existingSite) {
      return false;
    }

    // Use a DELETE query instead of setting is_delete flag
    await db.query(`DELETE FROM sites WHERE uid = $1`, [uid]);

    return true;
  } catch (error) {
    //console.error("Error deleting site by UID:", error);
    throw error;
  }
}

// Get site by domain
export async function getSiteByDomain(domain: string): Promise<Site | null> {
  const db = getDb();

  try {
    const result = await db.query(
      `SELECT * FROM sites WHERE domain = $1 LIMIT 1`,
      [domain]
    );

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as Site;
  } catch (error) {
    console.error("Error getting site by domain:", error);
    throw error;
  }
}
