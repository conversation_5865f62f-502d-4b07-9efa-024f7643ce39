import { getDb } from "~/libs/db/db";
import { getUserByServerSession } from "../common/user";
import { generateInviteCode } from "~/utils/uidUtil";

const db = getDb();

// 获取已存在的邀请码，根据当前用户信息获取
export const getExistCode = async () => {
  const user_info = await getUserByServerSession();
  if (!user_info.user_id) {
    return "";
  }
  const user_id = user_info.user_id;
  const { rows: inviteCodeList } = await db.query(
    `
    select * from invite_code
      where user_id = $1 and is_delete = false
      order by created_at desc limit 1
  `,
    [user_id]
  );

  if (inviteCodeList.length === 0) {
    // 如果没有邀请码，则生成一个新的邀请码
    return await generateNewInviteCode();
  }
  // 如果邀请码已存在，则返回已存在的邀请码
  return inviteCodeList[0]?.code || "";
};

// 生成新的邀请码，根据当前用户信息生成
export const generateNewInviteCode = async () => {
  const user_info = await getUserByServerSession();
  if (!user_info.user_id) {
    return "";
  }
  const user_id = user_info.user_id;

  // 将已有邀请码标记为删除
  await db.query(
    `
    update invite_code
      set updated_at = now(), is_delete = true
      where user_id = $1
  `,
    [user_id]
  );

  // 生成新的邀请码
  const code = generateInviteCode();
  await db.query(
    `
    insert into invite_code (user_id, code)
      values ($1, $2)
  `,
    [user_id, code]
  );

  return code;
};

// 查询已登录的用户是否是当前邀请码的邀请者
export const handleIsInvited = async (searchCode: string, user_id: string) => {
  const { rows: inviteCodeList } = await db.query(
    `
    select * from invite_record
      where invite_user_id = $1 and is_delete = false
  `,
    [user_id]
  );
  // 状态是 2 表示是被邀请者 3 表示是被他人邀请者 1 表示是注册用户
  if (inviteCodeList.length > 0) {
    return searchCode === inviteCodeList[0].invite_code
      ? {
          status: 2,
          invite_code: inviteCodeList[0].invite_code,
        }
      : {
          status: 3,
          invite_code: inviteCodeList[0].invite_code,
        };
  }
  return {
    status: 1,
    invite_code: "",
  };
};

// 查询邀请码对应的邀请人名字
export const getInviterName = async (inviteCode: string) => {
  // 首先从invite_code表查询出邀请人的id
  const { rows: inviteCodeInfo } = await db.query(
    `
    SELECT user_id FROM invite_code
    WHERE code = $1
    `,
    [inviteCode]
  );

  if (inviteCodeInfo.length === 0) {
    return "";
  }

  const inviterId = inviteCodeInfo[0].user_id;

  // 然后查询邀请人的名字
  const { rows: inviterInfo } = await db.query(
    `
    SELECT name FROM user_info
    WHERE user_id = $1
    `,
    [inviterId]
  );

  return inviterInfo[0]?.name || "";
};
