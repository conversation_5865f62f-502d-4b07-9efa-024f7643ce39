import { getPageBySlug, Page } from "~/lib/api/pages";
import { getRecommendedGamesForPage, getSiteRecommendationZones, RecommendedGame, RecommendationZoneConfig } from "~/lib/api/recommendations"; // Import recommendation fetching functions and types
import { getSiteByUid } from "~/servers/game/siteService";
import { Site } from "~/types/site";

// Define the structure for homepage data, similar to GamePageData for now
export interface HomepageData {
  page: Page; // The basic homepage page data
  recommendedGames: RecommendedGame[]; // Include recommended games
  recommendationZones: RecommendationZoneConfig[]; // Include recommendation zones
  site: Site; // 添加站点信息
}

export async function getHomepageData(
  domain: string,
  locale: string
): Promise<HomepageData | null> {
  try {
    // Use getPageBySlug to fetch the homepage data
    const homepage = await getPageBySlug('' /* slug for homepage is empty string */, domain, true /* isHomepage = true */);

    if (!homepage) {
      console.error(`[getHomepageData] Homepage not found for domain: ${domain}`);
      return null;
    }

    // Fetch recommended games and zones, similar to getGamePageData
    const recommendedGames = await getRecommendedGamesForPage(homepage.site_uid, homepage.uid);
    const recommendationZones = await getSiteRecommendationZones(homepage.site_uid);
    
    // Fetch site data using site_uid
    const site = await getSiteByUid(homepage.site_uid);

    if (!site) {
      console.error(`[getHomepageData] Site data not found for site_uid: ${homepage.site_uid}`);
      return null; // 或者根据需求返回部分数据/抛出错误
    }

    // Return the homepage data along with recommended games, zones, and site info
    return {
      page: homepage,
      recommendedGames,
      recommendationZones,
      site, // 添加站点信息
    };
  } catch (error) {
    console.error('[getHomepageData] Error fetching homepage data:', error);
    return null;
  }
} 