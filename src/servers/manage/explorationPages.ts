import { getDb } from "~/libs/db/db";
import { generateNewUID } from "~/utils/uidUtil";

const db = getDb();

export interface ExplorationPageData {
  uid?: string;
  site_uid: string;
  slug: string;
  exploration_sort_by: 'newest' | 'popular';
  title: string;
  description: string;
  h1: string;
  introduction: string;
  template_key?: string;
}

// 安全的JSON解析函数
function safeJsonParse(jsonString: any, fallback: any = {}) {
  try {
    // 如果已经是对象，直接返回
    if (typeof jsonString === 'object' && jsonString !== null) {
      return jsonString;
    }
    
    // 如果是字符串，尝试解析
    if (typeof jsonString === 'string') {
      return JSON.parse(jsonString);
    }
    
    // 其他情况返回fallback
    return fallback;
  } catch (error) {
    console.error('[safeJsonParse] JSON解析失败:', {
      input: jsonString,
      inputType: typeof jsonString,
      error: error.message
    });
    return fallback;
  }
}

export async function getExplorationPagesBySiteUid(siteUid: string) {
  try { 
    const { rows } = await db.query(
      `SELECT uid, site_uid, slug, exploration_sort_by, json_content, template_key, created_at, updated_at
       FROM pages 
       WHERE site_uid = $1 AND is_exploration_page = true AND status=1
       ORDER BY created_at DESC`,
      [siteUid]
    );

    return rows.map(row => {
      const parsedContent = safeJsonParse(row.json_content, {});
      
      return {
        uid: row.uid,
        site_uid: row.site_uid,
        slug: row.slug,
        exploration_sort_by: row.exploration_sort_by,
        template_key: row.template_key,
        created_at: row.created_at,
        updated_at: row.updated_at,
        title: parsedContent.title || '',
        description: parsedContent.description || '',
        h1: parsedContent.h1 || '',
        introduction: parsedContent.introduction || ''
      };
    });
  } catch (error) {
    console.error('[getExplorationPagesBySiteUid] 查询失败:', {
      siteUid,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

export async function createExplorationPage(data: ExplorationPageData) {
  try {
    const uid = generateNewUID();
    
    // 模板键优先级：用户指定 > 站点默认 > fallback
    let templateKey = data.template_key; // 优先使用用户指定的模板
    
    if (!templateKey) {
      // 如果用户未指定，查询站点的默认模板配置
      templateKey = `${data.exploration_sort_by}`; // 默认fallback值
      
      try {      
        const { rows: siteRows } = await db.query(
          `SELECT default_template_key FROM sites WHERE uid = $1`,
          [data.site_uid]
        );
        
        if (siteRows.length > 0 && siteRows[0].default_template_key) {
          templateKey = siteRows[0].default_template_key;
        }
      } catch (siteQueryError) {
        console.error('[createExplorationPage] 查询站点模板失败，使用fallback:', {
          site_uid: data.site_uid,
          error: siteQueryError.message,
          fallbackTemplate: templateKey
        });
        // 继续使用fallback值，不中断页面创建流程
      }
    }
    
    const jsonContent = {
      title: data.title,
      description: data.description,
      h1: data.h1,
      introduction: data.introduction
    };

    // 查询站点domain
    const { rows: siteRows } = await db.query(
      `SELECT domain, default_template_key FROM sites WHERE uid = $1`,
      [data.site_uid]
    );

    const domain = siteRows[0].domain;

    const { rows } = await db.query(
      `INSERT INTO pages (
        uid, site_uid, domain, slug, exploration_sort_by, 
        json_content, template_key, is_exploration_page, 
        status, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, true, 1, NOW(), NOW())
      RETURNING *`,
      [uid, data.site_uid, domain, data.slug, data.exploration_sort_by, JSON.stringify(jsonContent), templateKey]
    );

    const createdPage = rows[0];
    const parsedContent = safeJsonParse(createdPage.json_content, {});

    return {
      uid: createdPage.uid,
      site_uid: createdPage.site_uid,
      slug: createdPage.slug,
      exploration_sort_by: createdPage.exploration_sort_by,
      template_key: createdPage.template_key,
      created_at: createdPage.created_at,
      updated_at: createdPage.updated_at,
      ...parsedContent
    };
  } catch (error) {
    console.error('[createExplorationPage] 创建失败:', {
      data,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

export async function updateExplorationPage(uid: string, data: Partial<ExplorationPageData>) {
  try {
    const jsonContent = {
      title: data.title,
      description: data.description,
      h1: data.h1,
      introduction: data.introduction
    };

    // 模板键处理逻辑：用户指定 > 基于排序方式查询站点默认 > 不更新
    let templateKey = undefined;
    
    if (data.hasOwnProperty('template_key')) {
      // 用户明确指定了 template_key
      if (data.template_key) {
        templateKey = data.template_key;
      } else if (data.exploration_sort_by && data.site_uid) {
        // 用户清空了 template_key，但更新了排序方式，查询站点默认
        templateKey = await getTemplateKeyForSite(data.site_uid, data.exploration_sort_by);
      }
    } else if (data.exploration_sort_by && data.site_uid) {
      // 用户未指定 template_key，但更新了排序方式，查询站点默认
      templateKey = await getTemplateKeyForSite(data.site_uid, data.exploration_sort_by);
    }

    const updateFields = [];
    const updateValues = [];
    let paramIndex = 1;

    if (data.slug) {
      updateFields.push(`slug = $${paramIndex++}`);
      updateValues.push(data.slug);
    }

    if (data.exploration_sort_by) {
      updateFields.push(`exploration_sort_by = $${paramIndex++}`);
      updateValues.push(data.exploration_sort_by);
    }

    if (templateKey !== undefined) {
      updateFields.push(`template_key = $${paramIndex++}`);
      updateValues.push(templateKey);
    }

    updateFields.push(`json_content = $${paramIndex++}`);
    updateValues.push(JSON.stringify(jsonContent));

    updateFields.push(`updated_at = NOW()`);
    updateValues.push(uid);

    await db.query(
      `UPDATE pages SET ${updateFields.join(', ')} WHERE uid = $${paramIndex}`,
      updateValues
    );
  } catch (error) {
    console.error('[updateExplorationPage] 更新失败:', {
      uid,
      data,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

export async function deleteExplorationPage(uid: string) {
  try {   
    await db.query(
      'UPDATE pages SET status = 0, updated_at = NOW() WHERE uid = $1 AND is_exploration_page = true', 
      [uid]
    );

  } catch (error) {
    console.error('[deleteExplorationPage] 删除失败:', {
      uid,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
}

// 辅助函数：获取站点的模板键
async function getTemplateKeyForSite(siteUid: string, sortBy: string): Promise<string> {
  const fallbackTemplate = `${sortBy}`;
  
  try {    
    const { rows: siteRows } = await db.query(
      `SELECT default_template_key FROM sites WHERE uid = $1`,
      [siteUid]
    );
    
    if (siteRows.length > 0 && siteRows[0].default_template_key) {
      const templateKey = siteRows[0].default_template_key;
      return templateKey;
    }
  } catch (siteQueryError) {
    console.error('[getTemplateKeyForSite] 查询站点模板失败，使用fallback:', {
      site_uid: siteUid,
      error: siteQueryError.message,
      fallbackTemplate
    });
    return fallbackTemplate;
  }
}
