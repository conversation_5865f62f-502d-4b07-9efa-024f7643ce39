import { getDb } from "~/libs/db/db";
import { generateNewShortUniqueUID } from "~/utils/uidUtil";
import { getUserByServerSession } from "~/servers/common/user";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { getGameByUid as getGameByUidService, updateGameByUid, deleteGameByUid as deleteGameByUidBase } from "~/servers/game/gameService";

const db = getDb();

export const saveGame = async (gameData: {
  uid?: string;
  keyword: string;
  slug?: string;
  name?: string;
  iframe_url?: string;
  reference_data?: string;
  tags?: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Process tags
  const processedTags = gameData.tags
    ? gameData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    : [];

  // slug 唯一性校验
  if (gameData.slug) {
    let checkSlugQuery = '';
    let checkSlugParams: any[] = [];
    if (gameData.uid) {
      checkSlugQuery = 'SELECT uid FROM games WHERE slug = $1 AND uid != $2 LIMIT 1';
      checkSlugParams = [gameData.slug, gameData.uid];
    } else {
      checkSlugQuery = 'SELECT uid FROM games WHERE slug = $1 LIMIT 1';
      checkSlugParams = [gameData.slug];
    }
    const { rows: slugRows } = await db.query(checkSlugQuery, checkSlugParams);
    if (slugRows.length > 0) {
      return { code: 409, message: "slug已存在，请更换" };
    }
  }

  if (gameData.uid) {
    // Update existing game by uid
    const updateQuery = `
      UPDATE games
      SET keyword = $1, slug = $2, iframe_url = $3, reference_data = $4, tags = $5, updated_at = NOW()
      WHERE uid = $6
      RETURNING *;
    `;
    const { rows } = await db.query(updateQuery, [
      gameData.keyword,
      gameData.slug || null,
      gameData.iframe_url || null,
      gameData.reference_data || null,
      processedTags.length > 0 ? JSON.stringify(processedTags) : null,
      gameData.uid
    ]);

    if (rows.length === 0) {
      return { code: 404, message: "Game not found" };
    }

    return { code: 200, data: rows[0], message: "Game updated successfully" };
  } else {
    // Check if keyword already exists
    const checkKeywordQuery = `
      SELECT * FROM games WHERE keyword = $1
    `;
    const { rows: keywordRows } = await db.query(checkKeywordQuery, [gameData.keyword]);
    if (keywordRows.length > 0) {
      return { code: 400, message: "Game keyword already exists" };
    }

    // Generate a unique UID for the new game
    const uid = generateNewShortUniqueUID();

    // Create new game
    const insertQuery = `
      INSERT INTO games (keyword, slug, iframe_url, reference_data, tags, uid)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *;
    `;
    const { rows } = await db.query(insertQuery, [
      gameData.keyword,
      gameData.slug || null,
      gameData.iframe_url || null,
      gameData.reference_data || null,
      processedTags.length > 0 ? JSON.stringify(processedTags) : null,
      uid // 使用生成的唯一ID
    ]);

    return { code: 200, data: rows[0], message: "Game created successfully" };
  }
};

// New function to save game by UID
export const saveGameByUid = async (gameData: {
  uid: string;
  keyword: string;
  slug?: string;
  name?: string;
  iframe_url?: string;
  reference_data?: string;
  tags?: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Process tags
  const processedTags = gameData.tags
    ? gameData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    : [];

  // slug 唯一性校验
  if (gameData.slug) {
    const { rows: slugRows } = await db.query(
      'SELECT uid FROM games WHERE slug = $1 AND uid != $2 LIMIT 1',
      [gameData.slug, gameData.uid]
    );
    if (slugRows.length > 0) {
      return { code: 409, message: "slug已存在，请更换" };
    }
  }

  try {
    // Check if game exists
    const existingGame = await getGameByUidService(gameData.uid);
    
    if (!existingGame) {
      return { code: 404, message: "Game not found" };
    }
    
    // Update existing game by UID
    const updateQuery = `
      UPDATE games
      SET keyword = $1, slug = $2, iframe_url = $3, reference_data = $4, tags = $5, updated_at = NOW()
      WHERE uid = $6
      RETURNING *;
    `;
    const { rows } = await db.query(updateQuery, [
      gameData.keyword,
      gameData.slug || null,
      gameData.iframe_url || null,
      gameData.reference_data || null,
      processedTags.length > 0 ? JSON.stringify(processedTags) : null,
      gameData.uid
    ]);
    if (rows.length === 0) {
      return { code: 404, message: "Game not found" };
    }
    return { code: 200, data: rows[0], message: "Game updated successfully" };
  } catch (error) {
    console.error("Error in saveGameByUid:", error);
    return { code: 500, message: "Error updating game" };
  }
};

// Delete a game (actual delete)
export const deleteGame = async (id: number) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Perform real delete operation
  const deleteQuery = `
    DELETE FROM games WHERE id = $1
    RETURNING id;
  `;
  const { rows } = await db.query(deleteQuery, [id]);
  
  if (rows.length === 0) {
    return { code: 404, message: "Game not found" };
  }
  
  return { code: 200, message: "Game deleted successfully" };
};

// New function to delete game by UID
export const deleteGameByUid = async (uid: string) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  try {
    const result = await deleteGameByUidBase(uid);
    
    if (!result) {
      return { code: 404, message: "Game not found" };
    }
    
    return { code: 200, message: "Game deleted successfully" };
  } catch (error) {
    console.error("Error in deleteGameByUid:", error);
    return { code: 500, message: "Error deleting game" };
  }
};

// Get game by UID
export const getGameByUid = async (uid: string) => {
  const getQuery = `
    SELECT * FROM games WHERE uid = $1
  `;
  const { rows } = await db.query(getQuery, [uid]);
  
  if (rows.length === 0) {
    return { code: 404, message: "Game not found" };
  }
  
  return { code: 200, data: rows[0], message: "Game retrieved successfully" };
};

// Get all games (with pagination)
export const getGames = async (params: { pageSize?: number, skipSize?: number }) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403, 
      data: {
        resultList: [],
        totalPage: 0,
        countTotal: 0
      },
      message: "Unauthorized: Admin access required"
    };
  }

  const pageSize = params.pageSize || 10;
  const skipSize = params.skipSize || 0;

  const getQuery = `
    SELECT * FROM games 
    ORDER BY created_at DESC
    LIMIT $1 OFFSET $2
  `;
  const { rows } = await db.query(getQuery, [pageSize, skipSize]);
  
  // Get total count for pagination
  const countQuery = `
    SELECT COUNT(*) FROM games
  `;
  const { rows: countRows } = await db.query(countQuery);
  const total = parseInt(countRows[0].count);
  
  return { 
    code: 200, 
    data: {
      resultList: rows,
      totalPage: Math.ceil(total / pageSize),
      countTotal: total
    }, 
    message: "Games retrieved successfully" 
  };
}; 