import { getDb } from "~/libs/db/db";
import { getPageBySlug } from "~/lib/api/pages";
import { getRecommendedGamesForPage, getSiteRecommendationZones } from "~/lib/api/recommendations";
import { getSiteByUid } from "~/servers/game/siteService";
import { SiteData, Site } from "~/types/site";

const db = getDb();

export async function getGamePageData(slug: string, domain: string): Promise<SiteData | null> {
  try {
    // 1. Get page data
    const page = await getPageBySlug(slug, domain);
    if (!page) {
      console.error(`[getGamePageData] Page not found for slug: ${slug}, domain: ${domain}`);
      return null;
    }

    // 2. Get recommended games and zones
    const recommendedGames = await getRecommendedGamesForPage(page.site_uid, page.uid);
    const recommendationZones = await getSiteRecommendationZones(page.site_uid);

    // 3. Get site data
    const gameSite = await getSiteByUid(page.site_uid);
    if (!gameSite) {
      console.error(`[getGamePageData] Site data not found for site_uid: ${page.site_uid}`);
      return null;
    }

    // 4. Transform data to match SiteData type
    const site: Site = {
      id: gameSite.id,
      uid: gameSite.uid || '',
      name: gameSite.name,
      domain: gameSite.domain,
      config: {
        ...gameSite.config,
        navigation_links: gameSite.config?.navigation_links || []
      },
      default_template_key: gameSite.default_template_key,
      default_llm_model: gameSite.default_llm_model,
      default_llm_prompt_key: gameSite.default_llm_prompt_key,
      created_at: gameSite.created_at,
      updated_at: gameSite.updated_at
    };

    const siteData: SiteData = {
      site,
      page,
      recommendedGames,
      recommendationZones
    };

    return siteData;
  } catch (error) {
    console.error('[getGamePageData] Error:', error);
    return null;
  }
} 