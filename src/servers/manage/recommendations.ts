import { getDb } from "~/libs/db/db";
import { generateNewShortUniqueUID } from "~/utils/uidUtil"; // Assuming uidUtil exists

const db = getDb();

interface Zone {
  id: string;
  name: string;
}

interface RecommendationData {
  siteUid: string;
  zone: string;
  title: string;
  description?: string;
  jump_url: string;
  sort_order?: number;
  cover_img_url?: string;
}

// 获取推荐分区（从 sites.config 中读取 recommendationZones）
export const getRecommendationZonesBySiteUid = async (siteUid: string) => {
  // 1. 查询站点的 config 字段
  const getSiteConfigQuery = `
    SELECT config FROM sites WHERE uid = $1
  `;
  const { rows: siteRows } = await db.query(getSiteConfigQuery, [siteUid]);

  if (siteRows.length === 0) {
    // 如果站点不存在，返回空数组或抛出错误，这里返回空数组
    console.warn(`Site with UID ${siteUid} not found when fetching zones.`);
    return [];
  }

  // 2. 从 config 中提取 recommendationZones 数组
  const siteConfig = siteRows[0].config || {};
  const recommendationZones = siteConfig.recommendationZones || [];

  // 3. 返回 recommendationZones
  // 确保返回的格式是 { id, name }[]，虽然 config 里已经是这个格式了
  return recommendationZones.map((zone: any) => ({
    id: zone.id,
    name: zone.name
  }));
};

// 更新推荐分区（更新 sites.config 中的 recommendationZones）
export const updateRecommendationZonesBySiteUid = async (siteUid: string, zones: Zone[]) => {
  // 1. 获取当前站点配置
  const getSiteQuery = `
    SELECT config FROM sites WHERE uid = $1
  `;
  const { rows: siteRows } = await db.query(getSiteQuery, [siteUid]);
  
  if (siteRows.length === 0) {
    throw new Error(`Site with UID ${siteUid} not found.`);
  }

  // 2. 更新配置中的 recommendationZones
  const currentConfig = siteRows[0].config || {};
  const updatedConfig = {
    ...currentConfig,
    recommendationZones: zones
  };

  // 3. 更新站点配置
  const updateQuery = `
    UPDATE sites 
    SET config = $1, updated_at = NOW()
    WHERE uid = $2
    RETURNING *
  `;
  const { rows: updatedRows } = await db.query(updateQuery, [updatedConfig, siteUid]);

  return updatedRows[0];
};

// 获取推荐内容列表
export const listRecommendations = async (siteUid: string, zone?: string, page: number = 1, pageSize: number = 10) => {
  let query = `
    SELECT *
    FROM recommended_games
    WHERE site_uid = $1
  `;
  const params = [siteUid];
  let paramIndex = 2;

  if (zone && zone !== 'all') {
    query += ` AND recommendation_zone = $${paramIndex}`;
    params.push(zone);
    paramIndex++;
  }

  // Add pagination
  const offset = (page - 1) * pageSize;
  query += ` ORDER BY sort_order ASC, created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`; // Sort by sort_order then created_at
  params.push(pageSize.toString(), offset.toString());

  const { rows: items } = await db.query(query, params);

  // Get total count (without pagination)
  let countQuery = `
    SELECT COUNT(*)
    FROM recommended_games
    WHERE site_uid = $1
  `;
  const countParams = [siteUid];
  let countParamIndex = 2;

  if (zone && zone !== 'all') {
    countQuery += ` AND recommendation_zone = $${countParamIndex}`;
    countParams.push(zone);
    countParamIndex++;
  }

  const { rows: countRows } = await db.query(countQuery, countParams);
  const total = parseInt(countRows[0].count, 10);

  return { items, total };
};

// 创建新的推荐内容项
export const createRecommendation = async (data: RecommendationData) => {
  const uid = generateNewShortUniqueUID(); // Generate unique UID
  
  const query = `
    INSERT INTO recommended_games (
      uid,
      site_uid,
      recommendation_zone,
      title,
      description,
      jump_url,
      sort_order,
      cover_img_url
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    RETURNING *
  `;
  const params = [
    uid,
    data.siteUid,
    data.zone,
    data.title,
    data.description || null,
    data.jump_url,
    data.sort_order || 0,
    data.cover_img_url || null
  ];

  try {
    const { rows } = await db.query(query, params);
    return rows[0];
  } catch (error) {
    console.error('Error creating recommendation:', error);
    throw error;
  }
};

// 更新推荐内容项
export const updateRecommendation = async (id: number, data: Partial<RecommendationData>) => {
  const updates = [];
  const params = [];
  let paramIndex = 1;

  if (data.siteUid !== undefined) { updates.push(`site_uid = $${paramIndex++}`); params.push(data.siteUid); }
  if (data.zone !== undefined) { updates.push(`recommendation_zone = $${paramIndex++}`); params.push(data.zone); }
  if (data.title !== undefined) { updates.push(`title = $${paramIndex++}`); params.push(data.title); }
  if (data.description !== undefined) { updates.push(`description = $${paramIndex++}`); params.push(data.description || null); }
  if (data.jump_url !== undefined) { updates.push(`jump_url = $${paramIndex++}`); params.push(data.jump_url); }
  if (data.sort_order !== undefined) { updates.push(`sort_order = $${paramIndex++}`); params.push(data.sort_order || 0); }
  if (data.cover_img_url !== undefined) { updates.push(`cover_img_url = $${paramIndex++}`); params.push(data.cover_img_url || null); }

  // Always update updated_at
  updates.push(`updated_at = NOW()`);

  if (updates.length === 1) { // Only updated_at was added
    // Fetch and return the existing record if no data fields were provided
    const existingQuery = `SELECT * FROM recommended_games WHERE id = $1`;
    const { rows: existingRows } = await db.query(existingQuery, [id]);
    return existingRows.length > 0 ? existingRows[0] : null;
  }

  const query = `
    UPDATE recommended_games
    SET ${updates.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING *
  `;
  params.push(id);

  try {
    const { rows } = await db.query(query, params);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error(`Error updating recommendation with id ${id}:`, error);
    throw error;
  }
};

// 获取单个推荐内容项
export const getRecommendation = async (id: number) => {
  const query = `
    SELECT *
    FROM recommended_games
    WHERE id = $1
  `;
  const params = [id];

  try {
    const { rows } = await db.query(query, params);
    return rows.length > 0 ? rows[0] : null; // Return the first row or null if not found
  } catch (error) {
    console.error(`Error getting recommendation with id ${id}:`, error);
    throw error; // Re-throw to be caught by the API route
  }
};
