import { manageUserSize } from "~/configs/globalConfig";
import { getDb } from "~/libs/db/db";
import { formatDateToUTC8 } from "~/utils/handleData";
import { getDeviceType } from "~/utils/handleData";

const db = getDb();
export const getUserList = async (json: any) => {
  const positionPage = json.positionPage;
  const email = json.email;
  const pageSize = manageUserSize;
  const skipSize = pageSize * (Number(positionPage) - 1);

  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    // 有注册时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
      select ui.*, ua.stripe_customer_id, ua.available_times, ua.download_times from user_info ui
      left join user_available ua on ua.user_id = ui.user_id
      where ui.is_delete=false $$sqlQueryAppend$$
      order by ui.created_at desc
      limit $1 offset $2;
    `;
  let sqlParams = [pageSize, skipSize];
  let sqlQueryAppend = "";
  // 有注册时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }

  // 有邮箱
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email=$${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);
  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: userList } = await db.query(sqlQuery, sqlParams);

  if (userList.length > 0) {
    for (let i = 0; i < userList.length; i++) {
      const user = userList[i];
      const user_agent = user.user_agent;
      if (user_agent) {
        user.device = getDeviceType(user_agent);
      } else {
        user.device = "";
      }
      // 生成了多少条数据
      const { rows: dataList } = await db.query(
        `
                select count(1)
                from works
                where user_id=$1 and is_delete=false
            `,
        [user.user_id]
      );
      user.data_count = dataList[0].count;
    }
  }

  // 查询出页数信息
  const pageDesc = await getTotalPage(json);
  return {
    resultList: userList,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

export const getTotalPage = async (json) => {
  const pageSize = manageUserSize;
  const email = json.email;

  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
      select count(1)
      from user_info ui
      where ui.is_delete=false $$sqlQueryAppend$$;
    `;
  let sqlParams = [];
  let sqlQueryAppend = "";
  // 有注册时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }
  // 有邮箱
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email=$${sqlParams.length}`;
  }
  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);
  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

export const deleteUserByUserId = async (json) => {
  const user_id = json.user_id;

  const { rows: userList } = await db.query(
    `
    select * from user_info where user_id=$1;
  `,
    [user_id]
  );
  if (userList.length <= 0) {
    return { msg: "用户不存在" };
  }
  const user = userList[0];

  // 更新用户的昵称、邮箱、头像为空
  await db.query(
    `
    update user_info set is_delete=true where id=$1;
  `,
    [user.id]
  );

  return { msg: "删除成功" };
};

export const getGroupUserList = async (json) => {
  const login_from = json.login_from;
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    // 有注册时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
      select country, count(1) as count from user_info ui
      where 1=1 and ui.is_delete=false $$sqlQueryAppend$$
      group by ui.country
      order by count desc
      ;
    `;
  let sqlParams = [];
  let sqlQueryAppend = "";
  // 有 login_from
  if (login_from) {
    if (login_from == "empty") {
      sqlParams.push("");
      sqlQueryAppend += ` and (ui.login_from = $${sqlParams.length} or ui.login_from is null)`;
    } else {
      sqlParams.push(login_from);
      sqlQueryAppend += ` and ui.login_from = $${sqlParams.length}`;
    }
  }
  // 有注册时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }
  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  return {
    resultList: list,
  };
};
