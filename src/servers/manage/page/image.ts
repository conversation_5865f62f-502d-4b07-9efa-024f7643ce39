import { getDb } from "~/libs/db/db";
import { generateSectionBaseId } from "~/utils/uidUtil";
import { generateFourDigitUID } from "~/utils/uidUtil";

const db = getDb();

// 添加图片到图片库
export async function addImage(json: any) {
  const { image_url, image_type, image_name } = json;
  const uid = generateFourDigitUID();

  if (!image_url) {
    return {
      code: 500,
      message: "图片URL不能为空",
    };
  }

  if (!image_type || !["icon", "painting"].includes(image_type)) {
    return {
      code: 500,
      message: "图片类型必须是 icon 或 painting",
    };
  }

  const now = new Date();

  // 插入新图片记录
  const { rows } = await db.query(
    `
    INSERT INTO landing_image 
    (uid, image_url, image_type, image_name, created_at, updated_at)
    VALUES ($1, $2, $3, $4, $5, $6) 
    RETURNING *
    `,
    [uid, image_url, image_type, image_name || "", now, now]
  );

  return {
    code: 200,
    message: "图片添加成功",
    data: rows[0],
  };
}

// 分页获取图片列表
export async function getImageList(json: any) {
  const { image_type, page = 1, pageSize = 20, searchTerm = "" } = json;
  const offset = (page - 1) * pageSize;
  console.log("searchTerm>>>>", searchTerm);
  let query = `
    SELECT * FROM landing_image
    WHERE is_delete = false
  `;

  const params = [];

  // 如果指定了图片类型，添加到查询条件
  if (image_type && ["icon", "painting"].includes(image_type)) {
    query += ` AND image_type = $${params.length + 1}`;
    params.push(image_type);
  }

  // 添加搜索条件 - 修正这部分
  if (searchTerm && searchTerm.trim() !== "") {
    query += ` AND (
      image_name ILIKE $${params.length + 1} 
      OR image_url ILIKE $${params.length + 1}
    )`;
    params.push(`%${searchTerm}%`); // 使用ILIKE和%进行模糊匹配
  }

  // 添加分页和排序
  query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
  params.push(pageSize, offset);

  // 执行查询
  const { rows: images } = await db.query(query, params);

  // 获取总记录数 - 同样修正这部分
  let countQuery = `
    SELECT COUNT(*) as total FROM landing_image
    WHERE is_delete = false
  `;

  const countParams = [];

  if (image_type && ["icon", "painting"].includes(image_type)) {
    countQuery += ` AND image_type = $${countParams.length + 1}`;
    countParams.push(image_type);
  }

  // 添加搜索条件到计数查询
  if (searchTerm && searchTerm.trim() !== "") {
    countQuery += ` AND (
      image_name ILIKE $${countParams.length + 1} 
      OR image_url ILIKE $${countParams.length + 1}
    )`;
    countParams.push(`%${searchTerm}%`);
  }

  const { rows: countResult } = await db.query(countQuery, countParams);
  const total = parseInt(countResult[0].total);

  return {
    code: 200,
    message: "获取成功",
    data: {
      list: images,
      pagination: {
        current: page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      },
    },
  };
}

// 删除图片
export async function deleteImage(json: any) {
  const { uid } = json;

  if (!uid) {
    return {
      code: 500,
      message: "图片ID不能为空",
    };
  }

  // 软删除图片
  await db.query(
    `
    UPDATE landing_image
    SET is_delete = true, updated_at = NOW()
    WHERE uid = $1
    `,
    [uid]
  );

  return {
    code: 200,
    message: "图片删除成功",
  };
}

// 更新图片信息
export async function updateImage(json: any) {
  const { id, image_name, image_type } = json;

  if (!id) {
    return {
      code: 500,
      message: "图片ID不能为空",
    };
  }

  const updateFields = [];
  const params = [];

  if (image_name !== undefined) {
    updateFields.push(`image_name = $${params.length + 1}`);
    params.push(image_name);
  }

  if (image_type !== undefined && ["icon", "painting"].includes(image_type)) {
    updateFields.push(`image_type = $${params.length + 1}`);
    params.push(image_type);
  }

  if (updateFields.length === 0) {
    return {
      code: 400,
      message: "没有提供需要更新的字段",
    };
  }

  updateFields.push(`updated_at = NOW()`);

  params.push(id);

  // 更新图片信息
  await db.query(
    `
    UPDATE landing_image
    SET ${updateFields.join(", ")}
    WHERE id = $${params.length}
    `,
    params
  );

  return {
    code: 200,
    message: "图片信息更新成功",
  };
}
