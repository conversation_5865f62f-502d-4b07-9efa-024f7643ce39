import { getDb } from "~/libs/db/db";
import { generateContentBaseId } from "~/utils/uidUtil";

const db = getDb();
export async function savePageContent(json: any) {
  const { sectionList, page_url } = json;

  // 查询出 page_url 在数据库已有的 content 列表
  const { rows: existContentList } = await db.query(
    `
    select * from landing_content where page_url = $1 and is_delete=false
  `,
    [page_url]
  );

  // 遍历 sectionList 插入到 landing_content 表中
  for (let i = 0; i < sectionList.length; i++) {
    const section = sectionList[i];
    // 判断是否存在，存在则更新，不存在则插入
    const existContent = existContentList.find((item: any) => item.content_base_id === section.content_base_id);
    if (existContent) {
      // 更新
      await db.query(
        `
        UPDATE landing_content
        SET content_sort = $1, page_url = $2, section_name = $3
        WHERE content_base_id = $4
      `,
        [i, page_url, section.section_name, section.content_base_id]
      );
    } else {
      const newBaseId = generateContentBaseId();
      // 插入
      await db.query(
        `
        INSERT INTO landing_content (content_base_id, page_url, section_name, section_base_id, content_sort)
        VALUES ($1, $2, $3, $4, $5)
      `,
        [newBaseId, page_url, section.section_name, section.base_id, i]
      );
    }
  }

  return {
    code: 200,
    message: "保存成功",
  };
}

// 新增section
export async function addPageContent(json: any) {
  const { page_url, section_name } = json;
  const newBaseId = generateContentBaseId();
  // 获取 section 的 base_id
  const { rows: sectionInfo } = await db.query(
    `
    SELECT base_id FROM landing_section 
    WHERE section_name = $1 AND is_delete = false
    LIMIT 1
    `,
    [section_name]
  );

  if (!sectionInfo || sectionInfo.length === 0) {
    return {
      code: 500,
      message: "未找到对应的组件",
    };
  }

  const section_base_id = sectionInfo[0].base_id;
  const content_sort = json.content_sort || 0;

  // 插入新的 content 记录
  const { rows } = await db.query(
    `
    INSERT INTO landing_content 
    (content_base_id, page_url, section_name, section_base_id, content_sort)
    VALUES ($1, $2, $3, $4, $5)
    RETURNING *
    `,
    [newBaseId, page_url, section_name, section_base_id, content_sort]
  );

  // 获取完整的 section 信息用于返回
  const { rows: fullSection } = await db.query(
    `
    SELECT lc.*, ls.* 
    FROM landing_content lc
    LEFT JOIN landing_section ls ON ls.base_id = lc.section_base_id
    WHERE lc.content_base_id = $1
    `,
    [newBaseId]
  );

  return {
    code: 200,
    message: "添加成功",
    data: fullSection[0]
  };
}

export async function getPageSectionList(json: any) {
  const { page_url } = json;
  // 查询出page 有哪些 content 对应的 section
  const { rows: sectionList } = await db.query(
    `
    select ls.*, lc.content_base_id, lc.content_base_id as uniqueid, lc.content_sort, lc.content_json
    from landing_content lc
    left join landing_section ls on ls.base_id = lc.section_base_id
    where lc.page_url = $1 and lc.is_delete=false
    order by lc.content_sort asc
  `,
    [page_url]
  );

  return {
    code: 200,
    message: "获取成功",
    resultList: sectionList,
  };
}

export async function getPageContentList(json: any) {
  const { page_url } = json;

  const { rows: contentList } = await db.query(
    `
    select lc.*, ls.section_name, ls.section_type from landing_content lc
    left join landing_section ls on ls.base_id = lc.section_base_id
    where lc.page_url = $1 and lc.is_delete=false
    order by lc.content_sort asc
  `,
    [page_url]
  );

  return {
    code: 200,
    message: "获取成功",
    resultList: contentList,
  };
}

export async function changeContentStatus(json: any) {
  const { id, status } = json;
  await db.query(
    `
    UPDATE landing_content SET status = $1, updated_at = now() WHERE id = $2
  `,
    [status, id]
  );

  return {
    code: 200,
    message: "操作成功",
  };
}

//发布content
export async function publishContent(json: any) {
  const { content_base_id } = json;
  if (!content_base_id) {
    return {
      code: 500,
      message: "content_base_id 不能为空",
    };
  }
  await db.query(
    `
    UPDATE landing_content SET status = 1 WHERE content_base_id = $1
  `,
    [content_base_id]
  );

  return {
    code: 200,
    message: "操作成功",
  };
}

export async function deleteContentById(json: any) {
  const { content_base_id } = json;
  await db.query(
    `
    UPDATE landing_content
      SET is_delete = true
    WHERE content_base_id = $1
  `,
    [content_base_id]
  );
  return {
    code: 200,
    message: "操作成功",
  };
}

export async function getContentSection(content_base_id: string) {
  const { rows: section } = await db.query(
    `
    select lc.*, ls.section_name, ls.section_type, ls.section_json,ls.styles from landing_content lc
    left join landing_section ls on ls.base_id = lc.section_base_id
    where lc.content_base_id = $1
    `,
    [content_base_id]
  );
  if (section.length > 0) {
    return section[0];
  }
  return null;
}

export async function saveContentJson(json: any) {
  const { content_base_id, content_json, content_styles, page_url, section_name } = json;

  // 如果是临时ID，执行插入操作
  if (content_base_id.startsWith("temp-")) {
    // 获取section信息
    const { rows: sectionInfo } = await db.query(
      `
      SELECT base_id FROM landing_section 
      WHERE section_name = $1 AND is_delete = false
      LIMIT 1
      `,
      [section_name]
    );

    if (!sectionInfo || sectionInfo.length === 0) {
      return {
        code: 500,
        message: "未找到对应的组件",
      };
    }

    // 生成新的content_base_id
    const newBaseId = generateContentBaseId();

    // 插入新的content记录
    await db.query(
      `
      INSERT INTO landing_content 
      (content_base_id, page_url, section_name, section_base_id, content_sort, content_json, content_styles)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      `,
      [newBaseId, json.page_url, section_name, sectionInfo[0].base_id, json.content_sort || 0, content_json, content_styles]
    );

    return {
      code: 200,
      message: "保存成功",
      data: { content_base_id: newBaseId },
    };
  }

  // 如果这是一批保存操作中的第一个请求，清理已删除的sections
  if (json.isFirstSection) {
    const { currentSectionIds } = json; // 从前端传入当前页面上所有的section IDs

    // 将不在当前列表中的section标记为删除
    await db.query(
      `
      UPDATE landing_content 
      SET is_delete = true 
      WHERE page_url = $1 
        AND is_delete = false 
        AND content_base_id NOT IN (SELECT unnest($2::text[]))
        AND NOT content_base_id LIKE 'temp-%'
      `,
      [page_url, currentSectionIds]
    );
  }

  // 原有的更新逻辑
  await db.query(
    `
    UPDATE landing_content
    SET content_json = $1, content_styles = $2, content_sort = $3, updated_at = now()
    WHERE content_base_id = $4
    `,
    [content_json, content_styles, json.content_sort || 0, content_base_id]
  );

  return {
    code: 200,
    message: "保存成功",
  };
}

export async function updateContentSort(json: any) {
  const { id, content_sort } = json;
  await db.query(
    `
    UPDATE landing_content SET content_sort = $1 WHERE id = $2
  `,
    [content_sort, id]
  );

  return {
    code: 200,
    message: "操作成功",
  };
}
// 将 content_json 中的 styles 剔除掉，存入到 content_styles 中
export const updateContentStyles = async () => {
  // 获取所有记录
  const { rows: contents } = await db.query(`
    SELECT content_base_id, content_json, content_styles 
    FROM landing_content 
    WHERE is_delete = false
  `);

  // 遍历处理每条记录
  for (const content of contents) {
    try {
      let contentJson = content.content_json;

      // 如果content_json中包含styles属性
      if (contentJson && contentJson.styles) {
        // 提取styles对象
        const styles = contentJson.styles;
        // 删除content_json中的styles
        delete contentJson.styles;

        // 更新数据库记录
        await db.query(
          `
          UPDATE landing_content 
          SET content_json = $1, content_styles = $2
          WHERE content_base_id = $3
        `,
          [contentJson, styles, content.content_base_id]
        );
      }
    } catch (error) {
      console.error(`处理记录ID ${content.content_base_id} 时出错:`, error);
    }
  }

  return {
    code: 200,
    message: "内容样式数据迁移完成",
  };
};
