import { getDb } from "~/libs/db/db";
import { generateNewShortUniqueUID } from "~/utils/uidUtil";
import { getUserByServerSession } from "~/servers/common/user";
import { checkAdminUser } from "~/utils/checkWhiteUser";
const db = getDb();

export const saveBlog = async (blogData: {
  uid?: string;
  title: string;
  description: string;
  content_title: string;
  content_markdown: string;
  blog_url: string;
  language?: string;
  user_id: string;
  is_public?: boolean;
  generator?: string;
  content_description?: string;
  content_banner?: string;
  form_title?: string;
  form_description?: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      })
    );
  }
  const current_uid = user_info.user_id;
  if (blogData.uid) {
    const checkAuthorQuery = `
      SELECT user_id FROM blog WHERE uid = $1
    `;
    const authorResult = await db.query(checkAuthorQuery, [blogData.uid]);

    if (authorResult.rows.length === 0 || authorResult.rows[0].user_id !== current_uid) {
      return {
        status: 403,
        message: "您没有权限更新此博客",
      };
    }
    const updateQuery = `
      UPDATE blog
      SET title = $1, description = $2, content_title = $3, content_markdown = $4,
          language = $5, user_id = $6, is_public = $7, updated_at = NOW(), 
          generator = $8, blog_url = $9, content_description = $10, content_banner = $11, form_title = $12, form_description = $13
      WHERE uid = $14
      RETURNING *;
    `;
    const { rows } = await db.query(updateQuery, [
      blogData.title,
      blogData.description,
      blogData.content_title,
      blogData.content_markdown,
      blogData.language || "zh",
      blogData.user_id,
      true,
      blogData.generator || null,
      blogData.blog_url,
      blogData.content_description,
      blogData.content_banner,
      blogData.form_title,
      blogData.form_description,
      blogData.uid,
    ]);

    return { code: 200, data: rows[0], message: "update success" };
  } else {
    // 创建新博客
    const uid = generateNewShortUniqueUID();
    // 检查 blog_url 是否已存在
    const checkUrlQuery = `
      SELECT * FROM blog WHERE blog_url = $1 AND is_delete = false
    `;
    const { rows: urlRows } = await db.query(checkUrlQuery, [blogData.blog_url]);
    if (urlRows.length > 0) {
      return { code: 400, message: "Blog url already exists" };
    }
    const insertQuery = `
      INSERT INTO blog (uid, title, description, content_title, content_markdown, language, user_id, is_public, generator, blog_url, content_description, content_banner, form_title, form_description)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *;
    `;
    const { rows } = await db.query(insertQuery, [
      uid,
      blogData.title,
      blogData.description,
      blogData.content_title,
      blogData.content_markdown,
      blogData.language || "en",
      current_uid,
      true,
      blogData.generator || null,
      blogData.blog_url,
      blogData.content_description,
      blogData.content_banner,
      blogData.form_title,
      blogData.form_description,
    ]);
    return { code: 200, data: rows[0], message: "create success" };
  }
};
// 删除博客
export const deleteBlog = async (uid: string) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      })
    );
  }
  const current_uid = user_info.user_id;
  const deleteQuery = `
    UPDATE blog SET is_delete = true WHERE uid = $1 AND user_id = $2
  `;
  const { rows } = await db.query(deleteQuery, [uid, current_uid]);
  if (rows.length === 0) {
    return { code: 404, message: "delete failed" };
  }
  return { code: 200, message: "delete success" };
};
// 获取博客
export const getBlogDetailByUrl = async (blog_url: string) => {
  const getQuery = `
    SELECT * FROM blog WHERE blog_url = $1 and is_public = true and is_delete = false
  `;
  const { rows } = await db.query(getQuery, [blog_url]);
  if (rows.length > 0) {
    const currentBlog = rows[0];
    const relatedBlogsQuery = `
      SELECT * FROM blog 
      WHERE generator = $1 
      AND blog_url != $2 
      AND is_public = true
      AND is_delete = false
    `;
    const relatedBlogsResult = await db.query(relatedBlogsQuery, [currentBlog.generator, blog_url]);

    const relatedBlogs = relatedBlogsResult.rows;
    rows[0].relatedBlogs = relatedBlogs;
  }
  if (rows.length === 0) {
    return { code: 404, message: "Blog not found" };
  }
  return { code: 200, data: rows[0], message: "Get success" };
};

// 获取所有状态的博客
export const getAllBlogDetailByUrl = async (blog_url: string) => {
  const getQuery = `
    SELECT * FROM blog WHERE blog_url = $1 and is_delete = false
  `;
  const { rows } = await db.query(getQuery, [blog_url]);
  if (rows.length > 0) {
    const currentBlog = rows[0];
    const relatedBlogsQuery = `
      SELECT * FROM blog 
      WHERE generator = $1 
      AND blog_url != $2 
      AND is_public = true
      AND is_delete = false
    `;
    const relatedBlogsResult = await db.query(relatedBlogsQuery, [currentBlog.generator, blog_url]);

    const relatedBlogs = relatedBlogsResult.rows;
    rows[0].relatedBlogs = relatedBlogs;
  }
  if (rows.length === 0) {
    return { code: 404, message: "Blog not found" };
  }
  return { code: 200, data: rows[0], message: "Get success" };
};


// 根据 uid 获取博客详情
export const getBlogDetailByUid = async (json) => {
  const uid = json.uid;
  if (!uid) {
    return { code: 400, message: "Uid is required" };
  }
  const getQuery = `
    SELECT * FROM blog WHERE uid = $1 and is_delete = false
  `;
  const { rows } = await db.query(getQuery, [uid]);
  return { code: 200, data: rows[0], message: "Get success" };
};

// 根据 user_id 获取博客列表
export const getBlogListByUserId = async (json) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      })
    );
  }
  const getQuery = `
    SELECT * FROM blog WHERE user_id = $1 and is_delete = false
    order by created_at desc
    limit $2 offset $3  
  `;
  const { rows } = await db.query(getQuery, [user_info.user_id, json.pageSize, json.skipSize]);
  const pageDesc = await getTotalPage(json);
  rows.totalPage = pageDesc.totalPage;
  rows.countTotal = pageDesc.countTotal;
  return { code: 200, data: rows, message: "Get success" };
};
// 管理员可以查看所有已发布未删除的 博客
// 管理员查看所有已发布未删除的博客
export const getAllPublicBlog = async (json) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      })
    );
  }
  const getQuery = `
    SELECT blog.*, user_info.name 
    FROM blog 
    LEFT JOIN user_info ON blog.user_id = user_info.user_id
    WHERE blog.is_delete = false AND blog.is_public = true
    ORDER BY blog.created_at DESC
    LIMIT $1 OFFSET $2
  `;
  const { rows } = await db.query(getQuery, [json.pageSize, json.skipSize]);
  const pageDesc = await getTotalPage(json);
  rows.totalPage = pageDesc.totalPage;
  rows.countTotal = pageDesc.countTotal;
  return { code: 200, data: rows, message: "获取成功" };
};

export const getTotalPage = async (json) => {
  const pageSize = json.pageSize || Number(process.env.NEXT_PUBLIC_MANAGE_USER_WORKS_SIZE);
  const generator = json.generator;
  // 默认 SQL 查询和参数
  let sqlQuery = `
    SELECT COUNT(1)
    FROM blog
    WHERE is_delete = false 
    $$sqlQueryAppend$$
  `;
  let sqlParams = [];
  let sqlQueryAppend = "";

  // 如果提供了 generator，添加到查询条件中
  if (generator) {
    sqlParams.push(generator);
    sqlQueryAppend += ` AND generator = $${sqlParams.length}`;
  }

  // 拼接 SQL 查询
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

// 根据 generator 获取博客列表
export const getBlogListByGenerator = async (generator: string) => {
  if (!generator) {
    return { code: 400, message: "Generator is required" };
  }
  const getQuery = `
    SELECT * FROM blog WHERE generator = $1 and is_public = true and is_delete = false
  `;
  const { rows } = await db.query(getQuery, [generator]);
  return { code: 200, data: rows, message: "Get success" };
};

// 保存 blog，将 is_public 设置为 false
export const saveBlogDraft = async (blogData: {
  uid: string;
  title: string;
  description: string;
  content_title: string;
  content_markdown: string;
  blog_url: string;
  language?: string;
  user_id: string;
  is_public: boolean;
  generator: string;
  content_description: string;
  content_banner: string;
  form_title: string;
  form_description: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      })
    );
  }
  const current_uid = user_info.user_id;

  if (blogData.uid) {
    const checkAuthorQuery = `
      SELECT user_id FROM blog WHERE uid = $1
    `;
    const authorResult = await db.query(checkAuthorQuery, [blogData.uid]);

    if (authorResult.rows.length === 0 || authorResult.rows[0].user_id !== current_uid) {
      return {
        status: 403,
        message: "您没有权限更新此博客",
      };
    }
    const updateQuery = `
      UPDATE blog
      SET title = $1, description = $2, content_title = $3, content_markdown = $4,
          language = $5, user_id = $6, updated_at = NOW(), 
          generator = $7, blog_url = $8, content_description = $9, content_banner = $10, form_title = $11, form_description = $12
      WHERE uid = $13
      RETURNING *;
    `;
    const { rows } = await db.query(updateQuery, [
      blogData.title,
      blogData.description,
      blogData.content_title,
      blogData.content_markdown,
      blogData.language || "en",
      current_uid,
      blogData.generator,
      blogData.blog_url,
      blogData.content_description,
      blogData.content_banner,
      blogData.form_title,
      blogData.form_description,
      blogData.uid,
    ]);
    return { code: 200, data: rows[0], message: "update success" };
  } else {
    const checkUrlQuery = `
      SELECT * FROM blog WHERE blog_url = $1 AND is_delete = false
    `;
    const { rows: urlRows } = await db.query(checkUrlQuery, [blogData.blog_url]);
    if (urlRows.length > 0) {
      return { code: 400, message: "Blog url already exists" };
    }
    const insertQuery = `
      INSERT INTO blog (uid, title, description, content_title, content_markdown, language, user_id, is_public, generator, blog_url, content_description, content_banner, form_title, form_description)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *;
    `;
    const { rows } = await db.query(insertQuery, [
      generateNewShortUniqueUID(),
      blogData.title,
      blogData.description,
      blogData.content_title,
      blogData.content_markdown,
      blogData.language || "en",
      current_uid,
      false,
      blogData.generator,
      blogData.blog_url,
      blogData.content_description,
      blogData.content_banner,
      blogData.form_title,
      blogData.form_description,
    ]);
    return { code: 200, data: rows[0], message: "create success" };
  }
};

// 获取首页博客列表，连表查询，需要获取 user_info 的 name 字段
export const getHomeBlogList = async () => {
  const getQuery = `
    SELECT blog.*, user_info.name as author
    FROM blog 
    LEFT JOIN user_info ON blog.user_id = user_info.user_id
    WHERE blog.is_public = true and blog.is_delete = false and blog.generator = 'blog'
    order by created_at desc
  `;
  const { rows } = await db.query(getQuery);

  return { code: 200, data: rows, message: "Get success" };
};

// 隐藏博客的 public 状态
export const updateBlogPublicStatus = async (uid: string) => {
  if (!uid) {
    return { code: 400, message: "Uid is required" };
  }
  const updateQuery = `
    UPDATE blog SET is_public = false WHERE uid = $1
  `;
  const { rows } = await db.query(updateQuery, [uid]);
  return { code: 200, data: rows[0], message: "Update success" };
};
