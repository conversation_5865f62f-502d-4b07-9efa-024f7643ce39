import { manageOrderSize } from "~/configs/globalConfig";
import { getDb } from "~/libs/db/db";
import { formatDateToUTC8 } from "~/utils/handleData";
import { notifyToWeixin } from "../common/weixin";

const db = getDb();

export const getStripeOrderList = async (json: any) => {
  // 查询出 stripe_order_record表中的数据
  const positionPage = json.positionPage;
  let pageSize = manageOrderSize;
  if (json.pageSize) {
    try {
      pageSize = Number(json.pageSize);
    } catch (error) {
      console.error("json.pageSize-error-=-=->", error);
      await notifyToWeixin(`json.pageSize-error-=->\n ${JSON.stringify(json)}\n error: ${error}`);
    }
  }
  const skipSize = pageSize * (Number(positionPage) - 1);

  const timeFlag = "getStripeOrderList-=->" + new Date().getTime();
  console.time(timeFlag);

  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select sor.*, ui.email, ui.login_from, ui.user_agent, ui.created_at as user_created_at from stripe_order_record sor
    left join stripe_customers sc on sc.stripe_customer_id = sor.stripe_customer_id
    left join user_info ui on ui.user_id=sc.user_id
    where sor.status=$1 $$sqlQueryAppend$$
    order by sor.created_at desc
    limit $2 offset $3
  `;
  let sqlParams = ["complete", pageSize, skipSize];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and sor.created_at >= $${sqlParams.length - 1} and sor.created_at <= $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: orderList } = await db.query(sqlQuery, sqlParams);

  if (orderList.length > 0) {
    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i];
      // 获取订单金额
      const checkout = JSON.parse(order.checkout_session);
      order.pay_amount = checkout.amount_total;
      const user_agent = order.user_agent;
      if (user_agent) {
        order.device = deviceType(user_agent);
      } else {
        order.device = "";
      }
      const subscription_id = checkout.subscription;
      const stripe_customer_id = order.stripe_customer_id;
      // 查询该订单是否取消了订阅
      const { rows: cancelOrderInfoList } = await db.query(
        `
        select * from stripe_order_record sor
          where sor.stripe_customer_id=$1 and sor.status=$2
          and sor.checkout_session like $3
      `,
        [stripe_customer_id, "active", '%"cancel_at_period_end":true%']
      );
      if (cancelOrderInfoList.length > 0) {
        let cancelDetail = "";
        for (let j = 0; j < cancelOrderInfoList.length; j++) {
          const cancelOrderInfo = cancelOrderInfoList[j];
          if (cancelOrderInfo.id != subscription_id) {
            continue;
          }
          order.cancel_at_period_end = true;
          const cancel_checkout = JSON.parse(cancelOrderInfo.checkout_session);
          if (!cancel_checkout.cancellation_details.feedback) {
            continue;
          }
          cancelDetail += cancel_checkout.cancellation_details.feedback;
          if (cancel_checkout.cancellation_details.comment) {
            cancelDetail += ": " + cancel_checkout.cancellation_details.comment;
          }
        }
        order.cancelDetail = cancelDetail;
      }
    }
  }

  // 查询出页数信息
  const pageDesc = await getTotalPage(json);
  console.timeEnd(timeFlag);
  return {
    resultList: orderList,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

export const getTotalPage = async (json) => {
  let pageSize = manageOrderSize;
  if (json.pageSize) {
    try {
      pageSize = Number(json.pageSize);
    } catch (error) {
      console.error("json.pageSize-error-=-=->", error);
      await notifyToWeixin(`json.pageSize-error-=->\n ${JSON.stringify(json)}\n error: ${error}`);
    }
  }

  // 支付时间
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    // 有支付时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select count(1)
  from stripe_order_record sor
  where sor.status=$1 $$sqlQueryAppend$$
  `;
  let sqlParams = ["complete"];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and sor.created_at >= $${sqlParams.length - 1} and sor.created_at <= $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);
  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);
  // console.log("countTotal-=-=->", countTotal);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

function deviceType(userAgent) {
  if (/Mobile|Android|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(userAgent)) {
    return "手机";
  } else {
    return "电脑";
  }
}

export const getStripeGroupList = async (json) => {
  const login_from = json.login_from;
  const utm_source = json.utm_source;
  const utm_campaign = json.utm_campaign;

  // 支付时间
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;
  // 注册时间
  const registerDate = json.registerDate;
  const registerStartDate = registerDate.startDate;
  const registerEndDate = registerDate.endDate;

  let startTime;
  let endTime;
  let registerStartTime;
  let registerEndTime;
  if (startDate && endDate) {
    // 有支付时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }
  if (registerStartDate && registerEndDate) {
    // 注册时间
    registerStartTime = formatDateToUTC8(registerStartDate, true);
    registerEndTime = formatDateToUTC8(registerEndDate, false);
  }
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
    select
      count(card_address_country) as count,
      card_address_country,
      SUM((sor.checkout_session::json->>'amount_total')::numeric) AS total_paid_amount
    from stripe_order_record sor
      left join stripe_customers sc on sc.stripe_customer_id = sor.stripe_customer_id
      left join user_info ui on ui.user_id=sc.user_id
    where sor.status=$1 $$sqlQueryAppend$$
    group by sor.card_address_country
    order by count desc
  `;

  let sqlParams = ["complete"];
  let sqlQueryAppend = "";
  if (login_from) {
    sqlParams.push(login_from);
    sqlQueryAppend += ` and ui.login_from = $${sqlParams.length}`;
  }

  // 有 utm_source
  if (utm_source) {
    sqlParams.push(utm_source);
    sqlQueryAppend += ` and ui.utm_source = $${sqlParams.length}`;
  }

  // 有 utm_campaign
  if (utm_campaign) {
    sqlParams.push(utm_campaign);
    sqlQueryAppend += ` and ui.utm_campaign = $${sqlParams.length}`;
  }

  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and sor.created_at >= $${sqlParams.length - 1} and sor.created_at <= $${sqlParams.length}`;
  }
  // 有注册时间
  if (registerStartTime && registerEndTime) {
    sqlParams.push(registerStartTime);
    sqlParams.push(registerEndTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  return {
    resultList: list,
  };
};

export const getPaddleOrderList = async (json) => {
  // 查询出 paddle_webhook_record表中的数据
  const positionPage = json.positionPage;
  const email = json.email;
  const pageSize = manageOrderSize;
  const skipSize = pageSize * (Number(positionPage) - 1);

  const timeFlag = "getPaddleOrderList-=->" + new Date().getTime();
  console.time(timeFlag);

  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select pwr.*, ui.email, ui.login_from, ui.user_agent, ui.country, ui.created_at as user_created_at from paddle_webhook_record pwr
    left join user_info ui on ui.user_id=pwr.user_id
    where pwr.status = $1 $$sqlQueryAppend$$
    order by pwr.created_at desc
    limit $2 offset $3
  `;
  let sqlParams = ["completed", pageSize, skipSize];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and pwr.created_at >= $${sqlParams.length - 1} and pwr.created_at <= $${sqlParams.length}`;
  }

  // 有 email
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email = $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: orderList } = await db.query(sqlQuery, sqlParams);

  if (orderList.length > 0) {
    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i];
      // 获取订单金额
      const checkout = order.data.data.details.payout_totals;
      order.pay_amount = checkout.total;
      const user_agent = order.user_agent;
      if (user_agent) {
        order.device = deviceType(user_agent);
      } else {
        order.device = "";
      }
      // 查询该订单是否取消了订阅
      const id = order.data.data.subscription_id;
      const { rows: cancelOrderInfoList } = await db.query(
        `
        select * from paddle_webhook_record pwr
          where pwr.id=$1
        order by pwr.created_at desc
      `,
        [id]
      );
      if (cancelOrderInfoList.length > 0) {
        let cancelDetail = "";
        for (let j = 0; j < cancelOrderInfoList.length; j++) {
          const oneOrder = cancelOrderInfoList[j];
          // console.log('oneOrder-=->', oneOrder);
          // console.log('oneOrder.data?.data-=->', oneOrder.data?.data);
          // console.log('oneOrder.data?.data.next_billed_at-=->', oneOrder.data?.data?.next_billed_at);
          if (!oneOrder.data?.data?.next_billed_at) {
            cancelDetail = "已取消";
            break;
          }
        }
        order.cancelDetail = cancelDetail;
      }
    }
  }

  // 查询出页数信息
  const pageDesc = await getPaddleTotalPage(json);
  console.timeEnd(timeFlag);
  return {
    resultList: orderList,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

export const getPaddleTotalPage = async (json) => {
  const pageSize = manageOrderSize;

  const email = json.email;
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select count(1)
  from paddle_webhook_record pwr
  left join user_info ui on ui.user_id=pwr.user_id
  where pwr.status=$1 $$sqlQueryAppend$$
  `;
  let sqlParams = ["completed"];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and pwr.created_at >= $${sqlParams.length - 1} and pwr.created_at <= $${sqlParams.length}`;
  }

  // 有 email
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email = $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

export const getPaddleGroupList = async (json) => {
  const login_from = json.login_from;
  // 支付时间
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;
  // 注册时间
  const registerDate = json.registerDate;
  const registerStartDate = registerDate.startDate;
  const registerEndDate = registerDate.endDate;

  let startTime;
  let endTime;
  let registerStartTime;
  let registerEndTime;
  if (startDate && endDate) {
    // 有支付时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }
  if (registerStartDate && registerEndDate) {
    // 注册时间
    registerStartTime = formatDateToUTC8(registerStartDate, true);
    registerEndTime = formatDateToUTC8(registerEndDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select
              count(ui.country) as count,
          ui.country,
          SUM((pwr.data->'data'->'details'->'payout_totals'->>'total')::numeric) AS total_paid_amount,
          SUM((pwr.data->'data'->'details'->'payout_totals'->>'earnings')::numeric) AS total_earnings
          from paddle_webhook_record pwr
              left join user_info ui on ui.user_id=pwr.user_id
          where pwr.status=$1 $$sqlQueryAppend$$
          group by ui.country
          order by count desc
  `;
  let sqlParams = ["completed"];
  // let sqlParams = ['completed', login_from, startTime, endTime, registerStartTime, registerEndTime];
  let sqlQueryAppend = "";
  // 有 login_from
  if (login_from) {
    if (login_from == "empty") {
      sqlParams.push("");
      sqlQueryAppend += ` and (ui.login_from = $${sqlParams.length} or ui.login_from is null)`;
    } else {
      sqlParams.push(login_from);
      sqlQueryAppend += ` and ui.login_from = $${sqlParams.length}`;
    }
  }
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and pwr.created_at >= $${sqlParams.length - 1} and pwr.created_at <= $${sqlParams.length}`;
  }
  // 有注册时间
  if (registerStartTime && registerEndTime) {
    sqlParams.push(registerStartTime);
    sqlParams.push(registerEndTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }
  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  // console.log("sqlQuery-=-=->", sqlQuery);
  // console.log("sqlParams-=-=->", sqlParams);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  return {
    resultList: list,
  };
};

export const getDistinctUserFrom = async () => {
  const { rows: list } = await db.query(
    `
    select distinct(login_from) from user_info
  `,
    []
  );

  const listLoginFrom = [];
  list.forEach((item) => {
    if (item.login_from) {
      listLoginFrom.push(item.login_from);
    }
  });

  return listLoginFrom;
};

export const getDistinctUtmCampaign = async () => {
  const { rows: list } = await db.query(
    `
    select distinct(utm_campaign) from user_info
    where login_from is not null
  `,
    []
  );

  const listUtmCampaign = [];
  list.forEach((item) => {
    if (item.utm_campaign) {
      listUtmCampaign.push(item.utm_campaign);
    }
  });

  return listUtmCampaign;
};

// paypro
export const getPayproOrderList = async (json) => {
  const positionPage = json.positionPage;
  const email = json.email;
  let pageSize = manageOrderSize;
  if (json.pageSize) {
    try {
      pageSize = Number(json.pageSize);
    } catch (error) {
      console.error("json.pageSize-error-=-=->", error);
      await notifyToWeixin(`json.pageSize-error-=->\n ${JSON.stringify(json)}\n error: ${error}`);
    }
  }
  const skipSize = pageSize * (Number(positionPage) - 1);
  const timeFlag = "getPaddleOrderList-=->" + new Date().getTime();
  console.time(timeFlag);

  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select por.*, ui.email, ui.login_from, ui.user_agent, ui.created_at as user_created_at
    from paypro_order_record por
    left join user_info ui on ui.user_id=por.user_id
    where por.order_status = $1 $$sqlQueryAppend$$
    order by por.created_at desc
    limit $2 offset $3
  `;
  let sqlParams = ["Processed", pageSize, skipSize];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and por.created_at >= $${sqlParams.length - 1} and por.created_at <= $${sqlParams.length}`;
  }

  // 有 email
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email = $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  const { rows: orderList } = await db.query(sqlQuery, sqlParams);

  if (orderList.length > 0) {
    for (let i = 0; i < orderList.length; i++) {
      const order = orderList[i];
      const user_agent = order.user_agent;
      if (user_agent) {
        order.device = deviceType(user_agent);
      } else {
        order.device = "";
      }
    }
  }

  // 查询出页数信息
  const pageDesc = await getPayproTotalPage(json);

  console.timeEnd(timeFlag);
  return {
    resultList: orderList,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

export const getPayproTotalPage = async (json) => {
  let pageSize = manageOrderSize;
  if (json.pageSize) {
    try {
      pageSize = Number(json.pageSize);
    } catch (error) {
      console.error("json.pageSize-error-=-=->", error);
      await notifyToWeixin(`json.pageSize-error-=->\n ${JSON.stringify(json)}\n error: ${error}`);
    }
  }

  const email = json.email;
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;

  let startTime;
  let endTime;
  if (startDate && endDate) {
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select count(1)
    from paypro_order_record por
    left join user_info ui on ui.user_id=por.user_id
    where por.order_status = $1 $$sqlQueryAppend$$
  `;
  let sqlParams = ["Processed"];
  let sqlQueryAppend = "";
  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and por.created_at >= $${sqlParams.length - 1} and por.created_at <= $${sqlParams.length}`;
  }

  // 有 email
  if (email) {
    sqlParams.push(email);
    sqlQueryAppend += ` and ui.email = $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  return {
    totalPage: Math.ceil(countTotal[0].count / pageSize),
    countTotal: countTotal[0].count,
  };
};

export const getPayproGroupList = async (json) => {
  const login_from = json.login_from;
  // 支付时间
  const date = json.date;
  const startDate = date.startDate;
  const endDate = date.endDate;
  // 注册时间
  const registerDate = json.registerDate;
  const registerStartDate = registerDate.startDate;
  const registerEndDate = registerDate.endDate;

  let startTime;
  let endTime;
  let registerStartTime;
  let registerEndTime;
  if (startDate && endDate) {
    // 有支付时间
    startTime = formatDateToUTC8(startDate, true);
    endTime = formatDateToUTC8(endDate, false);
  }
  if (registerStartDate && registerEndDate) {
    // 注册时间
    registerStartTime = formatDateToUTC8(registerStartDate, true);
    registerEndTime = formatDateToUTC8(registerEndDate, false);
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断当前传参，生成 sql 及查询参数
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 默认 sql 及查询参数
  let sqlQuery = `
  select
    count(por.customer_country_code) as count,
    por.customer_country_code,
    SUM(por.product_price) AS total_paid_amount
    from paypro_order_record por
        left join user_info ui on ui.user_id=por.user_id
    where por.order_status=$1 $$sqlQueryAppend$$
    group by por.customer_country_code
    order by count desc
  `;
  let sqlParams = ["Processed"];
  let sqlQueryAppend = "";

  // 有 login_from
  if (login_from) {
    if (login_from == "empty") {
      sqlParams.push("");
      sqlQueryAppend += ` and (ui.login_from = $${sqlParams.length} or ui.login_from is null)`;
    } else {
      sqlParams.push(login_from);
      sqlQueryAppend += ` and ui.login_from = $${sqlParams.length}`;
    }
  }

  // 有支付时间
  if (startTime && endTime) {
    sqlParams.push(startTime);
    sqlParams.push(endTime);
    sqlQueryAppend += ` and por.created_at >= $${sqlParams.length - 1} and por.created_at <= $${sqlParams.length}`;
  }

  // 有注册时间
  if (registerStartTime && registerEndTime) {
    sqlParams.push(registerStartTime);
    sqlParams.push(registerEndTime);
    sqlQueryAppend += ` and ui.created_at >= $${sqlParams.length - 1} and ui.created_at <= $${sqlParams.length}`;
  }

  // 拼接 sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlQueryAppend);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  return { resultList: list };
};
