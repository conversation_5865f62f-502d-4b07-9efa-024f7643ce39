import { getDb } from "~/libs/db/db";

const db = getDb();

// 验证错误类型
export interface ValidationError {
  field: string;
  message: string;
}

// 评论验证参数
export interface CommentValidationData {
  content: string;
  user_id: string;
  page_uid: string;
  parent_comment_uid?: string;
}

/**
 * 验证评论内容
 * @param data 评论数据
 * @returns 验证结果
 */
export const validateComment = async (data: CommentValidationData): Promise<{
  isValid: boolean;
  errors: ValidationError[];
}> => {
  const errors: ValidationError[] = [];

  // 1. 基础字段验证
  if (!data.content || typeof data.content !== 'string') {
    errors.push({ field: 'content', message: 'Comment content cannot be empty' });
  } else {
    // 内容长度验证
    const trimmedContent = data.content.trim();
    if (trimmedContent.length === 0) {
      errors.push({ field: 'content', message: 'Comment content cannot be empty' });
    } else if (trimmedContent.length < 2) {
      errors.push({ field: 'content', message: 'Comment must be at least 2 characters' });
    } else if (trimmedContent.length > 1000) {
      errors.push({ field: 'content', message: 'Comment cannot exceed 1000 characters' });
    }
  }

  // 2. 用户ID验证
  if (!data.user_id || typeof data.user_id !== 'string') {
    errors.push({ field: 'user_id', message: 'User ID cannot be empty' });
  }

  // 3. 页面ID验证
  if (!data.page_uid || typeof data.page_uid !== 'string') {
    errors.push({ field: 'page_uid', message: 'Page ID cannot be empty' });
  }

  // 4. 如果有父评论ID，验证其存在性
  if (data.parent_comment_uid) {
    try {
      const { rows: parentComments } = await db.query(
        'SELECT uid FROM comments WHERE uid = $1 AND is_delete = false',
        [data.parent_comment_uid]
      );
      if (parentComments.length === 0) {
        errors.push({ field: 'parent_comment_uid', message: 'Parent comment does not exist' });
      }
    } catch (error) {
      console.error('Failed to validate parent comment:', error);
      errors.push({ field: 'parent_comment_uid', message: 'Parent comment validation failed' });
    }
  }

  // 5. 敏感词检查
  if (data.content && data.content.trim().length > 0) {
    const sensitiveWordCheck = await checkSensitiveWords(data.content);
    if (!sensitiveWordCheck.isValid) {
      errors.push({ field: 'content', message: `Comment contains sensitive words: ${sensitiveWordCheck.words.join(', ')}` });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 清理和净化评论内容
 * @param content 原始内容
 * @returns 清理后的内容
 */
export const sanitizeContent = (content: string): string => {
  if (!content || typeof content !== 'string') {
    return '';
  }

  let sanitized = content;

  // 1. 移除危险的HTML标签和脚本
  sanitized = sanitized
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // 移除script标签
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // 移除iframe标签
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '') // 移除object标签
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '') // 移除embed标签
    .replace(/javascript:/gi, '') // 移除javascript协议
    .replace(/on\w+\s*=/gi, ''); // 移除事件处理器

  // 2. 移除所有HTML标签（保留纯文本）
  sanitized = sanitized.replace(/<[^>]*>/g, '');

  // 3. 解码HTML实体
  sanitized = sanitized
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/');

  // 4. 清理多余的空白字符
  sanitized = sanitized
    .replace(/\s+/g, ' ') // 多个空格合并为一个
    .trim(); // 去除首尾空格

  // 5. 限制换行符数量
  sanitized = sanitized.replace(/\n{3,}/g, '\n\n');

  return sanitized;
};

/**
 * 检查敏感词（集成现有的敏感词系统）
 * @param content 内容
 * @returns 检查结果
 */
export const checkSensitiveWords = async (content: string): Promise<{
  isValid: boolean;
  words: string[];
}> => {
  try {
    // 查询敏感词表
    const { rows: sensitiveWords } = await db.query(
      'SELECT words FROM sensitive_words'
    );

    const foundWords: string[] = [];
    const lowerContent = content.toLowerCase();

    // 检查每个敏感词
    for (const row of sensitiveWords) {
      const word = row.words.toLowerCase();
      if (lowerContent.includes(word)) {
        foundWords.push(row.words);
      }
    }

    return {
      isValid: foundWords.length === 0,
      words: foundWords
    };
  } catch (error) {
    console.error('Failed to check sensitive words:', error);
    // 如果敏感词检查失败，为了安全起见，返回通过
    return {
      isValid: true,
      words: []
    };
  }
};

/**
 * 检查用户评论频率限制
 * @param user_id 用户ID
 * @param timeWindow 时间窗口（分钟）
 * @param maxComments 最大评论数
 * @returns 是否允许评论
 */
export const checkCommentRate = async (
  user_id: string, 
  timeWindow: number = 10, 
  maxComments: number = 5
): Promise<{
  isAllowed: boolean;
  message?: string;
}> => {
  try {
    const { rows: recentComments } = await db.query(
      `SELECT COUNT(*) as count 
       FROM comments 
       WHERE user_id = $1 
       AND created_at > NOW() - INTERVAL '${timeWindow} minutes'
       AND is_delete = false`,
      [user_id]
    );

    const commentCount = parseInt(recentComments[0].count);

    if (commentCount >= maxComments) {
      return {
        isAllowed: false,
        message: `Too many comments, please try again in ${timeWindow} minutes`
      };
    }

    return {
      isAllowed: true
    };
  } catch (error) {
    console.error('评论频率检查失败:', error);
    // 如果检查失败，为了用户体验，允许评论
    return {
      isAllowed: true
    };
  }
};

/**
 * 验证回复层级深度
 * @param parent_comment_uid 父评论ID
 * @param maxDepth 最大深度
 * @returns 是否允许回复
 */
export const validateReplyDepth = async (
  parent_comment_uid: string, 
  maxDepth: number = 1
): Promise<{
  isAllowed: boolean;
  message?: string;
}> => {
  try {
    // 检查父评论是否已经是回复（即是否有parent_comment_uid）
    const { rows: parentComments } = await db.query(
      'SELECT parent_comment_uid FROM comments WHERE uid = $1 AND is_delete = false',
      [parent_comment_uid]
    );

    if (parentComments.length === 0) {
      return {
        isAllowed: false,
        message: 'Parent comment does not exist'
      };
    }

    const parentComment = parentComments[0];

    // 如果父评论已经是回复，且最大深度为1，则不允许再回复
    if (parentComment.parent_comment_uid && maxDepth <= 1) {
      return {
        isAllowed: false,
        message: 'Multi-level replies not supported'
      };
    }

    return {
      isAllowed: true
    };
  } catch (error) {
    console.error('Failed to validate reply depth:', error);
    return {
      isAllowed: false,
      message: 'Reply validation failed'
    };
  }
};

/**
 * 综合验证评论数据
 * @param data 评论数据
 * @returns 验证结果
 */
export const validateCommentData = async (data: CommentValidationData): Promise<{
  isValid: boolean;
  errors: ValidationError[];
  sanitizedContent?: string;
}> => {
  // 1. 基础验证
  const basicValidation = await validateComment(data);
  if (!basicValidation.isValid) {
    return {
      isValid: false,
      errors: basicValidation.errors
    };
  }

  // 2. 频率限制检查
  const rateCheck = await checkCommentRate(data.user_id);
  if (!rateCheck.isAllowed) {
    return {
      isValid: false,
      errors: [{ field: 'rate_limit', message: rateCheck.message || 'Too many comments' }]
    };
  }

  // 3. 回复深度检查
  if (data.parent_comment_uid) {
    const depthCheck = await validateReplyDepth(data.parent_comment_uid);
    if (!depthCheck.isAllowed) {
      return {
        isValid: false,
        errors: [{ field: 'reply_depth', message: depthCheck.message || 'Reply depth too deep' }]
      };
    }
  }

  // 4. 内容清理
  const sanitizedContent = sanitizeContent(data.content);

  return {
    isValid: true,
    errors: [],
    sanitizedContent
  };
};
