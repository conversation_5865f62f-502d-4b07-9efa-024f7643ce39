import { getDb } from "~/libs/db/db";
import { generateNewUID } from "~/utils/uidUtil";

const db = getDb();

// 评论数据类型定义
export interface Comment {
  id?: number;
  uid: string;
  page_uid: string;
  parent_comment_uid?: string;
  user_id: string;
  content: string;
  is_delete?: boolean;
  is_approved?: boolean;
  user_name?: string;
  user_image?: string;
  reply_count?: number;
  created_at?: Date;
  updated_at?: Date;
}

// 获取评论列表参数
export interface GetCommentsParams {
  page_uid: string;
  page?: number;
  limit?: number;
  parent_comment_uid?: string | null;
}

// 创建评论参数
export interface CreateCommentParams {
  page_uid: string;
  parent_comment_uid?: string;
  user_id: string;
  content: string;
  user_name: string;
  user_image: string;
}

/**
 * 更新页面评论统计数量
 * @param pageUid 页面UID
 */
const updateCommentCount = async (pageUid: string) => {
  try {
    await db.query(
      `UPDATE pages 
       SET comment_count = (
         SELECT COUNT(*) 
         FROM comments 
         WHERE page_uid = $1 AND is_delete = false AND is_approved = true
       ),
       updated_at = NOW()
       WHERE uid = $1`,
      [pageUid]
    );
    
    console.log(`[CommentService] Updated comment count for page: ${pageUid}`);
  } catch (error) {
    // 记录错误但不抛出，避免影响主业务
    console.error(`[CommentService] Failed to update comment count for ${pageUid}:`, error);
  }
};

/**
 * 获取评论列表
 * @param params 查询参数
 * @returns 评论列表和总数
 */
export const getComments = async (params: GetCommentsParams) => {
  const { page_uid, page = 1, limit = 20, parent_comment_uid = null } = params;
  const offset = (page - 1) * limit;

  try {
    // 构建查询条件
    let whereClause = "WHERE c.page_uid = $1 AND c.is_delete = false AND c.is_approved = true";
    let queryParams: any[] = [page_uid];
    let paramIndex = 2;

    // 如果指定了父评论ID，则查询回复
    if (parent_comment_uid) {
      whereClause += ` AND c.parent_comment_uid = $${paramIndex}`;
      queryParams.push(parent_comment_uid);
      paramIndex++;

      // 查询回复列表
      const commentsQuery = `
        SELECT
          c.*,
          COALESCE(c.user_name, ui.name) as user_name,
          COALESCE(c.user_image, ui.image) as user_image
        FROM comments c
        LEFT JOIN user_info ui ON c.user_id = ui.user_id
        ${whereClause}
        ORDER BY c.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);
      const { rows: comments } = await db.query(commentsQuery, queryParams);

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM comments c
        ${whereClause}
      `;
      const { rows: countResult } = await db.query(countQuery, queryParams.slice(0, -2));
      const total = parseInt(countResult[0].total);

      return {
        success: true,
        data: {
          comments,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    } else {
      // 查询顶级评论和所有回复
      const allCommentsQuery = `
        SELECT
          c.*,
          COALESCE(c.user_name, ui.name) as user_name,
          COALESCE(c.user_image, ui.image) as user_image
        FROM comments c
        LEFT JOIN user_info ui ON c.user_id = ui.user_id
        ${whereClause}
        ORDER BY
          CASE WHEN c.parent_comment_uid IS NULL THEN c.created_at ELSE
            (SELECT created_at FROM comments WHERE uid = c.parent_comment_uid)
          END DESC,
          c.parent_comment_uid NULLS FIRST,
          c.created_at ASC
      `;

      const { rows: allComments } = await db.query(allCommentsQuery, [page_uid]);

      // 分页处理顶级评论
      const topLevelCommentsQuery = `
        SELECT
          c.*,
          COALESCE(c.user_name, ui.name) as user_name,
          COALESCE(c.user_image, ui.image) as user_image
        FROM comments c
        LEFT JOIN user_info ui ON c.user_id = ui.user_id
        WHERE c.page_uid = $1 AND c.is_delete = false AND c.is_approved = true AND c.parent_comment_uid IS NULL
        ORDER BY c.created_at DESC
        LIMIT $2 OFFSET $3
      `;

      const { rows: topLevelComments } = await db.query(topLevelCommentsQuery, [page_uid, limit, offset]);

      // 获取这些顶级评论的所有回复
      const topLevelUids = topLevelComments.map(c => c.uid);
      let replies: any[] = [];

      if (topLevelUids.length > 0) {
        const repliesQuery = `
          SELECT
            c.*,
            COALESCE(c.user_name, ui.name) as user_name,
            COALESCE(c.user_image, ui.image) as user_image
          FROM comments c
          LEFT JOIN user_info ui ON c.user_id = ui.user_id
          WHERE c.page_uid = $1 AND c.is_delete = false AND c.is_approved = true
            AND c.parent_comment_uid = ANY($2)
          ORDER BY c.created_at ASC
        `;

        const { rows: repliesResult } = await db.query(repliesQuery, [page_uid, topLevelUids]);
        replies = repliesResult;
      }

      // 组合评论和回复
      const comments = [];
      for (const topComment of topLevelComments) {
        comments.push(topComment);
        const commentReplies = replies.filter(r => r.parent_comment_uid === topComment.uid);
        comments.push(...commentReplies);
      }

      // 查询顶级评论总数
      const countQuery = `
        SELECT COUNT(*) as total
        FROM comments c
        WHERE c.page_uid = $1 AND c.is_delete = false AND c.is_approved = true AND c.parent_comment_uid IS NULL
      `;
      const { rows: countResult } = await db.query(countQuery, [page_uid]);
      const total = parseInt(countResult[0].total);

      return {
        success: true,
        data: {
          comments,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };
    }
  } catch (error) {
    console.error('Failed to get comments list:', error);
    return {
      success: false,
      error: 'Failed to get comments list'
    };
  }
};

/**
 * 创建新评论
 * @param params 评论参数
 * @returns 创建结果
 */
export const createComment = async (params: CreateCommentParams) => {
  const { page_uid, parent_comment_uid, user_id, content, user_name, user_image } = params;
  const uid = generateNewUID();

  try {
    // 开始事务
    await db.query('BEGIN');

    // 插入新评论
    const insertQuery = `
      INSERT INTO comments (
        uid, page_uid, parent_comment_uid, user_id, content, 
        user_name, user_image, is_delete, is_approved, reply_count
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, false, true, 0)
      RETURNING *
    `;
    
    const { rows: newComment } = await db.query(insertQuery, [
      uid, page_uid, parent_comment_uid || null, user_id, content, user_name, user_image
    ]);

    // 如果是回复，更新父评论的回复数量
    if (parent_comment_uid) {
      await db.query(
        'UPDATE comments SET reply_count = reply_count + 1 WHERE uid = $1',
        [parent_comment_uid]
      );
    }

    // 更新页面评论统计
    await updateCommentCount(page_uid);

    // 提交事务
    await db.query('COMMIT');

    return {
      success: true,
      data: newComment[0]
    };
  } catch (error) {
    // 回滚事务
    await db.query('ROLLBACK');
    console.error('Failed to create comment:', error);
    return {
      success: false,
      error: 'Failed to create comment'
    };
  }
};

/**
 * 软删除评论
 * @param uid 评论UID
 * @param user_id 操作用户ID
 * @param is_admin 是否为管理员
 * @returns 删除结果
 */
export const deleteComment = async (uid: string, user_id: string, is_admin: boolean = false) => {
  try {
    // 开始事务
    await db.query('BEGIN');

    // 查询评论信息
    const { rows: comments } = await db.query(
      'SELECT * FROM comments WHERE uid = $1 AND is_delete = false',
      [uid]
    );

    if (comments.length === 0) {
      await db.query('ROLLBACK');
      return {
        success: false,
        error: 'Comment not found'
      };
    }

    const comment = comments[0];

    // 权限检查：只有评论作者或管理员可以删除
    if (comment.user_id !== user_id && !is_admin) {
      await db.query('ROLLBACK');
      return {
        success: false,
        error: 'No permission to delete this comment'
      };
    }

    // 软删除评论
    await db.query(
      'UPDATE comments SET is_delete = true, updated_at = NOW() WHERE uid = $1',
      [uid]
    );

    // 如果是回复，减少父评论的回复数量
    if (comment.parent_comment_uid) {
      await db.query(
        'UPDATE comments SET reply_count = GREATEST(reply_count - 1, 0) WHERE uid = $1',
        [comment.parent_comment_uid]
      );
    }

    // 更新页面评论统计
    await updateCommentCount(comment.page_uid);

    // 提交事务
    await db.query('COMMIT');

    return {
      success: true,
      message: 'Comment deleted successfully'
    };
  } catch (error) {
    // 回滚事务
    await db.query('ROLLBACK');
    console.error('Failed to delete comment:', error);
    return {
      success: false,
      error: 'Failed to delete comment'
    };
  }
};

/**
 * 根据UID获取单个评论
 * @param uid 评论UID
 * @returns 评论信息
 */
export const getCommentByUid = async (uid: string) => {
  try {
    const { rows: comments } = await db.query(
      `SELECT
        c.*,
        COALESCE(c.user_name, ui.name) as user_name,
        COALESCE(c.user_image, ui.image) as user_image
      FROM comments c
      LEFT JOIN user_info ui ON c.user_id = ui.user_id
      WHERE c.uid = $1 AND c.is_delete = false`,
      [uid]
    );

    if (comments.length === 0) {
      return {
        success: false,
        error: 'Comment not found'
      };
    }

    return {
      success: true,
      data: comments[0]
    };
  } catch (error) {
    console.error('Failed to get comment:', error);
    return {
      success: false,
      error: 'Failed to get comment'
    };
  }
};

/**
 * 获取用户的评论列表
 * @param user_id 用户ID
 * @param page 页码
 * @param limit 每页数量
 * @returns 用户评论列表
 */
export const getUserComments = async (user_id: string, page: number = 1, limit: number = 20) => {
  const offset = (page - 1) * limit;

  try {
    const { rows: comments } = await db.query(
      `SELECT c.*, p.title as page_title, p.slug as page_slug
      FROM comments c
      LEFT JOIN pages p ON c.page_uid = p.uid
      WHERE c.user_id = $1 AND c.is_delete = false
      ORDER BY c.created_at DESC
      LIMIT $2 OFFSET $3`,
      [user_id, limit, offset]
    );

    const { rows: countResult } = await db.query(
      'SELECT COUNT(*) as total FROM comments WHERE user_id = $1 AND is_delete = false',
      [user_id]
    );
    const total = parseInt(countResult[0].total);

    return {
      success: true,
      data: {
        comments,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    console.error('Failed to get user comments:', error);
    return {
      success: false,
      error: 'Failed to get user comments'
    };
  }
};
