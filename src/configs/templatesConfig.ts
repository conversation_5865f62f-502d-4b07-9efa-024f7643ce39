// 可用模板配置
export const availableTemplates = [
  {
    key: 'default',
    name: '默认模板',
    description: '标准游戏落地页模板'
  },
  {
    key: 'test',
    name: '测试模板',
    description: '测试用模板'
  },
  {
    key: 'tinyfun',
    name: 'Tinyfun 模板',
    description: 'Tinyfun 模板'
  }
];

// 同步 import 常用模板
import DefaultTemplate from '~/components/templates/default';
import TestTemplate from '~/components/templates/test';
import TinyfunTemplate from '~/components/templates/tinyfun';

// 模板组件映射（同步为主，异步为辅）
export const templateComponents = {
  default: DefaultTemplate,
  test: TestTemplate,
  tinyfun: TinyfunTemplate,
};

/**
 * 获取所有可用模板的键值对映射
 * @returns 模板key到名称的映射
 */
export function getTemplateKeyNameMap(): Record<string, string> {
  const map: Record<string, string> = {};
  availableTemplates.forEach(template => {
    map[template.key] = template.name;
  });
  return map;
}

/**
 * 获取默认模板key
 * @returns 默认模板的key
 */
export const getDefaultTemplateKey = () => {
  return 'default';
}; 