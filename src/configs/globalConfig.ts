//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
//-------------------------通用配置---------------------------------
//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-

import { fileSaveWorker } from "./workerConfig";

// 支持邮箱
export const supportEmail = process.env.NEXT_PUBLIC_SUPPORT_EMAIL;
// 站点名称配置
export const siteNameConfig = process.env.NEXT_PUBLIC_SITE_NAME_CONFIG;
// 政策生效日期
export const policyEffectiveDate = process.env.NEXT_PUBLIC_POLICY_EFFECTIVE_DATE;
// 域名 https
export const domainNameHttps = process.env.NEXT_PUBLIC_DOMAIN_NAME_HTTPS;
// 版权
export const copyright = process.env.NEXT_PUBLIC_COPYRIGHT;

// domain name
export const domainName = siteNameConfig;

// 域名小写
export const domainNameLowercase = process.env.NEXT_PUBLIC_DOMAIN_NAME_LOWERCASE;

//-=-=-=-=-=-=-=-=-=-=-每页显示数据条数配置=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
// 首页显示数据条数
export const indexPageSize = 8;
// 发现页显示数据条数
export const discoverPageSize = 20;
// 我的作品每页显示条数
export const myPageSize = 16;

// 管理后台用户每页显示条数
export const manageUserSize = 10;
// 管理后台订单每页显示条数
export const manageOrderSize = 10;
// 管理后台用户生成数据每页显示条数
export const manageUserWorksSize = 10;

//-=-=-=-=-=-=-=-=-=-=-路由配置=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
// 是否多语言，即是否翻译好了除开默认语言（如英语）的其他语言
export const isMultiLanguage = false;

// 用户前端路由配置
// 发现页
// 是否展示发现页，false 代表不展示，true 代表展示
export const isDiscoverOpen = false;
// 发现页路由
export const discoverPage = "discover";
// 详情页路由
export const detailPage = "detail";

// 作品页
// 是否展示作品页，false 代表不展示，true 代表展示
export const isMyOpen = false;
// 作品页路由
export const myPage = "my";

// 生成页
// 是否展示生成页，false 代表不展示，true 代表展示
export const isGeneratorOpen = false;
// 生成页路由
export const generatorPage = "generator";

// 邀请页
// 是否展示邀请页，false 代表不展示，true 代表展示
export const isInviteOpen = false;
// 邀请页路由
export const invitePage = "invite";
// 我的邀请记录
// 是否展示我的邀请记录，false 代表不展示，true 代表展示
export const isMyInviteOpen = false;
// 我的邀请记录路由
export const myInvitePage = "myinvite";
// page 页面路由
export const pagePage = "g";

// 管理后台
// 用户管理页面
export const manageUserPage = "manage/user";
// stripe 支付成功页面
export const manageStripePaySuccessPage = "manage/stripePaySuccess";
// paddle 支付成功页面
export const managePaddlePaySuccessPage = "manage/paddlePaySuccess";
// paypro 支付成功页面
export const managePayproPaySuccessPage = "manage/payproPaySuccess";
// 用户生成数据管理页面
export const manageUserWorksPage = "manage/userVideo";
// 游戏管理页面
export const manageLandingPage = "manage/page";
// section管理页面
export const manageSectionPage = "manage/section";
// 基础配置管理页面
export const manageBaseConfigPage = "manage/baseConfig";
// Header管理页面
export const manageHeaderPage = "manage/baseConfig/header";

// blog管理页面 写文章
export const manageBlogWriterPage = "manage/blog/writer";
// blog管理页面 文章列表
export const manageBlogListPage = "manage/blog/list";

// 博客页面路由
export const blogPage = "blog";

// 次数配置
// 免费生成次数
export const freeGenerateTimes = 0;
// welcome 页面免费生成次数
export const welcomeGenerateTimes = 0;
// 邀请注册，给邀请人增加可用次数
export const inviteAddTimes = 2;
// 从邀请链接注册，给注册人增加可用次数
export const inviteRegisterAddTimes = 2;
// 分享到twitter，给分享人增加可用次数
export const shareTwitterAddTimes = 2;
// 分享到facebook，给分享人增加可用次数
export const shareFacebookAddTimes = 2;
// 注册人首次生成视频，给分享人增加可用次数
export const registerGenerateInviteAddTimes = 2;

// 未登录用户免费生成次数
export const notLoginFreeUseTimes = 1;

// -------------------------------------------------------------------------------------------------
// -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=
// -------------------------------------------------------------------------------------------------

//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
//----------------根据环境变量获取的配置，本地和生产环境不一样---------------------------------
//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
// 数据库配置
// 主要数据库配置 URL
export const postgresUrl = process.env.POSTGRES_URL;
// 共享数据库查询条件的域名
export const shareDataDomainName = domainNameLowercase;
// 共享数据库配置 URL
export const sharedDataPostgresUrl = "***************************************************/share_data";
// 是否使用数据库，0 代表不使用数据库，1 代表使用数据库， 默认为 0
export const useDatabase = process.env.NEXT_PUBLIC_USE_DATABASE === "1";
// 是否使用共享数据库，0 代表不使用共享数据库，1 代表使用共享数据库， 默认为 0
export const useShareDatabase = process.env.NEXT_PUBLIC_USE_SHARE_DATABASE === "1";

// 是否检查登录，需要配置文件配置好对应的谷歌 key
export const checkLogin = process.env.NEXT_PUBLIC_CHECK_GOOGLE_LOGIN === "1";
// google client id
export const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
// google secret id
export const googleSecretId = process.env.GOOGLE_SECRET_ID;

// website url
export const websiteUrl = process.env.NEXT_PUBLIC_SITE_URL;

// NEXTAUTH_URL
export const nextauthUrl = process.env.NEXTAUTH_URL;
// NEXTAUTH_SECRET
export const nextauthSecret = process.env.NEXTAUTH_SECRET;

// Analytics
// 是否开启谷歌分析
export const googleAnalyticsOpen = process.env.NEXT_PUBLIC_GOOGLE_OPEN === "1";
// 谷歌分析 ID
export const googleAnalyticsId = process.env.NEXT_PUBLIC_GOOGLE_TAG_ID;
// 是否开启百度分析
export const baiduAnalyticsOpen = process.env.NEXT_PUBLIC_BAIDU_OPEN === "1";
// 百度分析 ID
export const baiduAnalyticsId = process.env.NEXT_PUBLIC_BAIDU_ID;
// 是否开启 Plausible 分析
export const plausibleAnalyticsOpen = process.env.NEXT_PUBLIC_PLAUSIBLE_OPEN === "1";
// Plausible Server Domain
export const plausibleServerDomain = process.env.NEXT_PUBLIC_PLAUSIBLE_SERVER_DOMAIN || "click.pageview.click";
// 是否开启 Clarity 分析
export const clarityAnalyticsOpen = process.env.NEXT_PUBLIC_CLARITY_OPEN === "1";
// Clarity ID
export const clarityId = process.env.NEXT_PUBLIC_CLARITY_ID;

// Google ads ID
// 是否开启谷歌广告
export const googleAdsOpen = process.env.NEXT_PUBLIC_GOOGLE_ADS_OPEN === "1";
// 谷歌广告 ID
export const googleAdsId = process.env.NEXT_PUBLIC_GOOGLE_ADS_ID;
// 是否开启谷歌投放转化记录
export const googleAwOpen = process.env.NEXT_PUBLIC_GOOGLE_AW_OPEN === "1";
// 谷歌投放转化记录 ID
export const googleAwId = process.env.NEXT_PUBLIC_GOOGLE_AW_ID;

// 是否检查可用次数，不检查，也应该判断次数，只是不返回需要支付
export const checkAvailableTime = process.env.NEXT_PUBLIC_CHECK_AVAILABLE_TIME === "1";
// 支付方式 0 代表一次性付费，2 代表订阅付费
export const pricingType = process.env.NEXT_PUBLIC_PRICING_TYPE;
// 支付通道 0 代表 stripe，1 代表 paddle
export const pricingWay = process.env.NEXT_PUBLIC_PRICING_WAY;

// stripe 配置
export const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
// stripe 密钥
export const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
// stripe webhook 密钥
export const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// paddle 配置
// 是否是沙盒环境
export const paddleSandbox = process.env.NEXT_PUBLIC_PADDLE_SANDBOX === "true";
// paddle 客户端令牌
export const paddleClientToken = process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN;
// paddle 卖家 ID
export const paddleSellerId = Number(process.env.NEXT_PUBLIC_PADDLE_SELLER_ID);
// paddle webhook 密钥
export const paddleWebhookSecret = process.env.PADDLE_WEBHOOK_SECRET;
// paddle API 密钥
export const paddleApiKey = process.env.PADDLE_API_KEY;

// cloudflare R2 配置
export const storageUrl = process.env.NEXT_PUBLIC_STORAGE_URL;
// cloudflare R2 域名
export const storageDomain = process.env.STORAGE_DOMAIN;
// cloudflare R2 存储桶
export const r2Bucket = process.env.R2_BUCKET;
// cloudflare R2 账户 ID
export const r2AccountId = process.env.R2_ACCOUNT_ID;
// cloudflare R2 端点
export const r2Endpoint = process.env.R2_ENDPOINT;
// cloudflare R2 令牌值
export const r2TokenValue = process.env.R2_TOKEN_VALUE;
// cloudflare R2 访问密钥 ID
export const r2AccessKeyId = process.env.R2_ACCESS_KEY_ID;
// cloudflare R2 密钥
export const r2SecretAccessKey = process.env.R2_SECRET_ACCESS_KEY;

// openai 配置，也可以配置第三方中转api，比如 openrouter
export const openaiApiKey = process.env.OPENAI_API_KEY;
// openai api 基础 URL
export const openaiApiBaseUrl = process.env.OPENAI_API_BASE_URL;
// openai 模型
export const openaiApiModel = process.env.OPENAI_API_MODEL || "gpt-4o-mini";
// openrouter 配置
// openrouter 配置网站URL
export const openrouterConfigSiteUrl = process.env.OPENROUTER_CONFIG_SITE_URL;
// openrouter 配置网站名称
export const openrouterConfigSiteName = process.env.OPENROUTER_CONFIG_SITE_NAME;

// replicate 配置
// replicate api token
export const replicateApiToken = process.env.REPLICATE_API_TOKEN;
// replicate api，指定用哪个模型的对应版本
export const replicateApiVersion = process.env.REPLICATE_API_VERSION;

// 企业微信通知 URL
export const wecomNotifyUrl = process.env.NOTIFY_QIYE_WEIXIN_URL;

// webhook 配置
export const handleWebhook = process.env.HANDLE_WEBHOOK;

// 保存文件的 worker，已支持上传文件至指定的已存在 bucket
export const saveFileWorker = fileSaveWorker;

export const initPageJson = [];
