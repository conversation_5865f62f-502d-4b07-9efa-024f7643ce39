// Configuration for available prompts
// This avoids the need for a database table

export const availablePrompts = [
  {
    key: 'seo_landing_page_v1',
    name: 'SEO落地页 v1',
    description: '生成优化的SEO游戏落地页'
  }
];

// 各提示模板内容
export const promptTemplates = {
  seo_landing_page_v1: `Please create an optimized SEO landing page refer to the provided content and avoid repetition. The primary keyword is "{{KEYWORD}}". Ensure that the content reads naturally, avoiding any AI-generated tone. Write as a native English speaker with SEO expertise. Follow the specified JSON format for the content generation as described below:
  {
  "name": "",
  "title": "",
  "description": "",
  "h1": "",
  "introduction": {
    "h2": "",
    "content": ""
  },
  "features": {
    "h2": "",
    "content": [
      {
        "h3": "",
        "content": ""
      }
    ]
  },
  "how": {
    "h2": "",
    "content": [
      {
        "h3": "",
        "content": ""
      }
    ]
  },
  "tips": {
    "h2": "",
    "content": [
      {
        "h3": "",
        "content": ""
      }
    ]
  },
  "faq": {
    "h2": "",
    "content": [
      {
        "h3": "",
        "content": ""
      }
    ]
  },
  "cta": {
    "h2": "",
    "content": "",
    "buttonText": ""
  }
}
Instructions:
1. Title: Craft a title within 60 characters that includes the keyword, "free," and "online."
2. Description: Write a description in 20 words with the keyword, "free," and "online."
3. H1 Tag: Craft an H1 heading with the keyword, "free," and "online."
4. Content Structure:
   a. Features Section: List four H3 points that detail the game's features.
   b. How to Play Section: Outline four H3 points explaining how to play the game.
   c. Tips Section: Provide four H3 points with tips for players.
   d. FAQ Section: Create eight H3 points addressing frequently asked questions.
5. Intro and Call to Action: Write an engaging introduction and call-to-action (around 100 words each) to attract and encourage readers.
6. H2/H3 Titles: Ensure all H2 and H3 titles include the keyword and are concise.
7. H3 Content: Expand each H3 content section to 40-50 words, including the keyword once.
8. Button Text: Format as "Play {keyword} Now!"
9. Clarity and Engagement: Eliminate redundancy, ensure content is clear, engaging, and flows naturally.`,
};

/**
 * 获取指定key的prompt模板，替换关键词
 * @param promptKey - 提示模板的key
 * @param keyword - 游戏关键词
 * @returns 替换关键词后的提示模板
 */
export function getPromptWithKeyword(promptKey: string, keyword: string): string {
  const template = promptTemplates[promptKey];
  if (!template) {
    console.warn(`Prompt template not found for key: ${promptKey}`);
    return '';
  }
  
  return template.replace(/{{KEYWORD}}/g, keyword);
}

/**
 * 获取所有可用提示的键值对映射
 * @returns 提示key到名称的映射
 */
export function getPromptKeyNameMap(): Record<string, string> {
  const map: Record<string, string> = {};
  availablePrompts.forEach(prompt => {
    map[prompt.key] = prompt.name;
  });
  return map;
}

export const getDefaultPromptKey = () => {
  return 'seo_landing_page_v1';
}; 