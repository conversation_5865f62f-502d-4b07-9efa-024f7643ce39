import { pricingType, pricingWay } from "../globalConfig";

// 获取支付方式，一次性付费还是订阅付费，
export const getPricingType = () => {
  return pricingType;
};

// 获取支付通道，stripe 或者 paddle
export const getPricingWay = () => {
  return pricingWay;
};

// 获取价格字符串
export const getPriceString = (price) => {
  const priceString = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: price.currency!,
    minimumFractionDigits: 0,
  }).format((price?.unit_amount || 0) / 100);
  return priceString;
};

// 获取每次价格字符串
export const getPricePerString = (price) => {
  const perPrice = (price?.unit_amount || 0) / 100 / price.availableTimes;
  const pricePerString = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: price.currency!,
    minimumFractionDigits: 2,
  }).format(perPrice);
  return pricePerString;
};

// 获取每月价格字符串
export const getPricePerMonthString = (price) => {
  const perPrice = (price?.unit_amount || 0) / 100 / 12;
  const pricePerMonthString = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: price.currency!,
    minimumFractionDigits: 2,
  }).format(perPrice);
  return pricePerMonthString;
};
