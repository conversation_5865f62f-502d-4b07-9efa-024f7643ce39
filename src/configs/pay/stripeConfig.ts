// 可用次数
export const availableTimes = [200, 30, 10, 2];

export const downloadTimes = [-1, 60, 20, 10];

// 价格，到分
export const pricesValue = [2990, 990, 490, 190];

const priceProd = [
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 2990,
    id: "price_1Q130YFs9vmMPWBBIWyWHtlg",
    availableTimes: 200,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 990,
    id: "price_1Q130YFs9vmMPWBBtX6Mynew",
    availableTimes: 30,
    downloadTimes: 60,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 490,
    id: "price_1Q130YFs9vmMPWBBOKX1hwnb",
    availableTimes: 10,
    downloadTimes: 20,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 190,
    id: "price_1Q130YFs9vmMPWBBcXzIq5bd",
    availableTimes: 2,
    downloadTimes: 10,
  },
];

const priceTest = [
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 2990,
    id: "price_1Q12yiFs9vmMPWBBzkkpAJFe",
    availableTimes: 200,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 990,
    id: "price_1Q12yiFs9vmMPWBBg2QemQz7",
    availableTimes: 30,
    downloadTimes: 60,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 490,
    id: "price_1Q12yiFs9vmMPWBBtvgXJcZo",
    availableTimes: 10,
    downloadTimes: 20,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 190,
    id: "price_1Q12yiFs9vmMPWBBDgzL1mE8",
    availableTimes: 2,
    downloadTimes: 10,
  },
];

export const priceList = process.env.NODE_ENV === "production" ? priceProd : priceTest;

/**
 * 订阅产品价格，生产环境
 */
const subscription_priceProd = [
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 2990,
    id: "price_1Pt5m4Altr6kaHrbiZnGl4cL",
    availableTimes: 60,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 990,
    id: "price_1Pt5m4Altr6kaHrbkczwAi9A",
    availableTimes: 10,
    downloadTimes: 20,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 190,
    id: "price_1Pt5m4Altr6kaHrbS7x15hmj",
    availableTimes: 2,
    downloadTimes: 10,
  },
];

/**
 * 订阅产品价格，测试环境
 */
const subscription_priceTest = [
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 2990,
    id: "price_1Pt2KuAltr6kaHrbC7X6z413",
    availableTimes: 60,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 990,
    id: "price_1Pt2KuAltr6kaHrbzCnXYJjh",
    availableTimes: 10,
    downloadTimes: 20,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 190,
    id: "price_1Pt2KuAltr6kaHrbJgT35u1B",
    availableTimes: 2,
    downloadTimes: 10,
  },
];

export const subscription_priceList = process.env.NODE_ENV === "production" ? subscription_priceProd : subscription_priceTest;

/**
 * 根据价格 id 获取订阅产品价格
 */
export const getSubscriptionPrice = (priceId) => {
  return subscription_priceList.find((price) => price.id === priceId);
};
