import { Page<PERSON>onte<PERSON>, Page } from '~/lib/api/pages';

export interface NavigationLink {
  label: string;
  url: string;
  target?: string;
}

export interface RecommendationZoneConfig {
  id: string;
  name: string;
}

export interface SiteConfig {
  features?: {
    allowUserComments: boolean;
    allowForwardFunction: boolean;
  };
  navigation_links?: NavigationLink[];
  recommendationZones?: RecommendationZoneConfig[];
  // 其他配置项...
}

export interface Site {
  id: number;
  uid: string;
  name: string;
  domain: string;
  config: SiteConfig;
  default_template_key?: string;
  default_llm_model?: string;
  default_llm_prompt_key?: string;
  created_at: string;
  updated_at: string;
}

export interface SiteData {
  site: Site;
  page: Page;
  recommendedGames?: any[];
  recommendationZones?: any[];
} 