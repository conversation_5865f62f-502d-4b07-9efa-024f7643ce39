'use client';
import { createContext, useContext, useEffect, useRef, useState } from "react";
import { useSession } from "next-auth/react";
import { useInterval } from "ahooks";
import { getPricingWay } from "~/configs/pay/payUtil";
import { checkLogin } from "~/configs/globalConfig";
import { isMobile } from "~/utils/strUtil";

const CommonContext = createContext(undefined);

export const CommonProvider = ({
  children,
  commonText,
  authText,
  menuText,
  pricingText,
  footerLink,
}) => {
  const { data: session, status } = useSession();
  const [userData, setUserData] = useState({});
  const [intervalUserData, setIntervalUserData] = useState(1000);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showLoadingModal, setShowLoadingModal] = useState(false);
  const [showGeneratingModal, setShowGeneratingModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showManageBilling, setShowManageBilling] = useState(false);
  const [showPaddleCancelToastText, setShowPaddleCancelToastText] = useState(false);

  const [showPayModal, setShowPayModal] = useState(false);
  const [showPayProCheckoutModal, setShowPayProCheckoutModal] = useState(false);

  const [mobile, setMobile] = useState(false);

  useInterval(() => {
    if (checkLogin) {
      init();
    }
  }, intervalUserData);

  async function init() {
    if (status == "authenticated") {
      const userData = {
        name: session?.user?.name,
        email: session?.user?.email,
        image: session?.user?.image,
        // @ts-ignore
        user_id: session?.user?.user_id,
        // @ts-ignore
        available_times: session?.user?.available_times,
        // @ts-ignore
        download_times: session?.user?.download_times,
        // @ts-ignore
        stripe_customer_id: session?.user?.stripe_customer_id,
        // @ts-ignore
        paddle_customer_id: session?.user?.paddle_customer_id,
        // @ts-ignore
        paypro_customer_id: session?.user?.paypro_customer_id,
        subscribe_status: false,
        // @ts-ignore
        role: session?.user?.role,
      };
      setUserData(userData);
      setShowLoginModal(false);
      setIntervalUserData(undefined);
      await getUserByEmail(userData.email);
      await checkSubscribe(userData);
    }
  }

  const getUserByEmail = async (email) => {
    const login_from = sessionStorage.getItem("login_from");
    const code = sessionStorage.getItem("code");
    if (login_from || code) {
      sessionStorage.removeItem("login_from");
      sessionStorage.removeItem("code");
      const requestData = {
        email: email,
        login_from: login_from,
      };
      const response = await fetch(`/api/user/getUserByEmail`, {
        method: "POST",
        body: JSON.stringify(requestData),
      });
      if (response.status != 200) {
        return;
      }
      const result = await response.json();
      setIntervalAvailableTimes(1000);
    }
  };

  const checkSubscribe = async (currUserData) => {
    if (getPricingWay() == '0' || getPricingWay() == '2') {
      const response = await fetch(`/api/stripe/checkSubscribe`);
      const res = await response.json();
      if (res.status == 0) {
        if (res.checkResult) {
          setShowManageBilling(true);
          setUserData({
            ...currUserData,
            subscribe_status: true,
          });
        }
      }
    } else if (getPricingWay() == '1') {
      const response = await fetch(`/api/paddle/checkSubscribe`);
      const res = await response.json();
      if (res.status == 0) {
        if (res.checkResult?.subscribe_status) {
          setShowManageBilling(true);
          setUserData({
            ...currUserData,
            subscribe_status: true,
          });
          if (res.checkResult?.next_billed_at) {
            setShowPaddleCancelToastText(false);
          } else {
            setShowPaddleCancelToastText(true);
          }
        }
      }
    } else if (getPricingWay() == '4') {
      const response = await fetch(`/api/paypro/checkSubscribe`);
      const res = await response.json();
      if (res.status == 0) {
        if (res.checkResult?.subscribe_status) {
          setUserData({
            ...currUserData,
            subscribe_status: true,
          });
        }
      }
    }
  }

  const [intervalAvailableTimes, setIntervalAvailableTimes] = useState(undefined);
  const getAvailableTimes = async () => {
    if (!intervalAvailableTimes) {
      return;
    }
    const response = await fetch(`/api/user/getAvailableTimes`);
    const availableTimes = await response.json();
    if (availableTimes.available_times) {
      setUserData({
        ...userData,
        available_times: availableTimes?.available_times,
        download_times: availableTimes?.download_times,
      });
    }
    if (availableTimes.available_times >= 0) {
      setIntervalAvailableTimes(undefined);
    }
  };
  useInterval(() => {
    getAvailableTimes();
  }, intervalAvailableTimes);

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setMobile(isMobile(navigator.userAgent));
    return () => { };
  }, []);

  return (
    <CommonContext.Provider
      value={{
        userData, setUserData,
        showLoginModal, setShowLoginModal,
        showLogoutModal, setShowLogoutModal,
        showLoadingModal, setShowLoadingModal,
        showGeneratingModal, setShowGeneratingModal,
        footerLink,
        commonText,
        authText,
        menuText,
        pricingText,
        showDeleteModal, setShowDeleteModal,
        showManageBilling, setShowManageBilling,
        showPaddleCancelToastText, setShowPaddleCancelToastText,
        setIntervalAvailableTimes,
        showPayModal, setShowPayModal,
        showPayProCheckoutModal, setShowPayProCheckoutModal,
        mobile
      }}
    >
      {children}
    </CommonContext.Provider>
  );

};

export const useCommonContext = () => useContext(CommonContext);