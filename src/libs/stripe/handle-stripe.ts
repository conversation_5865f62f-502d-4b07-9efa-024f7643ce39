import { toDateTime } from "~/utils/helpers";
import { stripe } from "./stripe";
import <PERSON><PERSON> from "stripe";
import { getDb } from "~/libs/db/db";
import { subscriptionComplete } from "~/servers/pay/manageUserTimes";
import { domainNameLowercase } from "~/configs/globalConfig";

const db = getDb();
const createOrRetrieveCustomer = async ({ email, user_id }: { email: string; user_id: string }) => {
  const results = await db.query(
    `
    SELECT * FROM stripe_customers
      where user_id=$1 limit 1
    `,
    [user_id]
  );
  const existUser = results.rows;

  const customerData: { metadata: { user_id: string; domain: string }; email?: string } = {
    metadata: {
      user_id: user_id,
      domain: domainNameLowercase,
    },
  };
  if (email) customerData.email = email;

  if (existUser.length <= 0) {
    // 创建
    const customer = await stripe.customers.create(customerData);
    await db.query(
      `
      insert into stripe_customers(user_id,stripe_customer_id) values($1, $2)
      `,
      [user_id, customer.id]
    );
    return customer.id;
  }
  // 判断 stripe_customer_id 是否存在
  try {
    await stripe.customers.retrieve(existUser[0].stripe_customer_id);
  } catch (e) {
    // 创建新的 stripe 客户
    const customer = await stripe.customers.create(customerData);
    await db.query(
      `
      update stripe_customers set stripe_customer_id=$1 where user_id=$2
      `,
      [customer.id, user_id]
    );
    return customer.id;
  }
  return existUser[0].stripe_customer_id;
};

const manageSubscriptionStatusChange = async (subscriptionId: string, customerId: string) => {
  // Get customer's UUID from mapping table.
  const results = await db.query(
    `
    SELECT * FROM stripe_customers where stripe_customer_id=$1 limit 1
    `,
    [customerId]
  );
  const existCustomer = results.rows;
  if (existCustomer.length <= 0) {
    return;
  }
  const customerData = existCustomer[0];

  const { user_id: user_id } = customerData!;

  const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ["default_payment_method"],
  });
  // Upsert the latest status of the subscription object.
  const subscriptionData = {
    id: subscription.id,
    user_id: user_id,
    metadata: subscription.metadata,
    status: subscription.status,
    price_id: subscription.items.data[0].price.id,
    // @ts-ignore
    quantity: subscription.quantity,
    cancel_at_period_end: subscription.cancel_at_period_end,
    cancel_at: subscription.cancel_at ? toDateTime(subscription.cancel_at).toISOString() : null,
    canceled_at: subscription.canceled_at ? toDateTime(subscription.canceled_at).toISOString() : null,
    current_period_start: toDateTime(subscription.current_period_start).toISOString(),
    current_period_end: toDateTime(subscription.current_period_end).toISOString(),
    created: toDateTime(subscription.created).toISOString(),
    ended_at: subscription.ended_at ? toDateTime(subscription.ended_at).toISOString() : null,
    trial_start: subscription.trial_start ? toDateTime(subscription.trial_start).toISOString() : null,
    trial_end: subscription.trial_end ? toDateTime(subscription.trial_end).toISOString() : null,
  };

  const resultSubs = await db.query(
    `
    SELECT * FROM stripe_subscriptions where user_id=$1 limit 1
    `,
    [subscriptionData.user_id]
  );
  const existSubs = resultSubs.rows;
  if (existSubs.length <= 0) {
    // 没有，新增
    await db.query(
      `
      insert into stripe_subscriptions(id,user_id,metadata,status,price_id,quantity,cancel_at_period_end,cancel_at,canceled_at,current_period_start,current_period_end,created,ended_at,trial_start,trial_end)
        values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15)
      `,
      [
        subscriptionData.id,
        subscriptionData.user_id,
        subscriptionData.metadata,
        subscriptionData.status,
        subscriptionData.price_id,
        subscriptionData.quantity,
        subscriptionData.cancel_at_period_end,
        subscriptionData.cancel_at,
        subscriptionData.canceled_at,
        subscriptionData.current_period_start,
        subscriptionData.current_period_end,
        subscriptionData.created,
        subscriptionData.ended_at,
        subscriptionData.trial_start,
        subscriptionData.trial_end,
      ]
    );
    // 新增订阅，需要处理订阅次数
    await subscriptionComplete(subscription);
  } else {
    if (subscription.status == "incomplete_expired") {
      return;
    }
    // 有，更新
    await db.query(
      `
      update stripe_subscriptions set
        id=$2,metadata=$3,status=$4,price_id=$5,quantity=$6,
        cancel_at_period_end=$7,cancel_at=$8,canceled_at=$9,
        current_period_start=$10,current_period_end=$11,created=$12,
        ended_at=$13,trial_start=$14,trial_end=$15 where user_id=$1
      `,
      [
        subscriptionData.user_id,
        subscriptionData.id,
        subscriptionData.metadata,
        subscriptionData.status,
        subscriptionData.price_id,
        subscriptionData.quantity,
        subscriptionData.cancel_at_period_end,
        subscriptionData.cancel_at,
        subscriptionData.canceled_at,
        subscriptionData.current_period_start,
        subscriptionData.current_period_end,
        subscriptionData.created,
        subscriptionData.ended_at,
        subscriptionData.trial_start,
        subscriptionData.trial_end,
      ]
    );
    // 判断已存在是否订阅，如果是订阅那么需要判断订阅开始时间是否小于本次开始时间，小于的话是新的一期订阅，就调用处理订阅次数
    const existSub = existSubs[0];
    if (existSub.status === "active" && subscriptionData.status === "active") {
      // 转时间戳
      const existSubStart = new Date(existSub.current_period_start).getTime() / 1000;
      if (existSubStart < subscription.current_period_start) {
        await subscriptionComplete(subscription);
      }
    }
    if (existSub.status != "active" && subscriptionData.status == "active") {
      // 新订阅，需要处理订阅次数
      await subscriptionComplete(subscription);
    }
    if (subscriptionData.status !== "active") {
      // 取消订阅或其他状态，需要处理订阅次数
      await subscriptionComplete(subscription);
    }
  }
};

export { createOrRetrieveCustomer, manageSubscriptionStatusChange };
