import React, { ReactNode } from 'react';
import { Box, Container, Typography, Paper } from '@mui/material';

interface PageLayoutProps {
  children: ReactNode;
  title: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
}

/**
 * A standard page layout component for admin pages
 * @param {ReactNode} children - The page content
 * @param {string} title - The page title
 * @param {string} maxWidth - Maximum width of the container (MUI maxWidth prop)
 */
export default function PageLayout({ children, title, maxWidth = 'lg' }: PageLayoutProps) {
  return (
    <Container maxWidth={maxWidth} sx={{ py: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {title}
        </Typography>
      </Box>
      <Paper elevation={2} sx={{ p: 3 }}>
        {children}
      </Paper>
    </Container>
  );
} 