'use client'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { whiteLoadingSvg } from '~/components/svg';
import { useCommonContext } from '~/context/common-context';
import { useSession } from "next-auth/react";
import { Menu, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { getPricingWay } from "~/configs/pay/payUtil";
import { toast } from "react-toastify";

const LoginButton = ({ buttonType = 0 }) => {
  const router = useRouter();
  const { data: session, status } = useSession();

  const {
    userData,
    setUserData,
    setShowLoginModal,
    setShowLogoutModal,
    setShowLoadingModal,
    menuText,
    authText,
    showManageBilling,
    showPaddleCancelToastText,
    pricingText,
  } = useCommonContext();
  const [loading, setLoading] = useState(false);

  async function login(event) {
    event.preventDefault();
    setLoading(true);
    let _userData;
    if (userData == null || Object.keys(userData).length == 0) {
      if (status == "authenticated") {
        setUserData(session?.user);
        _userData = session?.user;
      }
    } else {
      _userData = userData;
    }

    if (_userData != null && Object.keys(_userData).length != 0) {
      router.refresh();
    } else {
      setShowLoginModal(true);
      setLoading(false);
    }
  }

  async function logout() {
    setShowLogoutModal(true);
  }

  const manageBilling = async () => {
    if (!userData?.user_id) {
      return;
    }
    const user_id = userData?.user_id;
    const requestData = {
      user_id: user_id,
    };
    setShowLoadingModal(true);
    const responseData = await fetch(`/api/stripe/create-portal-link`, {
      method: "POST",
      body: JSON.stringify(requestData),
    });
    const result = await responseData.json();
    if (result.url) {
      window.location.href = result.url;
    }
  };

  const managePaddleBilling = async () => {
    if (showPaddleCancelToastText) {
      toast(pricingText.subscribeHasCanceled, { type: "success" });
      return;
    }
    if (!userData?.user_id) {
      return;
    }
    setShowLoadingModal(true);
    const responseData = await fetch(`/api/paddle/create-portal-link`);
    const result = await responseData.json();
    if (result.cancel_url) {
      window.location.href = result.cancel_url;
    }
  };

  return (
    <>
      {buttonType == 0 && (
        <>
          {loading ? (
            <button
              className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold hover:border-[rgba(255,255,255,0.9)]"
              disabled
            >
              <p>Login</p>
              {whiteLoadingSvg}
            </button>
          ) : (
            <button
              className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold hover:border-[rgba(255,255,255,0.9)]"
              onClick={login}
            >
              {authText.loginText}
            </button>
          )}
        </>
      )}
      {buttonType == 1 && (
        <Menu as="div" className="relative ml-3">
          <div>
            <Menu.Button className="flex rounded-full text-sm">
              <span className="sr-only">Open user menu</span>
              <img className="h-8 w-8 rounded-full" src={userData.image} alt="" />
            </Menu.Button>
          </div>
          <Transition
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="transform opacity-0 scale-95"
            enterTo="transform opacity-100 scale-100"
            leave="transition ease-in duration-75"
            leaveFrom="transform opacity-100 scale-100"
            leaveTo="transform opacity-0 scale-95"
          >
            <Menu.Items className="absolute ltr:left-0 ltr:lg:left-auto ltr:lg:right-0 rtl:right-0 rtl:lg:right-auto rtl:lg:left-0 z-10 mt-4 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none w-auto min-w-max">
              <Menu.Item>
                <div className="flex justify-between px-6 py-2">
                  <p className="text-sm text-gray-600">{userData.email}</p>
                </div>
              </Menu.Item>
              {userData.stripe_customer_id ? (
                <Menu.Item>
                  <a onClick={() => manageBilling()} className={"block px-6 py-2 text-sm header-profile-menu cursor-pointer"}>
                    {menuText.footerSupport1}
                  </a>
                </Menu.Item>
              ) : null}
              {getPricingWay() == "1" && userData.paddle_customer_id && showManageBilling ? (
                <Menu.Item>
                  <a onClick={() => managePaddleBilling()} className={"block px-6 py-2 text-sm header-profile-menu cursor-pointer"}>
                    {menuText.footerSupport2}
                  </a>
                </Menu.Item>
              ) : null}
              <Menu.Item>
                <a onClick={logout} className={"block px-6 py-2 text-sm header-profile-menu cursor-pointer"}>
                  {authText.logoutText}
                </a>
              </Menu.Item>
            </Menu.Items>
          </Transition>
        </Menu>
      )}
    </>
  );
};

export default LoginButton
