import HeadInfo from "~/components/common/HeadInfo";
import GoogleAdsense from "~/components/GoogleAdsense";
import Header from "~/components/common/GameHeader";
import { SiteData } from "~/types/site";
import { ExplorationGamesResult } from "~/lib/api/pages";
// Import reusable landing components
import Introduction from "~/components/landing/Introduction";
import Feature from "~/components/landing/Feature";
import HowToUse from "~/components/landing/HowToUse";
import Tip from "~/components/landing/Tip";
import Faq from "~/components/landing/Faq";
import Cta from "~/components/landing/Cta";
import GameRecommendations from "~/components/landing/GameList";
import IframeCard from "~/components/landing/IframeCard"; // Import IframeCard
import Comments from "~/components/landing/Comments"; // Import Comments
import Footer from "~/components/common/Footer";
import ExplorationContent from "../landing/ExplorationContent";
// RecommendedGameSidebar 不再需要，推荐游戏现在集成在 IframeCard 中
import { RecommendedGame } from "~/components/landing/types"; // Import RecommendedGame type

interface DefaultTemplateProps {
  locale: string;
  pageData: SiteData;
  pagePath: String;
  gamesResult?: ExplorationGamesResult; // 发现页面的游戏数据
}

const DefaultTemplate = ({ locale, pageData, pagePath, gamesResult }: DefaultTemplateProps) => {
  const content = pageData.page.json_content;
  const recommendedGames = pageData.recommendedGames || [];
  const recommendationZones = pageData.recommendationZones || []; // Get recommendation zones

  // 检查是否为发现页面
  const isExplorationPage = pageData.page.is_exploration_page === true;

  // 提取站点名称和导航链接
  const siteName = pageData.site?.name || content.title || 'Game Center';
  const navigationLinks = pageData.site?.config?.navigation_links || [];

  // Group recommended games by zone
  const gamesByZone: { [key: string]: any[] } = {};
  recommendedGames.forEach((game: any) => {
    if (!gamesByZone[game.recommendation_zone]) {
      gamesByZone[game.recommendation_zone] = [];
    }
    gamesByZone[game.recommendation_zone].push(game);
  });

  // Map zone data to GameRecommendations expected props dynamically
  const dynamicRecommendationsInfo: { [zoneName: string]: { title: string; image: string; link: string; }[] } = {};

  recommendationZones.forEach((zone: any) => {
    const gamesInZone = gamesByZone[zone.id] || [];
    if (gamesInZone.length > 0) {
      dynamicRecommendationsInfo[zone.name] = gamesInZone.map((game: any) => ({
        title: game.title || '',
        image: (game.cover_img_url === '' ? null : game.cover_img_url) || null,
        link: game.jump_url || '',
        description: game.description || '',
      }));
    }
  });

  // 合并所有推荐区块的游戏为一个数组
  const allRecommendedGames = Object.values(dynamicRecommendationsInfo).flat();

  // 转换游戏数据格式为 RecommendedGame 类型
  const sidebarGames: RecommendedGame[] = allRecommendedGames.map((game: any) => ({
    title: game.title || '',
    image: game.image || null, // 这里已经在上面正确映射了 cover_img_url
    link: game.link || '',
    description: game.description || '',
  }));

  // 如果没有推荐游戏数据，创建一些测试数据（仅用于开发测试）
  const testGames: RecommendedGame[] = sidebarGames.length === 0 ? [
    {
      title: 'Sample Game 1',
      image: '/icon/favicon.ico',
      link: '#',
      description: 'This is a sample game for testing the sidebar layout.',
    },
    {
      title: 'Sample Game 2',
      image: null,
      link: '#',
      description: 'Another sample game to test the three-column layout.',
    },
    {
      title: 'Sample Game 3',
      image: '/icon/favicon.ico',
      link: '#',
      description: 'Third sample game for sidebar testing.',
    },
  ] : [];

  // 使用实际数据或测试数据
  const displayGames = sidebarGames.length > 0 ? sidebarGames : testGames;

  // 调试信息 - 可以在开发时使用
  console.log('Debug - recommendedGames:', recommendedGames.length);
  console.log('Debug - allRecommendedGames:', allRecommendedGames.length);
  console.log('Debug - sidebarGames:', sidebarGames.length);
  console.log('Debug - displayGames:', displayGames.length, displayGames.slice(0, 2));

  const outerStyle = {
    background: 'rgba(0,0,0,0.18)',
    backgroundImage: 'url("/images/background.jpg")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundAttachment: 'fixed',
    backdropFilter: 'blur(10px)',
    minHeight: '100vh',
    color: '#fff',
    fontFamily: 'Inter, Roboto, Arial, sans-serif',
    padding: '0 16px',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column' as const,
  };

  const sectionStyle = {
    background: 'rgba(0,0,0,0.18)',
    backdropFilter: 'blur(12px)',
    WebkitBackdropFilter: 'blur(12px)',
    boxShadow: 'none',
    borderRadius: '2rem',
    // 主题色
    h1Color: '#FFFFFF',
    h2Color: '#FFFFFF',
    h3Color: '#FFFFFF',
    descriptionColor: '#FFFFFF',
    buttonTextColor: '#FFFFFF',
    buttonBackgroundColor: '#FF9500',
    buttonHoverBackgroundColor: '#E6850E',
    buttonBorderRadius: '12px',
    buttonColor: '#4A90E2',
    titleColor: '#FF9500',
    textColor: '#FFFFFF',
  };

  const cardStyle = {
    maxWidth: 420,
    width: '100%',
    margin: '0 auto',
    borderRadius: '0.5rem',
    boxShadow: 'none',
    background: 'rgba(0,0,0,0.0)',
    border: '1px solid #AAAAAA',
    padding: '1.5rem',
    boxSizing: 'border-box' as const,
  };

  const buttonStyle = {
    background: '#FF9500',
    color: '#FFFFFF',
    borderRadius: '12px',
  };

  // 评论专用样式对象 - 参考 cardStyle 的设计模式
  const commentStyle = {
    // 评论项样式
    backgroundColor: 'rgba(0,0,0,0.15)',
    textColor: '#FFFFFF',
    secondaryTextColor: 'rgba(255, 255, 255, 0.7)',
    timeColor: 'rgba(255, 255, 255, 0.6)',
    padding: '1.5rem',
    margin: '0 0 1.5rem 0',
    borderRadius: '1rem',
    backdropFilter: 'blur(8px)',
    boxShadow: 'none',

    // 评论按钮样式
    buttonBackgroundColor: 'rgba(255, 255, 255, 0.1)',
    buttonTextColor: 'rgba(255, 255, 255, 0.8)',
    buttonHoverColor: '#E6850E',
    buttonBorderRadius: '8px',
    buttonPadding: '6px 12px',
    buttonBorder: 'none',

    // 评论输入框样式
    inputBackgroundColor: 'rgba(255, 255, 255, 0.95)',
    inputBorderColor: 'rgba(255, 255, 255, 0.3)',
    inputTextColor: '#374151',
    inputBorderRadius: '8px',
    inputPadding: '12px',
    inputFontSize: '14px',
    placeholderColor: 'rgba(107, 114, 128, 0.8)',

    // 评论标识样式
    badgeBackgroundColor: 'rgba(59, 130, 246, 0.2)',
    badgeTextColor: '#60A5FA',
    badgeBorderColor: 'rgba(59, 130, 246, 0.3)',
    adminBadgeBackgroundColor: 'rgba(168, 85, 247, 0.2)',
    adminBadgeTextColor: '#C084FC',
    adminBadgeBorderColor: 'rgba(168, 85, 247, 0.3)'
  };

  // 推荐游戏侧边栏样式对象 - 与现有样式保持一致
  const recommendedGameStyle = {
    // 卡片样式
    cardBackgroundColor: 'rgba(0,0,0,0.15)',
    cardBorderRadius: '1rem',
    cardPadding: '1rem',
    cardBackdropFilter: 'blur(8px)',

    // 文本样式
    titleColor: '#FFFFFF',
    titleFontSize: '0.9rem',
    titleFontWeight: '600',
    descriptionColor: 'rgba(255, 255, 255, 0.8)',
    descriptionFontSize: '0.8rem',

    // 按钮样式
    buttonBackgroundColor: '#FF9500',
    buttonTextColor: '#FFFFFF',
    buttonHoverBackgroundColor: '#E6850E',
    buttonBorderRadius: '8px',

    // 侧边栏样式
    sidebarBackgroundColor: 'rgba(0,0,0,0.18)',
    sidebarBorderRadius: '1rem',
    sidebarPadding: '1.5rem',
    sidebarBackdropFilter: 'blur(12px)',
    sidebarTitleColor: '#FFFFFF',
    sidebarTitleFontSize: '1.2rem',
    sidebarTitleFontWeight: '700',
  };

  const sectionClassName = "w-full max-w-6xl mx-auto my-8 sm:my-12 px-2 sm:px-4 py-8 lg:py-12";

  const headerClassName = "mt-5 left-0 w-full z-50 bg-opacity-90 px-8 py-6 flex items-center justify-between shadow-lg";

  return (
    <div style={outerStyle}>
      {/* <div>Template Loaded</div> Temporary text */}
      <HeadInfo
        locale={locale}
        page={pagePath}
        title={content.title}
        description={content.description}
      />
      {/* Render Header component */}
      <Header siteName={siteName} navigationLinks={navigationLinks} styles={sectionStyle} className={headerClassName} />

      {/* 主要内容区域 - 使用flex-grow确保占据剩余空间 */}
      <div style={{ flex: '1 0 auto' }}>
        {/* 条件渲染：发现页面 vs 普通游戏页面 */}
        {isExplorationPage && gamesResult ? (
          /* 发现页面内容 - 保持原有单栏布局 */
          <ExplorationContent
            pageData={pageData}
            gamesResult={gamesResult}
            styles={sectionStyle}
            cardStyle={{...cardStyle, padding: '0rem', border: 'none', background: 'rgba(0,0,0,0.15)'}}
            buttonStyle={buttonStyle}
            className={sectionClassName}
          />
        ) : (
          /* 普通游戏页面内容 - 三栏布局，推荐游戏集成在 IframeCard 中 */
          <>
            {pageData.page.iframe_url && (
              <IframeCard
                landingInfo={{
                  iframeSrc: pageData.page.iframe_url,
                  title: content.name,
                  description: content.description,
                  h1: content.h1,
                }}
                styles={sectionStyle}
                className="" // 移除限制宽度的类名
                recommendedGames={displayGames}
                showRecommendedGames={true}
              />
            )}
          </>
        )}

        {/* 普通游戏页面的其他组件 - 只在非发现页面时显示 */}
        {!isExplorationPage && (
          <>
            {/* Comments Section */}
            <Comments
              landingInfo={{
                pageUid: pageData.page.uid,
                h2: "",
                placeholder: "Share your thoughts and experience...",
                showNewCommentForm: true
              }}
              styles={sectionStyle}
              commentStyle={commentStyle}
              className={sectionClassName}
            />

            {/* 其他内容组件 */}
            {Array.isArray(content.components) && content.components.length > 0 ? (
              content.components.map?.((component: any, index: number) => {
                return null;
              })
            ) : (
              <>
                {content.introduction && <Introduction landingInfo={content.introduction} styles={sectionStyle} className={sectionClassName} />}

                {content.features && <Feature landingInfo={content.features} styles={sectionStyle} cardStyle={cardStyle} className={sectionClassName} />}

                {content.how && <HowToUse landingInfo={content.how} styles={sectionStyle} cardStyle={{ ...cardStyle, maxWidth: 700 }} className={sectionClassName} />}

                {content.tips && <Tip landingInfo={content.tips} styles={sectionStyle} cardStyle={{...cardStyle, maxWidth: 700, }} className={sectionClassName} />}

                {content.faq && <Faq landingInfo={content.faq} styles={sectionStyle} className={sectionClassName} />}

                {content.cta && <Cta landingInfo={content.cta} styles={sectionStyle} className={sectionClassName} />}
              </>
            )}
          </>
        )}

        <GoogleAdsense />
      </div>

      {/* Footer - 使用flex-shrink: 0确保始终显示在底部 */}
      <div style={{ flexShrink: 0 }}>
        <Footer locale={locale} page={pagePath} />
      </div>
    </div>
  );
};

export default DefaultTemplate; 