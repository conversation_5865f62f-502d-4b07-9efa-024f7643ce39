'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Editor } from '@monaco-editor/react';
import { HeaderManage } from '~/components/manage/HeaderManage';
import { Template } from '~/utils/types/game';
import { updateTemplateByUid } from "~/servers/game/templateService";

interface TemplateFormComponentProps {
  locale: string;
  templateUid?: string; // 使用uid作为模板标识符
}

const TemplateFormComponent = ({ locale, templateUid }: TemplateFormComponentProps) => {
  const router = useRouter();
  const isEditing = !!templateUid;

  // Form state
  const [formData, setFormData] = useState<{
    name: string;
    content: string;
    preview_url?: string;
  }>({
    name: '',
    content: '',
    preview_url: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [isFetchingData, setIsFetchingData] = useState(isEditing);

  // Fetch template data when editing
  useEffect(() => {
    if (isEditing && templateUid) {
      setIsFetchingData(true);
      setError('');
      
      fetch(`/api/manage/templates/uid/${templateUid}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: templateUid,
          action: 'get'
        })
      })
        .then(response => response.json())
        .then(result => {
          if (result.status !== 200) {
            throw new Error(result.message || 'Failed to fetch template');
          }
          
          if (!result.data) {
            throw new Error('Template not found');
          }
          
          const template = result.data;
          setFormData({
            name: template.name || '',
            content: template.content || '',
            preview_url: template.preview_url || '',
          });
        })
        .catch(err => {
          console.error('Error fetching template:', err);
          setError(err instanceof Error ? err.message : 'Failed to load template data');
        })
        .finally(() => {
          setIsFetchingData(false);
        });
    }
  }, [isEditing, templateUid]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle editor changes
  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setFormData((prev) => ({
        ...prev,
        content: value,
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSubmitSuccess(false);

    try {
      // Validate form
      if (!formData.name || !formData.content) {
        throw new Error('Name and content are required');
      }

      const url = isEditing
        ? `/api/manage/templates/uid/${templateUid}`
        : '/api/manage/templates';

      const method = 'POST';

      const body = isEditing
        ? JSON.stringify({
            uid: templateUid,
            action: 'update',
            data: formData,
          })
        : JSON.stringify({
            action: 'create',
            data: formData,
          });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      });

      if (!response.ok) {
        let errorMessage = `Failed to ${isEditing ? 'update' : 'create'} template`;
        try {
          // Attempt to parse JSON error response if content type is JSON
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            errorMessage = errorData.message || errorMessage;
          } else {
            // Fallback to status text if not JSON
             errorMessage = `Failed to ${isEditing ? 'update' : 'create'} template: ${response.status} ${response.statusText}`;
          }
        } catch (jsonError) {
          console.error('Error parsing error response as JSON:', jsonError);
          // If parsing fails, use a generic error message with status
          errorMessage = `Failed to ${isEditing ? 'update' : 'create'} template: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();

      if (result.status !== 200) {
        throw new Error(result.message || `Failed to ${isEditing ? 'update' : 'create'} template`);
      }

      setSubmitSuccess(true);

      // Redirect after a brief delay to show success message
      setTimeout(() => {
        router.push(`/${locale}/manage/templates`);
      }, 1500);
    } catch (err) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} template:`, err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Editor options
  const editorOptions = {
    minimap: { enabled: false },
    fontSize: 14,
    formatOnPaste: true,
    formatOnType: false,
    roundedSelection: false,
    scrollBeyondLastLine: false,
    readOnly: false,
    theme: 'vs',
    wordWrap: 'on' as const,
    automaticLayout: true,
    tabSize: 2,
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <HeaderManage title={isEditing ? '编辑模板' : '创建新模板'} locale={locale} />

      {isFetchingData ? (
        <div className="text-center py-12">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <p className="mt-2">加载中...</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
          {submitSuccess && (
            <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6">
              <p>{isEditing ? '模板更新成功！' : '模板创建成功！'}</p>
            </div>
          )}

          {error && (
            <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
              <p>{error}</p>
            </div>
          )}

          <div className="mb-6">
            <label className="block mb-2 font-medium">
              模板名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded"
              disabled={isLoading}
              required
            />
          </div>

          <div className="mb-6">
            <label className="block mb-2 font-medium">
              预览链接
            </label>
            <input
              type="text"
              name="preview_url"
              value={formData.preview_url}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 rounded"
              disabled={isLoading}
              placeholder="https://example.com/preview"
            />
          </div>

          <div className="mb-6">
            <label className="block mb-2 font-medium">
              模板内容 <span className="text-red-500">*</span>
            </label>
            <div className="border border-gray-300 rounded" style={{ height: '500px' }}>
              <Editor
                height="100%"
                defaultLanguage="html"
                value={formData.content}
                onChange={handleEditorChange}
                options={editorOptions}
                loading={<div className="text-center p-4">Loading editor...</div>}
              />
            </div>
          </div>

          <div className="flex gap-4 mt-6">
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? '提交中...' : isEditing ? '更新模板' : '创建模板'}
            </button>

            <button
              type="button"
              className="px-6 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400"
              onClick={() => router.push(`/${locale}/manage/templates`)}
              disabled={isLoading}
            >
              取消
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default TemplateFormComponent; 