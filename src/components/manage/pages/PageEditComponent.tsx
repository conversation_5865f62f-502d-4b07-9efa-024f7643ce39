'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import HeaderManage from "~/components/common/HeaderManage";
import { getTemplateKeyNameMap } from '~/configs/templatesConfig';

interface PageEditComponentProps {
  locale: string;
  pageUid: string;
}

export const PageEditComponent = ({ locale, pageUid }: PageEditComponentProps) => {
  const router = useRouter();
  
  const [page, setPage] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [jsonContent, setJsonContent] = useState('');
  const [games, setGames] = useState<any[]>([]);
  const [sites, setSites] = useState<any[]>([]);
  const [siteDomain, setSiteDomain] = useState('');
  const [iframeUrl, setIframeUrl] = useState('');
  const [publicUrl, setPublicUrl] = useState('');
  const [isHomepage, setIsHomepage] = useState(false);
  const [templateKeyNameMap] = useState(getTemplateKeyNameMap());
  const [templateKey, setTemplateKey] = useState('default');
  const [slug, setSlug] = useState('');
  
  // 格式化URL，确保格式一致
  const formatUrl = (url: string) => {
    if (!url) return '';
    // 移除末尾的斜杠
    url = url.replace(/\/+$/, '');
    // 如果没有协议，根据环境添加协议
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // 如果是localhost，使用http
      if (url.includes('localhost')) {
        url = 'http://' + url;
      } else {
        // 其他情况使用https
        url = 'https://' + url;
      }
    }
    return url;
  };

  useEffect(() => {
    const fetchAllData = async () => {
      setIsLoading(true);
      setError('');
      try {
        // 主页面数据
        const pageRes = await fetch(`/api/manage/pages/${pageUid}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ uid: pageUid, action: 'get' })
        });
        const pageResult = await pageRes.json();
        if (pageResult.status !== 200 || !pageResult.data) throw new Error(pageResult.message || '获取页面数据失败');
        setPage(pageResult.data);
        setJsonContent(JSON.stringify(pageResult.data.json_content, null, 2));
        setIframeUrl(pageResult.data.iframe_url || '');
        setPublicUrl(pageResult.data.public_url || '');
        setIsHomepage(pageResult.data.is_homepage || false);
        setTemplateKey(pageResult.data.template_key || 'default');
        setSlug(pageResult.data.slug || '');
        // 辅助数据
        try {
          // 站点列表（修正为POST+action:list）
          const sitesRes = await fetch('/api/manage/sites', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'list',
              data: { page: 1, pageSize: 100 }
            })
          });
          const sitesResult = await sitesRes.json();
          if (sitesResult.status === 200 && sitesResult.data) {
            setSites(sitesResult.data.sites || []);
            const site = (sitesResult.data.sites || []).find((s: any) => s.uid === pageResult.data.site_uid);
            setSiteDomain(site?.domain || '');
            if (!pageResult.data.public_url && site?.domain && pageResult.data.slug) {
                setPublicUrl(`${site.domain}/game/${pageResult.data.slug}`);
            }
          } else {
            console.warn('站点列表加载失败', sitesResult);
          }
          // 游戏列表
          const gamesRes = await fetch('/api/manage/games');
          const gamesResult = await gamesRes.json();
          if (gamesResult.code === 200 && gamesResult.data) {
            setGames(gamesResult.data.resultList || []);
            const game = (gamesResult.data.resultList || []).find((g: any) => g.uid === pageResult.data.game_uid);
            if (!pageResult.data.iframe_url && game?.iframe_url) {
                setIframeUrl(game.iframe_url);
            }
          } else {
            console.warn('游戏列表加载失败', gamesResult);
          }
        } catch (err) {
          console.warn('辅助数据加载失败', err);
        }
      } catch (err) {
        setError('无法加载页面数据，请重试。');
      } finally {
        setIsLoading(false);
      }
    };
    fetchAllData();
  }, [pageUid]);
  
  const handleBack = () => {
    router.push(`/${locale}/manage/pages`);
  };
  
  const validateSlug = (value: string) => {
    // 只有首页允许 slug 为空，非首页必须校验格式且非空
    if (isHomepage) {
      return value === '';
    }
    return value !== '' && /^[a-zA-Z0-9_-]+$/.test(value);
  };
  
  const handleSave = async () => {
    if (!page) return;
    
    // 校验 slug
    if (!validateSlug(slug)) {
      alert('slug 仅允许字母、数字、-、_，只有首页允许 slug 为空');
      return;
    }
    // 唯一性校验 TODO
    
    // 验证首页设置
    if (isHomepage) {
      // 格式化URL后比较
      const formattedPublicUrl = formatUrl(publicUrl);
      const formattedDomain = formatUrl(siteDomain);
      
      if (formattedPublicUrl !== formattedDomain) {
        alert('检查首页的URL是否正确');
        return;
      }
    } else if (page.is_homepage) {
      // 如果当前页面是首页，不允许取消首页设置
      alert('不能取消设置首页，网站必须有一个首页');
      return;
    }
    
    setIsSaving(true);
    try {
      let parsedJson;
      try {
        parsedJson = JSON.parse(jsonContent);
      } catch (err) {
        throw new Error('无效的JSON格式，请检查您的输入');
      }
      const response = await fetch(`/api/manage/pages/${pageUid}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          uid: pageUid,
          action: 'patch',
          data: { 
            json_content: parsedJson,
            iframe_url: iframeUrl,
            public_url: formatUrl(publicUrl),
            is_homepage: isHomepage,
            slug: isHomepage ? '' : slug,
            template_key: templateKey
          }
        }),
      });
      const result = await response.json();
      if (result.status === 200 && result.data) {
        setPage(result.data);
        alert('页面保存成功！');
      } else {
        throw new Error(result.message || '保存页面失败');
      }
    } catch (err) {
      alert(err.message || '保存页面失败，请重试。');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handlePublish = async () => {
    if (!page) return;
    try {
      let parsedJson;
      try {
        parsedJson = JSON.parse(jsonContent);
      } catch (err) {
        throw new Error('无效的JSON格式，请先保存有效的JSON内容');
      }
      setIsPublishing(true);
      // 先保存内容
      const saveResponse = await fetch(`/api/manage/pages/${pageUid}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          uid: pageUid,
          action: 'patch',
          data: {
            json_content: parsedJson,
            iframe_url: iframeUrl,
            public_url: publicUrl,
            is_homepage: isHomepage,
            template_key: templateKey
          }
        }),
      });
      
      // Process save response - check status from JSON body, not just response.ok
      const saveResult = await saveResponse.json();
      if (saveResult.status !== 200) {
        // If save failed (e.g., homepage conflict), throw error with backend message
        throw new Error(saveResult.message || '更新内容失败，无法发布');
      }

      // 如果保存成功，再发布
      const response = await fetch(`/api/manage/pages/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: pageUid, status: 1 }),
      });
      const result = await response.json();
      if (result.status === 200 && result.data) {
        setPage(result.data);
        alert('页面发布成功！');
      } else {
        throw new Error(result.message || '发布页面失败');
      }
    } catch (err) {
      console.error('发布页面出错:', err);
      alert(err.message || '发布页面失败，请重试。');
    } finally {
      setIsPublishing(false);
    }
  };
  
  const handleUnpublish = async () => {
    if (!page) return;
    
    // 检查是否是首页
    if (page.is_homepage) {
      alert('首页不能取消发布');
      return;
    }
    
    try {
      const response = await fetch(`/api/manage/pages/status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: pageUid, status: 0 }),
      });
      const result = await response.json();
      if (result.status === 200 && result.data) {
        setPage(result.data);
        alert('已取消发布，页面变为草稿');
      } else {
        throw new Error(result.message || '取消发布失败');
      }
    } catch (err) {
      alert(err.message || '取消发布失败，请重试。');
    }
  };
  
  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page="pages" />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <div className="mx-auto max-w-[80%]">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold">编辑页面</h1>
              <button
                onClick={handleBack}
                className="mt-2 text-blue-600 hover:underline"
              >
                ← 返回页面列表
              </button>
            </div>
          </div>
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <p>Loading...</p>
            </div>
          ) : page ? (
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              {/* Page metadata */}
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">{page.title}</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 rounded">
                    <div className="space-y-2">
                      <div><span className="font-medium">游戏关键词:</span> {page.game_keyword || '未知游戏'}</div>
                      <div><span className="font-medium">站点:</span> {page.site_name || '未知站点'}</div>
                      <div><span className="font-medium">站点域名:</span> {siteDomain || '-'}</div>
                      <div><span className="font-medium">模板:</span> {page.template_name || '默认模板'}</div>
                      <div><span className="font-medium">LLM模型:</span> {page.llm_model_used || '未知'}</div>
                      <div><span className="font-medium">状态:</span> {page.status === 0 ? '草稿' : '已发布'}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* public_url 编辑区 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">页面最终发布URL:</label>
                <input
                  type="text"
                  value={publicUrl}
                  onChange={e => setPublicUrl(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="例如: http://yourdomain.com/game/yourgame"
                />
              </div>

              {/* slug 编辑区 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">页面 Slug（唯一且必填，仅限字母、数字、-、_）:</label>
                <input
                  type="text"
                  value={slug}
                  onChange={e => setSlug(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="例如: merge-melons"
                  required
                />
                {!validateSlug(slug) && <div className="text-red-500 text-xs mt-1">slug 仅允许字母、数字、-、_，只有首页允许 slug 为空</div>}
              </div>

              {/* Template Selector */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">页面模板:</label>
                <select
                  value={templateKey}
                  onChange={e => setTemplateKey(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                >
                  {Object.entries(templateKeyNameMap).map(([key, name]) => (
                    <option key={key} value={key}>
                      {name}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* iframe_url 编辑区 */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">游戏iframe地址:</label>
                <input
                  type="text"
                  value={iframeUrl}
                  onChange={e => setIframeUrl(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="请输入iframe地址"
                />
              </div>
              
              {/* is_homepage 勾选框 */}
              <div className="mb-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isHomepage}
                    onChange={e => {
                      if (page.is_homepage && !e.target.checked) {
                        alert('不能取消首页设置，网站必须有一个首页');
                        return;
                      }
                      setIsHomepage(e.target.checked);
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium">设置为首页</span>
                </label>
              </div>
              
              {/* JSON Editor */}
              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">
                  页面JSON内容:
                </label>
                <textarea
                  value={jsonContent || ''}
                  onChange={(e) => setJsonContent(e.target.value)}
                  rows={20}
                  className="w-full p-3 border border-gray-300 rounded font-mono text-sm shadow-inner"
                  style={{ tabSize: 2 }}
                />
              </div>
                          
              {/* Action buttons */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`px-4 py-2 ${isSaving ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'} text-white rounded`}
                >
                  {isSaving ? '保存中...' : '保存修改'}
                </button>
                
                {page.status === 0 && (
                  <button
                    onClick={handlePublish}
                    disabled={isPublishing}
                    className={`px-4 py-2 ${
                      isPublishing ? 'bg-green-400' : 'bg-green-600 hover:bg-green-700'
                    } text-white rounded`}
                  >
                    {isPublishing ? '发布中...' : '发布页面'}
                  </button>
                )}
                
                {page.status === 1 && (
                  <button
                    onClick={handleUnpublish}
                    className="px-4 py-2 bg-red-600 hover:bg-yellow-700 text-white rounded"
                  >
                    取消发布
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="p-4 bg-red-100 text-red-700 rounded">
              找不到页面数据
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageEditComponent;