'use client'
import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import HeaderManage from '~/components/common/HeaderManage';
import { Site, SiteConfig } from '~/types/site';
import { Template, Prompt } from '~/utils/types/game';
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { toast } from "react-toastify";
import Link from "next/link";
import { availableLlmModels } from "~/configs/llmModelsConfig";
import { availablePrompts } from "~/configs/promptsConfig";
import { availableTemplates } from '~/configs/templatesConfig';

interface SiteFormComponentProps {
  locale: string;
  siteUid?: string; // Single parameter for site identification
}

// Default empty config structure
const DEFAULT_CONFIG = {
  features: {
    allowUserComments: false,
    allowForwardFunction: false
  },
  navigation_links: [] // 添加 navigation_links 默认值
};

const SiteFormComponent = ({ locale, siteUid }: SiteFormComponentProps) => {
  const router = useRouter();
  const { userData, setShowLoadingModal } = useCommonContext();
  const [authStatus, setAuthStatus] = useState('pending');

  // State for templates and prompts (for dropdowns)
  const [templates, setTemplates] = useState<Template[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Form state with proper typings
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    default_template_key: '',
    default_llm_model: '',
    default_llm_prompt_key: '',
    config: DEFAULT_CONFIG,
  });

  // UI state
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);

  // Check user authorization
  useEffect(() => {
    if (userData && Object.keys(userData).length > 0) {
      if (userData.hasOwnProperty('role')) {
        if (checkAdminUser(userData)) {
          setAuthStatus('authorized');
          setShowLoadingModal(false);
        } else {
          setAuthStatus('unauthorized');
          setShowLoadingModal(false);
        }
      } else {
        setAuthStatus('pending');
        setShowLoadingModal(true);
      }
    } else if (userData === undefined) {
      setAuthStatus('pending');
      setShowLoadingModal(true);
    } else {
      setAuthStatus('unauthorized');
      setShowLoadingModal(false);
    }
  }, [userData, setShowLoadingModal]);

  // Add useEffect to fetch site data if editing
  useEffect(() => {
    // Only fetch if we have a UID
    if (siteUid) {
      const fetchSiteData = async () => {
        setIsLoading(true);
        setError('');
        
        try {
          const apiUrl = `/api/manage/sites/uid/${siteUid}`;
          
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              uid: siteUid,
              action: 'get'
            })
          });
          
          if (!response.ok) {
            //console.error(`API response not OK: ${response.status} ${response.statusText}`);
            throw new Error(`获取站点数据失败: ${response.status}`);
          }
          
          const result = await response.json();
          
          if (result.status !== 200) {
            //console.error(`API returned error status: ${result.status}, message: ${result.message}`);
            throw new Error(result.message || '获取站点数据失败');
          }
          
          const site = result.data;
          
          if (!site) {
            //console.error('No site data in response');
            throw new Error('API返回的站点数据为空');
          }
          
          setFormData({
            name: site.name || '',
            domain: site.domain || '',
            default_template_key: site.default_template_key || '',
            default_llm_model: site.default_llm_model || '',
            default_llm_prompt_key: site.default_llm_prompt_key || '',
            config: {
              ...(site.config || {}), // 保留所有现有 config 属性
              features: {
                allowUserComments: site.config?.features?.allowUserComments || false,
                allowForwardFunction: site.config?.features?.allowForwardFunction || false
              },
              navigation_links: site.config?.navigation_links || [] // 加载 navigation_links
            }
          });
          
          setIsEditMode(true);
        } catch (error) {
          //console.error('Error fetching site data:', error);
          setError('获取站点数据失败: ' + (error instanceof Error ? error.message : '未知错误'));
          toast.error('获取站点数据失败: ' + (error instanceof Error ? error.message : '未知错误'));
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchSiteData();
    }
  }, [siteUid]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes for config features
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;

    if (name.startsWith('features.')) {
      const featureName = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        config: {
          ...prev.config,
          features: {
            ...prev.config.features,
            [featureName]: checked
          }
        }
      }));
    }
  };

  // Handle adding a new navigation link
  const handleAddLink = () => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        navigation_links: [...prev.config.navigation_links, { label: '', url: '', target: '_self' }]
      }
    }));
  };

  // Handle removing a navigation link
  const handleRemoveLink = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        navigation_links: prev.config.navigation_links.filter((_, index) => index !== indexToRemove)
      }
    }));
  };

  // Handle changes to a specific navigation link
  const handleLinkChange = (indexToChange, e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        navigation_links: prev.config.navigation_links.map((link, index) =>
          index === indexToChange ? { ...link, [name]: value } : link
        )
      }
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (isSaving) return;

    // Validate form data
    if (!formData.name.trim()) {
      toast.error('站点名称不能为空');
      return;
    }

    if (!formData.domain.trim()) {
      toast.error('域名不能为空');
      return;
    }
    
    if (!formData.default_template_key) {
      toast.error('必须选择默认模板');
      return;
    }
    
    if (!formData.default_llm_model) {
      toast.error('必须选择默认LLM模型');
      return;
    }
    
    if (!formData.default_llm_prompt_key) {
      toast.error('必须选择默认提示');
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // Prepare data for API
      const apiData = {
        // Add UID for update operations
        ...(isEditMode && { uid: siteUid }),
        action: isEditMode ? 'update' : 'create', // Use 'update' or 'create' action
        data: { // Wrap form data in a 'data' object
        name: formData.name.trim(),
        domain: formData.domain.trim(),
        config: formData.config,
        default_template_key: formData.default_template_key,
        default_llm_model: formData.default_llm_model,
        default_llm_prompt_key: formData.default_llm_prompt_key
        }
      };

      // Always use POST method
      const method = 'POST';
      // The URL is always the UID route for updates, and the base route for creates
      const url = isEditMode ? `/api/manage/sites/uid/${siteUid}` : '/api/manage/sites';


      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiData)
      });

      const result = await response.json();

      if (!response.ok || result.status !== 200) {
        throw new Error(result.message || '保存站点失败');
      }

      toast.success(`站点${isEditMode ? '更新' : '创建'}成功`);

      // 操作成功后重定向到站点列表
      setTimeout(() => {
        router.push(`/${locale}/manage/sites`);
      }, 1500);
    } catch (error) {
      //console.error('Error submitting form:', error);
      setError(error instanceof Error ? error.message : '发生错误，请重试');
      toast.error(error instanceof Error ? error.message : '保存站点失败');
    } finally {
      setIsSaving(false);
    }
  };

  if (authStatus === 'unauthorized') {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong className="font-bold">未授权!</strong>
          <span className="block sm:inline"> 您没有权限访问此页面。</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <HeaderManage locale={locale} page="manage/sites" />
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">{isEditMode ? '编辑站点' : '创建新站点'}</h1>
          <p className="text-gray-600 mt-1">
            {isEditMode 
              ? '更新站点信息和配置。'
              : '创建一个新站点来生成和管理落地页。'}
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center my-12">
            <p className="text-gray-500">加载站点数据中...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
            {/* Site Name */}
            <div className="mb-4">
              <label htmlFor="name" className="block text-gray-700 text-sm font-bold mb-2">
                站点名称 *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="输入站点名称"
                required
              />
            </div>

            {/* Domain */}
            <div className="mb-4">
              <label htmlFor="domain" className="block text-gray-700 text-sm font-bold mb-2">
                域名 *
              </label>
              <input
                type="text"
                id="domain"
                name="domain"
                value={formData.domain}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="例如: example.com"
                required
              />
            </div>

            {/* Default Template */}
            <div className="mb-4">
              <label htmlFor="default_template_key" className="block text-gray-700 text-sm font-bold mb-2">
                默认模板 *
              </label>
              <select
                id="default_template_key"
                name="default_template_key"
                value={formData.default_template_key}
                onChange={handleInputChange}
                className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              >
                <option value="">选择模板</option>
                {availableTemplates.map(template => (
                  <option key={template.key} value={template.key}>
                    {template.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Default LLM Model */}
            <div className="mb-4">
              <label htmlFor="default_llm_model" className="block text-gray-700 text-sm font-bold mb-2">
                默认LLM模型 *
              </label>
              <select
                id="default_llm_model"
                name="default_llm_model"
                value={formData.default_llm_model}
                onChange={handleInputChange}
                className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              >
                <option value="">选择模型</option>
                {availableLlmModels.map(model => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Default Prompt */}
            <div className="mb-4">
              <label htmlFor="default_llm_prompt_key" className="block text-gray-700 text-sm font-bold mb-2">
                默认提示 *
              </label>
              <select
                id="default_llm_prompt_key"
                name="default_llm_prompt_key"
                value={formData.default_llm_prompt_key}
                onChange={handleInputChange}
                className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              >
                <option value="">选择提示</option>
                {availablePrompts.map(prompt => (
                  <option key={prompt.key} value={prompt.key}>
                    {prompt.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Configuration Options */}
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                功能设置
              </label>
              
              <div className="ml-2 mt-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    name="features.allowUserComments"
                    checked={formData.config.features.allowUserComments}
                    onChange={handleCheckboxChange}
                    className="form-checkbox h-5 w-5 text-blue-600"
                  />
                  <span className="ml-2 text-gray-700">允许用户评论</span>
                </label>
              </div>
              
              <div className="ml-2 mt-2">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    name="features.allowForwardFunction"
                    checked={formData.config.features.allowForwardFunction}
                    onChange={handleCheckboxChange}
                    className="form-checkbox h-5 w-5 text-blue-600"
                  />
                  <span className="ml-2 text-gray-700">允许转发功能</span>
                </label>
              </div>
            </div>

            {/* Navigation Links Configuration */}
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                导航链接
              </label>
              <div className="ml-2 mt-2 space-y-4">
                {formData.config.navigation_links.map((link, index) => (
                  <div key={index} className="border p-4 rounded-md bg-gray-50">
                    <h3 className="text-md font-semibold mb-2">链接 #{index + 1}</h3>
                    <div className="mb-3">
                      <label htmlFor={`link-label-${index}`} className="block text-gray-700 text-xs font-bold mb-1">
                        链接文本
                      </label>
                      <input
                        type="text"
                        id={`link-label-${index}`}
                        name="label"
                        value={link.label}
                        onChange={(e) => handleLinkChange(index, e)}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline text-sm"
                        placeholder="New Games"
                      />
                    </div>
                    <div className="mb-3">
                      <label htmlFor={`link-url-${index}`} className="block text-gray-700 text-xs font-bold mb-1">
                        链接地址
                      </label>
                      <input
                        type="text"
                        id={`link-url-${index}`}
                        name="url"
                        value={link.url}
                        onChange={(e) => handleLinkChange(index, e)}
                        className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline text-sm"
                        placeholder="完整的跳转链接"
                      />
                    </div>
                    <div className="mb-3">
                      <label htmlFor={`link-target-${index}`} className="block text-gray-700 text-xs font-bold mb-1">
                        打开方式
                      </label>
                      <select
                        id={`link-target-${index}`}
                        name="target"
                        value={link.target || '_self'} // 默认值 _self
                        onChange={(e) => handleLinkChange(index, e)}
                        className="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline text-sm"
                      >
                        <option value="_self">当前窗口 (_self)</option>
                        <option value="_blank">新窗口 (_blank)</option>
                      </select>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveLink(index)}
                      className="mt-2 bg-red-500 hover:bg-red-700 text-white text-sm font-bold py-1 px-3 rounded focus:outline-none focus:shadow-outline"
                    >
                      移除此链接
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={handleAddLink}
                  className="mt-4 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline text-sm"
                >
                  添加新链接
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={() => router.push(`/${locale}/manage/sites`)}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className={`bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ${
                  isSaving ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSaving ? '保存中...' : isEditMode ? '更新站点' : '创建站点'}
              </button>
            </div>
          </form>
        )}
      </div>
    </>
  );
};

export default SiteFormComponent; 