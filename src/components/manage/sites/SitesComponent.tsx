'use client'
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";
import { useState, useEffect, useCallback } from "react";
// import { checkAdminUser } from "~/utils/checkWhiteUser";
import Link from "next/link";
import { useRouter } from "next/navigation";
import debounce from "lodash/debounce";
import { toast } from "react-toastify";
import { availableTemplates } from '~/configs/templatesConfig';

const SitesComponent = ({ locale }) => {
  const router = useRouter();
  const { userData, setShowLoadingModal } = useCommonContext();
  // 直接设置为authorized，跳过鉴权
  const [authStatus, setAuthStatus] = useState('authorized');
  const [sites, setSites] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchName, setSearchName] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [sitePageCounts, setSitePageCounts] = useState({});

  const templateKeyNameMap = {};
  availableTemplates.forEach(t => {
    templateKeyNameMap[t.key] = t.name;
  });

  // Fetch sites data with pagination and search
  const fetchSites = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/manage/sites/uid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'list',
          data: {
            page,
            pageSize: 10,
            name: searchName
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();


      if (data.status === 200) {
        setSites(data.data?.data?.resultList || []);
        setTotalPages(data.data?.data?.totalPage || 1);
      } else {
        setError(data.message || '加载站点失败');
        toast.error(data.message || '加载站点失败');
      }
    } catch (err) {
      setError('加载站点失败: ' + (err instanceof Error ? err.message : '未知错误'));
      toast.error('加载站点失败');
      //console.error(err);
    } finally {
      setIsLoading(false);
    }
  }, [page, searchName]);

  // Fetch templates data
  const fetchTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/manage/templates');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.code === 200) {
        setTemplates(data.data || []);
      } else {
        //console.error('Failed to load templates:', data.message);
      }
    } catch (err) {
      //console.error('Error fetching templates:', err);
    }
  }, []);

  // Add useEffect to fetch on component mount
  useEffect(() => {
    fetchSites();
    fetchTemplates();
  }, [fetchSites, fetchTemplates]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((value) => {
      setSearchName(value);
      setPage(1); // Reset to first page on search
    }, 500),
    []
  );

  const handleSearchChange = (e) => {
    debouncedSearch(e.target.value);
  };

  const handleDelete = async (site) => {
    if (!window.confirm('确定要删除这个站点吗？')) {
      return;
    }

    setIsDeleting(true);

    try {
      // Use UID when available, fall back to ID for backward compatibility
      const endpoint = site.uid 
        ? `/api/manage/sites/uid/${site.uid}` 
        : `/api/manage/sites/${site.id}`;
        
      const response = await fetch(endpoint, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok && data.code === 200) {
        toast.success('站点删除成功');
        // Refresh the list
        fetchSites();
      } else {
        toast.error(data.message || '站点删除失败');
      }
    } catch (err) {
      toast.error('删除站点时发生错误');
      //console.error(err);
    } finally {
      setIsDeleting(false);
    }
  };

  // 新增：获取所有站点的已上线页面数量
  const fetchSitePageCounts = useCallback(async (sitesList) => {
    if (!sitesList || sitesList.length === 0) return;
    const counts = {};
    await Promise.all(sitesList.map(async (site) => {
      if (!site.uid) return;
      try {
        const res = await fetch(`/api/manage/pages/count?siteUid=${site.uid}&status=1`);
        if (res.ok) {
          const data = await res.json();
          counts[site.uid] = data?.data?.total || 0;
        } else {
          counts[site.uid] = 0;
        }
      } catch {
        counts[site.uid] = 0;
      }
    }));
    setSitePageCounts(counts);
  }, []);

  // 修改fetchSites后，拉取每个站点的上线页面数量
  useEffect(() => {
    if (sites && sites.length > 0) {
      fetchSitePageCounts(sites);
    }
  }, [sites, fetchSitePageCounts]);

  // 添加HeaderManage组件
  return (
    <>
      <HeaderManage locale={locale} page="manage/sites" />
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">站点管理</h1>
          <Link
            href={`/${locale}/manage/sites/new`}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          >
            创建新站点
          </Link>
        </div>

        <div className="mb-6">
          <input
            type="text"
            placeholder="搜索站点名称..."
            onChange={handleSearchChange}
            className="w-full p-2 border border-gray-300 rounded-md"
          />
        </div>

        {isLoading && sites.length === 0 ? (
          <div className="flex justify-center my-8">
            <p className="text-gray-500">加载站点中...</p>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : sites.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">未找到站点。创建一个新站点开始使用。</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已上线游戏数</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {sites.map((site) => (
                    <tr key={site.id || site.uid} className="hover:bg-gray-50">
                      <td className="px-4 py-3 whitespace-nowrap">{site.id}</td>
                      <td className="px-4 py-3 whitespace-nowrap">{site.name}</td>
                      <td className="px-4 py-3 whitespace-nowrap">{site.domain}</td>
                      <td className="px-4 py-3 whitespace-nowrap">
                        {site.default_template_key
                          ? (templateKeyNameMap[site.default_template_key] || site.default_template_key)
                          : '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-center">{sitePageCounts[site.uid] ?? '-'}</td>
                      <td className="px-4 py-3 whitespace-nowrap flex gap-2">
                        {site.uid ? (
                          <>
                            <Link
                              href={`/${locale}/manage/sites/uid/${site.uid}/edit`}
                              className="text-blue-600 hover:text-blue-900 px-2 py-1 rounded-md hover:bg-blue-50"
                            >
                              编辑
                            </Link>
                          </>
                        ) : (
                          <>
                            <Link
                              href={`/${locale}/manage/sites/${site.id}/edit`}
                              className="text-blue-600 hover:text-blue-900 px-2 py-1 rounded-md hover:bg-blue-50"
                            >
                              编辑
                            </Link>
                          </>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-6">
                <nav className="flex items-center">
                  <button
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={page === 1}
                    className="px-3 py-1 border rounded-md mr-2 disabled:opacity-50"
                  >
                    上一页
                  </button>

                  {[...Array(totalPages)].map((_, i) => (
                    <button
                      key={i + 1}
                      onClick={() => setPage(i + 1)}
                      className={`px-3 py-1 mx-1 rounded-md ${page === i + 1 ? 'bg-blue-600 text-white' : 'border'
                        }`}
                    >
                      {i + 1}
                    </button>
                  ))}

                  <button
                    onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                    disabled={page === totalPages}
                    className="px-3 py-1 border rounded-md ml-2 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </nav>
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};

export default SitesComponent; 