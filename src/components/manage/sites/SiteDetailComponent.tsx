'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import HeaderManage from '~/components/common/HeaderManage';
import { Template, Prompt } from '~/utils/types/game';
import { Site } from '~/types/site';
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import Link from "next/link";
import { toast } from "react-toastify";

interface SiteDetailComponentProps {
  locale: string;
  siteUid: string;
}

const SiteDetailComponent = ({ locale, siteUid }: SiteDetailComponentProps) => {
  const router = useRouter();
  const { userData, setShowLoadingModal } = useCommonContext();
  const [authStatus, setAuthStatus] = useState('pending');
  const [site, setSite] = useState<Site | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [templateName, setTemplateName] = useState('');

  // Fetch template data when component mounts
  useEffect(() => {
    if (authStatus === 'authorized') {
      const fetchSiteData = async () => {
        setIsLoading(true);
        try {
          // Use UID API endpoint
          const apiUrl = `/api/manage/sites/uid/${siteUid}`;
            
          const response = await fetch(apiUrl);
          if (!response.ok) {
            throw new Error(`Failed to fetch site: ${response.status}`);
          }
          const result = await response.json();
          if (result.code === 200) {
            setSite(result.data);
          } else {
            setError(result.message || 'Failed to fetch site details');
          }
          
          // Fetch templates
          const templatesResponse = await fetch('/api/manage/templates');
          if (templatesResponse.ok) {
            const templatesResult = await templatesResponse.json();
            if (templatesResult.code === 200 && Array.isArray(templatesResult.data?.resultList)) {
              setTemplates(templatesResult.data.resultList);
              
              // Find the template name if site has a default template
              if (result.data?.default_template_uid && templatesResult.data?.resultList) {
                const template = templatesResult.data.resultList.find(
                  t => t.uid === result.data.default_template_uid
                );
                if (template) {
                  setTemplateName(template.name);
                }
              }
            }
          }
          
        } catch (error) {
          console.error('Error fetching site data:', error);
          setError('Failed to load site details. Please try again later.');
        } finally {
          setIsLoading(false);
        }
      };
      
      if (siteUid) {
        fetchSiteData();
      }
    }
  }, [siteUid, authStatus]);

  // Check user authorization
  useEffect(() => {
    if (userData && Object.keys(userData).length > 0) {
      if (userData.hasOwnProperty('role')) {
        if (checkAdminUser(userData)) {
          setAuthStatus('authorized');
          setShowLoadingModal(false);
        } else {
          setAuthStatus('unauthorized');
          setShowLoadingModal(false);
        }
      } else {
        setAuthStatus('pending');
        setShowLoadingModal(true);
      }
    } else if (userData === undefined) {
      setAuthStatus('pending');
      setShowLoadingModal(true);
    } else {
      setAuthStatus('unauthorized');
      setShowLoadingModal(false);
    }
  }, [userData, setShowLoadingModal]);

  // Get prompt name by key
  const getPromptName = (promptKey?: string) => {
    if (!promptKey) return '未设置';
    const prompt = prompts.find(p => p.key === promptKey);
    return prompt ? promptKey : promptKey;
  };

  // Get back link based on parameters
  const getBackLink = () => {
    return `/${locale}/manage/sites`;
  };
  
  // Get edit link based on parameters
  const getEditLink = () => {
    return `/${locale}/manage/sites/uid/${siteUid}/edit`;
  };

  // Conditional rendering based on authorization state
  if (authStatus === 'pending') {
    return (
      <>
        <HeaderManage locale={locale} page="sites" />
        <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
          <p className="text-gray-500">Loading...</p>
        </div>
      </>
    );
  }

  if (authStatus === 'unauthorized') {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page="sites" />
        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-100px)] text-center px-4">
          <h2 className="text-2xl font-semibold text-red-600 mb-2">Access Denied</h2>
          <p className="text-gray-700">You do not have permission to access this page.</p>
          <p className="text-gray-500 mt-1">Please log in with an administrator account.</p>
        </div>
      </>
    );
  }

  if (isLoading) {
    return (
      <>
        <HeaderManage locale={locale} page="sites" />
        <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
          <p className="text-gray-500">Loading site details...</p>
        </div>
      </>
    );
  }

  if (error || !site) {
    return (
      <>
        <HeaderManage locale={locale} page="sites" />
        <div className="container mx-auto py-8 px-4">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold">Site Details</h1>
            <Link
              href={getBackLink()}
              className="text-blue-600 hover:text-blue-800"
            >
              Back to Site List
            </Link>
          </div>

          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <span className="block sm:inline">{error || 'Site not found'}</span>
          </div>
        </div>
      </>
    );
  }

  // Extract features for easier access
  const { features = { allowUserComments: false, allowForwardFunction: false } } = site.config || {};

  return (
    <>
      <HeaderManage locale={locale} page="sites" />
      <div className="container mx-auto py-8 px-4">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Site Details</h1>
          <div className="flex gap-3">
            <Link
              href={getEditLink()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              Edit Site
            </Link>
            <Link
              href={getBackLink()}
              className="text-blue-600 hover:text-blue-800 border border-blue-600 px-4 py-2 rounded-md"
            >
              Back to List
            </Link>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          {/* Basic Information */}
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-gray-600 text-sm">ID</p>
                <p className="font-medium">{site.id}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">UID</p>
                <p className="font-medium">{site.uid || '-'}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Name</p>
                <p className="font-medium">{site.name}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Domain</p>
                <p className="font-medium">{site.domain}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Created At</p>
                <p className="font-medium">{new Date(site.created_at).toLocaleString()}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Updated At</p>
                <p className="font-medium">{new Date(site.updated_at).toLocaleString()}</p>
              </div>
            </div>
          </div>

          {/* Default Settings */}
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold mb-4">Default Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-gray-600 text-sm">Default Template</p>
                <p className="font-medium">{templateName || 'Not Set'}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Default LLM Model</p>
                <p className="font-medium">{site.default_llm_model || 'Not Set'}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Default Prompt Key</p>
                <p className="font-medium">{getPromptName(site.default_llm_prompt_key)}</p>
              </div>
            </div>
          </div>

          {/* Features Config */}
          <div className="p-6 border-b">
            <h2 className="text-xl font-semibold mb-4">Features Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-gray-600 text-sm">Allow User Comments</p>
                <p className="font-medium">{features.allowUserComments ? 'Yes' : 'No'}</p>
              </div>
              <div>
                <p className="text-gray-600 text-sm">Allow Forward Function</p>
                <p className="font-medium">{features.allowForwardFunction ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SiteDetailComponent; 