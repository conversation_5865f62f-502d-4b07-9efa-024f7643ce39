import React, { useState, useEffect } from "react";
import { useCommonContext } from "~/context/common-context";
import { getContentTypeByFileExtension } from "~/utils/fileType";
import { Loader2, X, ArrowLeft, ArrowRight, Search, Upload, Image as ImageIcon } from "lucide-react";

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (imageUrl: string) => void;
  imageType: "icon" | "painting";
}

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, onSelect, imageType }) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const { setShowLoadingModal } = useCommonContext();

  // 获取图片列表
  const fetchImages = async (page = 1) => {
    setLoading(true);
    try {
      console.log("搜索条件:", searchTerm); // 调试用
      const response = await fetch("/api/manage/image/getList", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          image_type: imageType,
          page,
          pageSize: 12,
          searchTerm: searchTerm, // 确保这个字段名称和后端一致
        }),
      });

      const result = await response.json();
      console.log("搜索结果:", result); // 调试用

      if (result.code === 200) {
        setImages(result.data.list);
        setTotalPages(result.data.pagination.totalPages);
        setCurrentPage(result.data.pagination.current);
      } else {
        console.error("获取图片失败:", result.message);
      }
    } catch (error) {
      console.error("获取图片出错:", error);
    } finally {
      setLoading(false);
    }
  };

  // 当模态框打开或页面变化时获取图片
  useEffect(() => {
    if (isOpen) {
      fetchImages(currentPage);
    }
  }, [isOpen, currentPage]);

  // 获取预签名上传URL
  const getPresignedUrl = async (fileExtension) => {
    const response = await fetch(`/api/upload/generatePresignedUrl?fileExtension=${fileExtension}`);
    const result = await response.json();
    return result;
  };

  // 上传图片到对象存储
  const uploadImageToR2 = async (file) => {
    setUploadLoading(true);
    setShowLoadingModal(true);

    try {
      const fileExtension = file.name.split(".").pop().toLowerCase();
      console.log("fileExtension", fileExtension);
      const presignedUrlObject = await getPresignedUrl(fileExtension);
      const presignedUrl = presignedUrlObject.presignedUrl;
      const contentType = getContentTypeByFileExtension(fileExtension);
      console.log("presignedUrl》》》》》", presignedUrl);
      console.log("contentTyp》》》》》》", contentType);
      const response = await fetch(presignedUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": contentType,
        },
      });

      const status = await response.status;
      const objectUrl = presignedUrlObject.objectUrl;
      console.log("objectUrl", objectUrl);

      if (status === 200) {
        // 添加图片到数据库
        const imageResponse = await fetch("/api/manage/image/add", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            image_url: objectUrl,
            image_type: imageType,
            image_name: file.name.split(".")[0],
          }),
        });

        const imageResult = await imageResponse.json();

        if (imageResult.code === 200) {
          fetchImages(1); // 重新获取第一页
        }

        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("上传图片出错:", error);
      return false;
    } finally {
      setUploadLoading(false);
      setShowLoadingModal(false);
    }
  };

  // 删除图片
  const deleteImage = async (uid) => {
    if (!confirm("确定要删除这张图片吗？")) return;

    setShowLoadingModal(true);
    try {
      const response = await fetch("/api/manage/image/delete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ uid }),
      });

      const result = await response.json();

      if (result.code === 200) {
        fetchImages(currentPage); // 刷新当前页
      }
    } catch (error) {
      console.error("删除图片出错:", error);
    } finally {
      setShowLoadingModal(false);
    }
  };

  // 搜索按钮点击事件
  const handleSearch = () => {
    setCurrentPage(1); // 重置到第一页
    fetchImages(1);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">{imageType === "icon" ? "图标库" : "图片库"}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 focus:outline-none">
            <X size={20} />
          </button>
        </div>

        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索图片名称..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    fetchImages(1);
                  }
                }}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer" size={18} onClick={() => fetchImages(1)} />
            </div>

            <button
              onClick={() => {
                setSearchTerm(""); // 清空搜索条件
                fetchImages(1); // 使用空搜索条件刷新
              }}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
            >
              重置
            </button>

            <button onClick={handleSearch} className="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 focus:outline-none">
              搜索
            </button>
          </div>

          <label className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none cursor-pointer">
            <Upload size={18} className="mr-2" />
            <span>上传{imageType === "icon" ? "图标" : "图片"}</span>
            <input
              type="file"
              className="hidden"
              accept="image/*"
              onChange={async (e) => {
                const file = e.target.files?.[0];
                if (file) {
                  await uploadImageToR2(file);
                }
              }}
            />
          </label>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 size={32} className="animate-spin text-indigo-600" />
            </div>
          ) : images.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-64 text-gray-500">
              <ImageIcon size={48} className="mb-4 opacity-30" />
              <p>没有找到{imageType === "icon" ? "图标" : "图片"}</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {images.map((image) => (
                <div key={image.id} className="group relative border border-gray-200 rounded-md overflow-hidden hover:border-indigo-500 transition-colors p-2">
                  <div className="aspect-square flex items-center justify-center bg-gray-50 rounded cursor-pointer overflow-hidden" onClick={() => onSelect(image.image_url)}>
                    <img src={image.image_url} alt={image.image_name || "图片"} className="max-h-full max-w-full object-contain" />
                  </div>

                  <div className="mt-2 text-sm truncate text-center">{image.image_name || "未命名"}</div>

                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button onClick={() => deleteImage(image.uid)} className="p-1 bg-red-50 text-red-500 rounded-full hover:bg-red-100" title="删除">
                      <X size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="p-4 border-t flex items-center justify-between">
          <div className="text-sm text-gray-500">
            第 {currentPage} 页 / 共 {totalPages} 页
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage <= 1}
              className={`p-2 rounded-md ${currentPage <= 1 ? "text-gray-300 cursor-not-allowed" : "text-gray-700 hover:bg-gray-100"}`}
            >
              <ArrowLeft size={20} />
            </button>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage >= totalPages}
              className={`p-2 rounded-md ${currentPage >= totalPages ? "text-gray-300 cursor-not-allowed" : "text-gray-700 hover:bg-gray-100"}`}
            >
              <ArrowRight size={20} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageModal;
