'use client';

import React from 'react';
import { Typography, Box, Breadcrumbs, Link as MuiLink } from '@mui/material';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import HomeIcon from '@mui/icons-material/Home';

interface HeaderManageProps {
  title: string;
  locale?: string;
  subtitle?: string;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
}

export function HeaderManage({ title, locale = '', subtitle = '', breadcrumbs = [] }: HeaderManageProps) {
  const pathname = usePathname();

  // Generate default breadcrumbs based on pathname if not provided
  const defaultBreadcrumbs = React.useMemo(() => {
    if (breadcrumbs) return breadcrumbs;

    const pathSegments = pathname.split('/').filter(Boolean);
    const result = [];

    // The first segment is always locale, skip it
    // For /en/manage/games, pathSegments would be ['en', 'manage', 'games']

    // Always add dashboard as first breadcrumb
    result.push({
      label: '控制台',
      href: `/${locale}/manage`
    });

    // Add current section
    if (pathSegments.length > 2) {
      // The third segment should be the current section (e.g., 'games', 'pages', etc.)
      const currentSection = pathSegments[2];

      if (currentSection) {
        result.push({
          label: getSectionLabel(currentSection),
          href: `/${locale}/manage/${currentSection}`
        });
      }
    }

    // Add deeper levels (if any)
    if (pathSegments.length > 3) {
      const lastSegment = pathSegments[pathSegments.length - 1];
      const isIdOrSlug = !isNaN(Number(lastSegment)) || lastSegment === 'new' || lastSegment === 'edit';

      if (isIdOrSlug) {
        // If it's an ID or a special action (new/edit), use the previous segment for label
        result.push({
          label: lastSegment === 'new' ? '新建' :
            lastSegment === 'edit' ? '编辑' :
              '详情',
          href: pathname
        });
      }
    }

    return result;
  }, [pathname, locale, breadcrumbs]);

  function getSectionLabel(section: string): string {
    // Map section names to Chinese labels
    const sectionLabels: Record<string, string> = {
      'games': '游戏管理',
      'pages': '落地页管理',
      'sites': '站点管理',
      'templates': '模板管理',
      'prompts': 'Prompt管理',
      'recommendations': '推荐内容管理',
      'users': '用户管理'
    };

    return sectionLabels[section] || section;
  }

  return (
    <Box sx={{ mb: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <MuiLink
          component={Link}
          href={`/${locale}/manage`}
          underline="hover"
          sx={{ display: 'flex', alignItems: 'center' }}
          color="inherit"
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          首页
        </MuiLink>

        {defaultBreadcrumbs.map((crumb, index) => {
          const isLast = index === defaultBreadcrumbs.length - 1;

          return isLast ? (
            <Typography key={index} color="text.primary">
              {crumb.label}
            </Typography>
          ) : (
            <MuiLink
              key={index}
              component={Link}
              underline="hover"
              color="inherit"
              href={crumb.href || '#'}
            >
              {crumb.label}
            </MuiLink>
          );
        })}
      </Breadcrumbs>

      {/* Title */}
      <Typography variant="h4" component="h1" gutterBottom>
        {title}
      </Typography>

      {/* Optional subtitle */}
      {subtitle && (
        <Typography variant="subtitle1" color="text.secondary" paragraph>
          {subtitle}
        </Typography>
      )}
    </Box>
  );
} 