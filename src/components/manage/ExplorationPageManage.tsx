'use client';

import { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Pencil, Trash2, Plus } from 'lucide-react';
import { useToast } from '~/hooks/use-toast';

interface ExplorationPage {
  uid: string;
  site_uid: string;
  slug: string;
  exploration_sort_by: 'newest' | 'popular';
  title: string;
  description: string;
  h1: string;
  introduction: string;
  template_key?: string;
  created_at: string;
  updated_at: string;
}

interface ExplorationPageManageProps {
  siteUid: string;
}

export default function ExplorationPageManage({ siteUid }: ExplorationPageManageProps) {
  const [explorationPages, setExplorationPages] = useState<ExplorationPage[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [editingPage, setEditingPage] = useState<ExplorationPage | null>(null);
  const [loading, setLoading] = useState(false);
  const [siteDefaultTemplate, setSiteDefaultTemplate] = useState<string>(''); // 新增：站点默认模板
  const [templateLoading, setTemplateLoading] = useState(false); // 新增：模板加载状态
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    slug: '',
    exploration_sort_by: 'newest' as 'newest' | 'popular',
    title: '',
    description: '',
    h1: '',
    introduction: '',
    template_key: ''
  });

  useEffect(() => {
    if (siteUid) {
      fetchExplorationPages();
      fetchSiteDefaultTemplate();
    }
  }, [siteUid]);

  // 获取站点默认模板的函数
  const fetchSiteDefaultTemplate = async () => {
    try {
      setTemplateLoading(true);
      
      // 使用现有的站点列表API
      const response = await fetch('/api/manage/sites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          data: { page: 1, pageSize: 100 }
        })
      });
      
      const result = await response.json();
      
      if (result.status === 200 && result.data?.sites) {
        // 查找当前站点
        const currentSite = result.data.sites.find((site: any) => site.uid === siteUid);
        
        if (currentSite?.default_template_key) {
          setSiteDefaultTemplate(currentSite.default_template_key);
        } else {
          setSiteDefaultTemplate('default');
        }
      } else {
        setSiteDefaultTemplate('default');
      }
    } catch (error) {
      console.error('[ExplorationPageManage] 获取站点默认模板失败:', error);
      setSiteDefaultTemplate('default'); // 错误时使用fallback
    } finally {
      setTemplateLoading(false);
    }
  };

  const fetchExplorationPages = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/manage/exploration-pages?site_uid=${siteUid}`);
      const result = await response.json();
      if (result.code === 200) {
        setExplorationPages(result.data);
      }
    } catch (error) {
      console.error('Error fetching exploration pages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.slug || !formData.title) {
      toast({
        title: '错误',
        description: '请填写必填字段',
        variant: 'destructive',
      });
      return;
    }

    try {
      setLoading(true);
      const action = editingPage ? 'update' : 'create';
      const payload = {
        action,
        site_uid: siteUid,
        ...formData,
        ...(editingPage && { uid: editingPage.uid })
      };

      const response = await fetch('/api/manage/exploration-pages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.code === 200) {
        await fetchExplorationPages();
        setShowForm(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error saving exploration page:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (uid: string) => {
    if (!confirm('确定要删除这个探索页面吗？')) return;

    try {
      setLoading(true);
      const response = await fetch('/api/manage/exploration-pages', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'delete', uid })
      });

      const result = await response.json();
      if (result.code === 200) {
        await fetchExplorationPages();
      }
    } catch (error) {
      console.error('Error deleting exploration page:', error);
    } finally {
      setLoading(false);
    }
  };

  // 修改：显示表单时预填充默认模板
  const handleShowForm = () => {
    setShowForm(true);
    // 预填充站点默认模板
    setFormData(prev => ({
      ...prev,
      template_key: siteDefaultTemplate || 'default'
    }));
  };

  const handleEdit = (page: ExplorationPage) => {
    setEditingPage(page);
    setFormData({
      slug: page.slug,
      exploration_sort_by: page.exploration_sort_by,
      title: page.title,
      description: page.description,
      h1: page.h1,
      introduction: page.introduction,
      template_key: page.template_key || siteDefaultTemplate || 'default'
    });
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingPage(null);
    resetForm();
  };

  const resetForm = () => {
    setEditingPage(null);
    setFormData({
      slug: '',
      exploration_sort_by: 'newest',
      title: '',
      description: '',
      h1: '',
      introduction: '',
      template_key: ''
    });
  };

  if (showForm) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{editingPage ? '编辑探索页面' : '添加探索页面'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            {/* 页面标识 */}
            <div className="mb-4">
              <label className="block mb-1">页面标识 (Slug) *</label>
              <Input
                value={formData.slug}
                onChange={(e) => handleChange('slug', e.target.value)}
                placeholder="例如: new"
                required
              />
            </div>

            {/* 排序方式 */}
            <div className="mb-4">
              <label className="block mb-1">排序方式</label>
              <select
                value={formData.exploration_sort_by}
                onChange={(e) => handleChange('exploration_sort_by', e.target.value as 'newest' | 'popular')}
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="newest">最新游戏</option>
                <option value="popular">热门游戏</option>
              </select>
            </div>

            {/* 页面标题 */}
            <div className="mb-4">
              <label className="block mb-1">页面标题 *</label>
              <Input
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                required
              />
            </div>

            {/* 页面描述 */}
            <div className="mb-4">
              <label className="block mb-1">页面描述</label>
              <Textarea
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
              />
            </div>

            {/* H1 标题 */}
            <div className="mb-4">
              <label className="block mb-1">H1 标题</label>
              <Input
                value={formData.h1}
                onChange={(e) => handleChange('h1', e.target.value)}
              />
            </div>

            {/* 页面介绍 */}
            <div className="mb-4">
              <label className="block mb-1">页面介绍</label>
              <Textarea
                value={formData.introduction}
                onChange={(e) => handleChange('introduction', e.target.value)}
                rows={2}
              />
            </div>

            {/* 模板选择 */}
            <div className="mb-4">
              <label className="block mb-1">页面模板</label>
              <Input
                value={formData.template_key}
                onChange={(e) => handleChange('template_key', e.target.value)}
                placeholder={templateLoading ? "加载中..." : `站点默认: ${siteDefaultTemplate || 'default'}`}
                disabled={templateLoading}
              />
              <small className="text-gray-500 text-sm">
                {templateLoading ? (
                  "正在获取站点默认模板..."
                ) : (
                  <>
                    当前站点默认模板: <code className="bg-gray-100 px-1 rounded">{siteDefaultTemplate || 'default'}</code>
                    {" "}(可修改)
                  </>
                )}
              </small>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end gap-4">
              <button 
                type="button" 
                onClick={handleCancel} 
                className="exploration-page-btn"
                disabled={loading}
              >
                取消
              </button>
              <button 
                type="submit" 
                disabled={loading} 
                className="exploration-page-btn exploration-page-btn-primary"
              >
                {loading ? '保存中...' : (editingPage ? '更新' : '创建')}
              </button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>探索页面管理</CardTitle>
        <Button onClick={handleShowForm} disabled={loading || templateLoading}>
          <Plus className="w-4 h-4 mr-2" />
          添加探索页面
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <p className="text-center py-4">加载中...</p>
        ) : (
          <div className="space-y-4">
            {explorationPages.map((page) => (
              <div key={page.uid} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">{page.title}</p>
                  <p className="text-sm text-gray-600">/{page.slug}</p>
                </div>
                <div className="flex space-x-2">
                  <button
                    className="exploration-page-btn"
                    onClick={() => handleEdit(page)}
                    disabled={loading}
                    type="button"
                  >
                    <Pencil className="w-4 h-4" />
                  </button>
                  <button
                    className="exploration-page-btn exploration-page-btn-danger"
                    onClick={() => handleDelete(page.uid)}
                    disabled={loading}
                    type="button"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
            {explorationPages.length === 0 && !loading && (
              <p className="text-center text-gray-500 py-8">暂无探索页面</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
