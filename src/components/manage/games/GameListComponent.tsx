'use client'
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Game {
  id: number;
  keyword: string;
  name?: string;
  created_at: string;
  iframe_url?: string;
  tags?: any; // Could be string, array or null
  uid?: string;
}

const GameListComponent = ({ locale }) => {
  const router = useRouter();
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 10; // 可配置

  // 加载游戏列表数据的函数
  const loadGames = async (page = 1) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/manage/games?page=${page}&pageSize=${pageSize}`);
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }
      
      const result = await response.json();
      
      if (result.code === 200 && result.data && result.data.resultList) {
        setGames(result.data.resultList);
        setTotalCount(result.data.countTotal || 0);
        setTotalPages(result.data.totalPage || 1);
        setCurrentPage(page);
      } else {
        console.error('API返回错误: ', result);
        throw new Error(result.message || '获取游戏列表失败');
      }
    } catch (err) {
      console.error('加载游戏列表出错:', err);
      setError('无法加载游戏列表。请稍后再试。');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadGames(currentPage);
  }, [currentPage]);

  const handleDelete = async (game: Game) => {
    if (!confirm(`确定要删除游戏 "${game.keyword}" 吗？`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // 优先使用uid，如果不存在才回退到id
      const url = game.uid 
        ? `/api/manage/games/uid/${game.uid}`
        : `/api/manage/games/${game.id}`;
      
      const response = await fetch(url, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (response.ok && data.code === 200) {
        setGames(games.filter(g => g.id !== game.id));
        setTotalCount(prevCount => prevCount - 1);
        alert('游戏删除成功');
      } else {
        throw new Error(data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除游戏时出错:', error);
      alert('删除游戏失败: ' + (error.message || '未知错误'));
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to parse and handle tags
  const renderTags = (tags: any) => {
    if (!tags) return '-';
    
    let tagArray: string[] = [];
    
    // Parse tags if they're a string
    if (typeof tags === 'string') {
      try {
        tagArray = JSON.parse(tags);
      } catch (e) {
        // If it's not valid JSON, treat it as a single tag
        tagArray = [tags];
      }
    } 
    // If tags is already an array, use it
    else if (Array.isArray(tags)) {
      tagArray = tags;
    }
    
    if (tagArray.length === 0) return '-';
    
    return (
      <div className="flex flex-wrap gap-1">
        {tagArray.map((tag, index) => (
          <span key={index} className="px-2 py-1 bg-gray-200 text-xs rounded-full">
            {tag}
          </span>
        ))}
      </div>
    );
  };

  if (isLoading) {
    return <div className="text-center py-10">加载中...</div>;
  }

  if (error) {
    return <div className="text-red-600 text-center py-10">{error}</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">游戏列表</h1>
        <button
          onClick={() => router.push(`/${locale}/manage/games/new`)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          添加新游戏
        </button>
      </div>

      {games.length === 0 ? (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <p className="text-gray-500">暂无游戏记录</p>
          <button
            onClick={() => router.push(`/${locale}/manage/games/new`)}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            添加第一个游戏
          </button>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white rounded-lg overflow-hidden">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-left">ID</th>
                  <th className="py-3 px-4 text-left">关键词</th>
                  <th className="py-3 px-4 text-left">创建时间</th>
                  <th className="py-3 px-4 text-left">标签</th>
                  <th className="py-3 px-4 text-left">操作</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {games.map((game) => (
                  <tr key={game.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">{game.id}</td>
                    <td className="py-3 px-4">{game.keyword}</td>
                    <td className="py-3 px-4">{new Date(game.created_at).toLocaleDateString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{renderTags(game.tags)}</td>
                    <td className="px-6 py-4 whitespace-nowrap flex gap-2">
                      <button
                        onClick={() => router.push(`/${locale}/manage/games/uid/${game.uid}/edit`)}
                        className="px-3 py-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded text-sm"
                      >
                        编辑
                      </button>
                      <button
                        onClick={() => router.push(`/${locale}/manage/games/uid/${game.uid}/generate`)}
                        className="px-3 py-1 bg-green-100 hover:bg-green-200 text-green-700 rounded text-sm"
                      >
                        生成页面
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {totalPages > 1 && (
            <div className="mt-4 flex justify-center">
              <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  &laquo; 上一页
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  return (
                    <button
                      key={pageNum}
                      className={`relative inline-flex items-center px-4 py-2 border ${
                        currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      } text-sm font-medium`}
                      onClick={() => setCurrentPage(pageNum)}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                <button
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页 &raquo;
                </button>
              </nav>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default GameListComponent;