'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Game } from '~/utils/types/game';

interface GameDetailComponentProps {
  locale?: string;
  gameUid: string;
}

const GameDetailComponent = ({ locale, gameUid }: GameDetailComponentProps) => {
  const router = useRouter();
  const [game, setGame] = useState<Game | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Fetch game data
  useEffect(() => {
    const fetchGame = async () => {
      setIsLoading(true);
      setError('');
      
      try {
        // 使用统一的gameUid参数获取游戏数据
        const apiUrl = `/api/manage/games/uid/${gameUid}`;
          
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            uid: gameUid,
            action: 'get'
          })
        });
        
        if (!response.ok) {
          throw new Error(`获取游戏数据失败: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.code !== 200) {
          throw new Error(result.message || '获取游戏数据失败');
        }
        
        setGame(result.data);
      } catch (err) {
        console.error('获取游戏数据出错:', err);
        setError('无法加载游戏数据，请重试。');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchGame();
  }, [gameUid]);
  
  const handleDelete = async () => {
    if (!confirm('确认要删除这个游戏吗？')) {
      return;
    }
    
    try {
      const apiUrl = `/api/manage/games/uid/${gameUid}`;
        
      const response = await fetch(apiUrl, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      
      if (!response.ok || !result.success) {
        throw new Error(result.error || `错误: ${response.status}`);
      }
      
      // 删除成功后重定向
      router.push(`/${locale || 'en'}/manage/games`);
    } catch (err) {
      alert('删除游戏失败，请稍后再试。');
      console.error('删除游戏出错:', err);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
        <p className="text-gray-500">Loading game details...</p>
      </div>
    );
  }
  
  if (!game) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[calc(100vh-100px)]">
        <h2 className="text-2xl font-semibold text-red-600 mb-2">游戏不存在</h2>
        <p className="text-gray-700 mb-4">未找到请求的游戏信息</p>
        <button
          onClick={() => router.push(`/${locale || 'en'}/manage/games`)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          返回游戏列表
        </button>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">{game.keyword}</h1>
          <div className="flex gap-2">
            <button
              onClick={() => router.push(`/${locale || 'en'}/manage/games/uid/${gameUid}/edit`)}
              className="px-4 py-2 bg-yellow-100 hover:bg-yellow-200 text-yellow-700 rounded"
            >
              编辑
            </button>
            <button
              onClick={handleDelete}
              className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded"
            >
              删除
            </button>
            <button
              onClick={() => router.push(`/${locale || 'en'}/manage/games/uid/${gameUid}/generate`)}
              className="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 rounded"
            >
              生成页面
            </button>
            <button
              onClick={() => router.push(`/${locale || 'en'}/manage/games`)}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
            >
              返回列表
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-semibold mb-2 border-b pb-1">基本信息</h3>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">ID</p>
              <p>{game.id}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">UID</p>
              <p>{game.uid || '-'}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">关键词</p>
              <p>{game.keyword}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">创建时间</p>
              <p>{new Date(game.created_at).toLocaleString()}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">更新时间</p>
              <p>{new Date(game.updated_at).toLocaleString()}</p>
            </div>
            
            <div className="mb-3">
              <p className="text-gray-500 text-sm">标签</p>
              {game.tags && (Array.isArray(game.tags) ? (
                <div className="flex flex-wrap gap-1 mt-1">
                  {game.tags.map((tag, index) => (
                    <span key={index} className="bg-gray-200 px-2 py-1 text-xs rounded">
                      {tag}
                    </span>
                  ))}
                </div>
              ) : (
                <p>{game.tags}</p>
              ))}
            </div>
          </div>
          
          <div>
            <div className="mb-6">
              <h3 className="font-semibold mb-2 border-b pb-1">iframe URL</h3>
              {game.iframe_url ? (
                <>
                  <p className="mb-2 break-all">{game.iframe_url}</p>
                  <div className="mt-2">
                    <p className="text-gray-500 text-sm mb-1">预览：</p>
                    <div className="w-full h-60 border overflow-hidden">
                      <iframe
                        src={game.iframe_url}
                        className="w-full h-full"
                        title={game.keyword}
                        sandbox="allow-scripts allow-same-origin"
                      />
                    </div>
                  </div>
                </>
              ) : (
                <p className="text-gray-500">未设置</p>
              )}
            </div>
            
            <div>
              <h3 className="font-semibold mb-2 border-b pb-1">参考资料</h3>
              {game.reference_data ? (
                <div className="max-h-60 overflow-y-auto p-2 bg-gray-50 rounded">
                  <pre className="whitespace-pre-wrap text-sm">{game.reference_data}</pre>
                </div>
              ) : (
                <p className="text-gray-500">未提供参考资料</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameDetailComponent; 