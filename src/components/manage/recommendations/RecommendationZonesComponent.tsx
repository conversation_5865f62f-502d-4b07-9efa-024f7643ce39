import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { toast } from 'react-toastify';
import { EditOutlined, PlusOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

interface Zone {
  id: string;
  name: string;
}

interface RecommendationZonesComponentProps {
  siteUid: string;
  zones: Zone[];
  onZonesChange: (zones: Zone[]) => void;
  selectedZoneId: string;
  onZoneSelect: (zoneId: string) => void;
}

export default function RecommendationZonesComponent({ siteUid, zones, onZonesChange, selectedZoneId, onZoneSelect }: RecommendationZonesComponentProps) {
  const [editingZone, setEditingZone] = useState<Zone | null>(null);
  const [zoneName, setZoneName] = useState('');

  // 添加新分区
  const handleAdd = () => {
    setEditingZone({ id: '', name: '' });
    setZoneName('');
  };

  // 编辑分区
  const handleEdit = (zone: Zone) => {
    setEditingZone(zone);
    setZoneName(zone.name);
  };

  // 上移分区
  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const newZones = [...zones];
    [newZones[index - 1], newZones[index]] = [newZones[index], newZones[index - 1]];
    onZonesChange(newZones);
  };

  // 下移分区
  const handleMoveDown = (index: number) => {
    if (index === zones.length - 1) return;
    const newZones = [...zones];
    [newZones[index], newZones[index + 1]] = [newZones[index + 1], newZones[index]];
    onZonesChange(newZones);
  };

  // 提交表单
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!zoneName.trim()) {
      toast.error('请输入分区名称');
      return;
    }
    if (editingZone?.id) {
      // 编辑现有分区
      const newZones = zones.map(zone =>
        zone.id === editingZone.id ? { ...zone, name: zoneName.trim() } : zone
      );
      onZonesChange(newZones);
    } else {
      // 添加新分区
      const newZone = {
        id: `zone_${Date.now()}`,
        name: zoneName.trim(),
      };
      onZonesChange([...zones, newZone]);
    }
    setEditingZone(null);
    setZoneName('');
  };

  return (
    <Card className="mb-0">
      <CardHeader>
        <CardTitle>推荐分区</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <Button type="button" onClick={handleAdd} className="w-32 flex items-center gap-2">
            <PlusOutlined /> 添加分区
          </Button>
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200 bg-white">
              <tbody>
                {zones.map((zone, idx) => (
                  <tr key={zone.id} className={zone.id === selectedZoneId ? 'bg-blue-50' : ''}>
                    <td className="px-4 py-2 w-48">
                      <span
                        title={zone.name}
                        style={{
                          fontWeight: zone.id === selectedZoneId ? 'bold' : 'normal',
                          cursor: 'pointer',
                          color: zone.id === selectedZoneId ? '#1677ff' : undefined,
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: 120,
                          display: 'inline-block',
                        }}
                        onClick={() => onZoneSelect(zone.id)}
                      >
                        {zone.name}
                      </span>
                    </td>
                    <td className="px-2 py-2 w-32">
                      <div className="flex gap-1">
                        <Button type="button" size="sm" onClick={() => handleEdit(zone)} title="编辑">
                          <EditOutlined />
                        </Button>
                        <Button type="button" size="sm" onClick={() => handleMoveUp(idx)} disabled={idx === 0} title="上移">
                          <ArrowUpOutlined />
                        </Button>
                        <Button type="button" size="sm" onClick={() => handleMoveDown(idx)} disabled={idx === zones.length - 1} title="下移">
                          <ArrowDownOutlined />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {editingZone && (
            <form onSubmit={handleSubmit} className="flex items-end gap-4 mt-2">
              <div>
                <label className="block mb-1">分区名称</label>
                <Input
                  value={zoneName}
                  onChange={e => setZoneName(e.target.value)}
                  maxLength={20}
                  placeholder="请输入分区名称"
                  required
                />
              </div>
              <div className="flex gap-2 mb-1">
                <Button type="submit">{editingZone.id ? '更新' : '添加'}</Button>
                <Button type="button" onClick={() => { setEditingZone(null); setZoneName(''); }}>取消</Button>
              </div>
            </form>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 