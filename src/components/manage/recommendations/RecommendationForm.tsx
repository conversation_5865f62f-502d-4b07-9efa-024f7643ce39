'use client'

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, CardTitle } from '~/components/ui/card';
import { Input } from '~/components/ui/input';
import { Select } from '~/components/ui/select';
import { Button } from '~/components/ui/button';
import { Recommendation } from './RecommendationsList';
import { toast } from 'react-toastify';

interface Zone { id: string; name: string; }

interface RecommendationFormProps {
  siteId: string;
  recommendationId?: number;
  zones: Zone[];
  onCancel: () => void;
  onSuccess: () => void;
  locale: string;
  defaultZoneId?: string;
  initialData?: Recommendation;
}

export const RecommendationForm = ({
  siteId,
  recommendationId,
  zones,
  onCancel,
  onSuccess,
  locale,
  defaultZoneId,
  initialData,
}: RecommendationFormProps) => {
  const [loading, setLoading] = useState(false);
  const [pages, setPages] = useState<any[]>([]);
  const [formState, setFormState] = useState({
    zone_id: defaultZoneId || '',
    title: '',
    description: '',
    jump_url: '',
    sort_order: 0,
    cover_img_url: '',
  });

  // Effect to set form initial values when initialData is available
  useEffect(() => {
    if (initialData) {
      setFormState({
        zone_id: initialData.recommendation_zone || defaultZoneId || '',
        title: initialData.title || '',
        description: initialData.description || '',
        jump_url: initialData.jump_url || '',
        sort_order: initialData.sort_order || 0,
        cover_img_url: initialData.cover_img_url || '',
      });
    } else {
      setFormState({
        zone_id: defaultZoneId || '',
        title: '',
        description: '',
        jump_url: '',
        sort_order: 0,
        cover_img_url: '',
      });
    }
  }, [initialData, defaultZoneId]);

  // 获取已发布的页面列表
  useEffect(() => {
    const fetchPages = async () => {
      if (!siteId) return;
      setLoading(true);
      const apiUrl = `/api/manage/pages?siteUid=${siteId}&status=1`;
      try {
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error('获取页面列表失败');
        const result = await response.json();
        if (result.status === 200) {
          const filteredPages = (result.resultList || []).filter((page: any) => page.public_url != null && page.public_url !== '');
          setPages(filteredPages);
        }
      } catch (error) {
        toast.error('获取页面列表失败');
      } finally {
        setLoading(false);
      }
    };
    fetchPages();
  }, [siteId]);

  // 受控表单 onChange 处理
  const handleChange = (field: string, value: any) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  // 表单提交逻辑
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      // 校验
      if (!formState.zone_id) throw new Error('请选择推荐区域');
      if (!formState.title) throw new Error('请输入标题');
      if (!formState.jump_url) throw new Error('请输入跳转目标URL');
      if (!formState.cover_img_url) throw new Error('请输入封面图片URL');
      // ... 其他校验

      const url = '/api/manage/recommendations';
      const action = recommendationId ? 'update' : 'create';
      const cleanedDescription = formState.description ? formState.description.replace(/\/\/.*$/, '').trim() : null;
      const body: any = {
        action,
        siteUid: siteId,
        zone: formState.zone_id,
        title: formState.title,
        description: cleanedDescription,
        sort_order: formState.sort_order || 0,
        cover_img_url: formState.cover_img_url,
        jump_url: formState.jump_url,
      };
      if (recommendationId) body.id = recommendationId;
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });
      const result = await response.json();
      if (!response.ok || result.status !== 200) {
        throw new Error(result.message || '保存失败');
      }
      toast.success('保存成功');
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || '保存失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{recommendationId ? '编辑推荐' : '添加新推荐'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          {/* 推荐区域 */}
          <div className="mb-4">
            <label className="block mb-1">推荐区域</label>
            <select
              value={formState.zone_id}
              onChange={e => handleChange('zone_id', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
              required
            >
              <option value="" disabled>请选择推荐区域</option>
              {zones.map(zone => (
                <option key={zone.id} value={zone.id}>{zone.name}</option>
              ))}
            </select>
          </div>
          {/* 标题 */}
          <div className="mb-4">
            <label className="block mb-1">标题</label>
            <Input value={formState.title} onChange={e => handleChange('title', e.target.value)} required placeholder="请输入推荐标题" />
          </div>
          {/* 描述 */}
          <div className="mb-4">
            <label className="block mb-1">描述</label>
            <Input value={formState.description} onChange={e => handleChange('description', e.target.value)} placeholder="请输入推荐描述" />
          </div>
          {/* 封面图片 URL */}
          <div className="mb-4">
            <label className="block mb-1">封面图片URL</label>
            <Input value={formState.cover_img_url} onChange={e => handleChange('cover_img_url', e.target.value)} placeholder="请输入推荐图片URL" />
          </div>
          {/* 跳转目标URL */}
          <div className="mb-4">
            <label className="block mb-1">跳转目标URL</label>
            <Input
              type="url"
              value={formState.jump_url}
              onChange={e => handleChange('jump_url', e.target.value)}
              required
              placeholder="请输入推荐跳转目标URL"
            />
          </div>
          {/* 排序权重 */}
          <div className="mb-4">
            <label className="block mb-1">排序权重</label>
            <Input type="number" min={0} value={formState.sort_order} onChange={e => handleChange('sort_order', Number(e.target.value))} />
          </div>
          {/* 操作按钮 */}
          <div className="flex justify-end gap-4">
            <Button type="button" onClick={onCancel} className="ant-btn">取消</Button>
            <Button type="submit" disabled={loading} className="ant-btn ant-btn-primary">{recommendationId ? '保存' : '添加'}</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}; 