'use client'

import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { MoreHorizontal, Pencil, Trash2 } from 'lucide-react';
import { useToast } from '~/hooks/use-toast';
import { Skeleton } from '~/components/ui/skeleton';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '~/components/ui/alert-dialog';
import { Input } from '~/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Pagination } from '~/components/ui/pagination';

interface Zone {
  id: string;
  name: string;
}

interface RecommendationsListProps {
  siteId: string;
  onEdit: (id: number) => void;
  refreshTrigger?: boolean;
  locale: string;
}

export interface Recommendation {
  id: number;
  uid: string;
  site_uid: string;
  recommendation_zone: string;
  title: string;
  description: string | null;
  jump_url: string | null;
  sort_order: number | null;
  page_uid: string | null;
  cover_img_url?: string | null;
}

export function RecommendationsList({ siteId, onEdit, refreshTrigger = false, locale }: RecommendationsListProps) {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [zones, setZones] = useState<Zone[]>([]);
  const [selectedZone, setSelectedZone] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 10;
  const { toast } = useToast();

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('siteId', siteId);

      if (selectedZone && selectedZone !== 'all') {
        params.append('zone', selectedZone);
      }

      params.append('page', currentPage.toString());
      params.append('pageSize', pageSize.toString());

      const response = await fetch(`/api/manage/recommendations?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setRecommendations(data.data.items);
        setTotalPages(Math.ceil(data.data.total / pageSize));
      } else {
        console.error('Failed to fetch recommendations:', data.error);
        toast({
          title: '错误',
          description: '获取推荐列表失败',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      toast({
        title: '错误',
        description: '获取推荐列表失败',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchZones = async () => {
    try {
      const response = await fetch('/api/manage/recommendations/zones', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'get',
          siteUid: siteId,
        }),
      });

      const data = await response.json();

      if (response.ok && data.status === 200) {
        setZones(data.data);
      } else {
        const errorMessage = data.message || `获取推荐区域失败: ${response.status} ${response.statusText}`;
        console.error('Failed to fetch zones:', errorMessage);
        toast({
          title: '错误',
          description: errorMessage,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching zones:', error);
      toast({
        title: '错误',
        description: '获取推荐区域时发生未知错误',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    if (siteId) {
      fetchRecommendations();
      fetchZones();
    }
  }, [siteId, currentPage, selectedZone, refreshTrigger]);

  const filteredRecommendations = recommendations.filter(
    (rec) => rec.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatTargetInfo = (recommendation: Recommendation) => {
    const target_type = recommendation.page_uid ? 'INTERNAL_PAGE' : (recommendation.jump_url ? 'EXTERNAL_URL' : 'UNKNOWN');

    switch (target_type) {
      case 'INTERNAL_PAGE':
        return `内部页面 (UID: ${recommendation.page_uid})`;
      case 'EXTERNAL_URL':
        return `外部链接: ${recommendation.jump_url}`;
      default:
        return '未知';
    }
  };

  return (
    <div>
      <div className="flex gap-4 mb-4">
        <div className="flex-1">
          <Input
            placeholder="搜索推荐..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="w-[200px]">
          <select
            className="w-full p-2 border border-gray-300 rounded"
            value={selectedZone}
            onChange={e => setSelectedZone(e.target.value)}
          >
            <option value="all">所有区域</option>
            {zones.map(zone => (
              <option key={zone.id} value={zone.id}>{zone.name}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>标题</TableHead>
              <TableHead>推荐区域</TableHead>
              <TableHead>排序</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-6 w-40" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-10" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                </TableRow>
              ))
            ) : filteredRecommendations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  暂无推荐内容
                </TableCell>
              </TableRow>
            ) : (
              filteredRecommendations.map((recommendation) => (
                <TableRow key={recommendation.id}>
                  <TableCell className="font-medium">{recommendation.title}</TableCell>
                  <TableCell>{
                    zones.find(zone => zone.id === recommendation.recommendation_zone)?.name || '未分类'
                  }</TableCell>
                  <TableCell>{recommendation.sort_order}</TableCell>
                  <TableCell className="whitespace-nowrap">
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => onEdit(recommendation.id)}
                      className="px-0 text-blue-600 hover:text-blue-800"
                    >
                      编辑
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </div>
      )}
    </div>
  );
} 