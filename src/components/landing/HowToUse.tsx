'use client';

import { LandingComponentStyles } from "./types";

interface HowToUseItem {
  h3?: string;
  content?: string;
}

interface HowToUseProps {
  landingInfo: {
    h2?: string;
    content?: HowToUseItem[];
  };
  styles?: LandingComponentStyles;
  cardStyle?: React.CSSProperties;
  className?: string;
}

export default function HowToUse({ landingInfo, styles, cardStyle, className }: HowToUseProps) {
  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  return (
    <section id="how-to-use" className={className} style={styles}>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-8" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div className="grid grid-cols-1 gap-8">
          {landingInfo.content.map((item, index) => (
            <div
              key={index}
              className="flex gap-4 justify-center items-stretch mb-8"
              style={cardStyle}
            >
              {/* 序号圆圈 */}
              <div
                className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full text-base font-bold shadow-md"
                style={{ background: styles?.buttonBackgroundColor || undefined, color: styles?.buttonTextColor || undefined }}
              >
                {index + 1}
              </div>
              {/* 内容区，增加一层div做左右padding，提升移动端体验 */}
              <div className="flex-1" style={{ padding: '0 0.5rem', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                <>
                  {item.h3 && (
                    <h3 className="text-2xl font-semibold mb-3" style={{ color: styles?.h3Color || undefined }}>
                      {item.h3}
                    </h3>
                  )}
                  {item.content && (
                    <p className="text-base" style={{ color: styles?.descriptionColor || undefined }}>
                      {item.content}
                    </p>
                  )}
                </>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
