export interface LandingComponentStyles {
  h1Color?: string;
  h1Gradient?: string;
  h2Color?: string;
  h3Color?: string;
  titleColor?: string;
  descriptionColor?: string;
  backgroundColor?: string;
  borderRadius?: string;
  boxShadow?: string;
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  buttonBorderRadius?: string;
  buttonHoverBackgroundColor?: string;
  marginBottom?: string;
  marginTop?: string;
  backdropFilter?: string;
  padding?: string;
  tabColor?: string;
  cardBackgroundColor?: string;
  buttonColor?: string;
  textColor?: string;
  itemBackgroundColor?: string;
  itemTitleColor?: string;
  itemDescriptionColor?: string;
  headerBackgroundColor?: string;
  headerBoxShadow?: string;
  headerTextColor?: string;
  headerPadding?: string;
  headerBorderRadius?: string;
  headerBackdropFilter?: string;
  navLinkColor?: string;
  navLinkHoverColor?: string;
  navLinkBackground?: string;
  navLinkHoverBackground?: string;
  navLinkBorderRadius?: string;
  navLinkFontWeight?: number | string;
  sectionWidth?: string;
  sectionMaxWidth?: string;
  sectionMargin?: string;
  sectionBackground?: string;
  sectionBackdropFilter?: string;
  sectionBorderRadius?: string;
  sectionBoxShadow?: string;
  sectionPadding?: string;
}

// 评论专用样式接口
export interface CommentStyles {
  // 评论项样式
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  secondaryTextColor?: string;
  timeColor?: string;
  padding?: string;
  margin?: string;
  borderRadius?: string;
  backdropFilter?: string;
  boxShadow?: string;

  // 评论按钮样式
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  buttonHoverColor?: string;
  buttonBorderRadius?: string;
  buttonPadding?: string;
  buttonBorder?: string;

  // 评论输入框样式
  inputBackgroundColor?: string;
  inputBorderColor?: string;
  inputTextColor?: string;
  inputBorderRadius?: string;
  inputPadding?: string;
  inputFontSize?: string;
  placeholderColor?: string;

  // 评论标识样式
  badgeBackgroundColor?: string;
  badgeTextColor?: string;
  badgeBorderColor?: string;
  adminBadgeBackgroundColor?: string;
  adminBadgeTextColor?: string;
  adminBadgeBorderColor?: string;
}

// 推荐游戏数据接口
export interface RecommendedGame {
  title: string;
  image: string | null;
  cover_img_url?: string; // 新增封面图片字段，与 ExplorationGameList 保持一致
  link: string;
  description?: string;
  category?: string;
  rating?: number;
  playCount?: number;
}

// 推荐游戏样式接口
export interface RecommendedGameStyles {
  // 卡片样式
  cardBackgroundColor?: string;
  cardBorderColor?: string;
  cardBorderRadius?: string;
  cardPadding?: string;
  cardMargin?: string;
  cardBoxShadow?: string;
  cardBackdropFilter?: string;

  // 文本样式
  titleColor?: string;
  titleFontSize?: string;
  titleFontWeight?: string;
  descriptionColor?: string;
  descriptionFontSize?: string;

  // 按钮样式
  buttonBackgroundColor?: string;
  buttonTextColor?: string;
  buttonHoverBackgroundColor?: string;
  buttonBorderRadius?: string;
  buttonPadding?: string;
  buttonFontSize?: string;
  buttonFontWeight?: string;

  // 图片样式
  imageBackgroundColor?: string;
  imageBorderRadius?: string;

  // 侧边栏样式
  sidebarBackgroundColor?: string;
  sidebarBorderRadius?: string;
  sidebarPadding?: string;
  sidebarMargin?: string;
  sidebarBackdropFilter?: string;
  sidebarBoxShadow?: string;

  // 标题样式
  sidebarTitleColor?: string;
  sidebarTitleFontSize?: string;
  sidebarTitleFontWeight?: string;
}
