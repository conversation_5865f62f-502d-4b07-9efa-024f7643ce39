'use client';

import React from 'react';
import CommentList from '~/components/common/CommentList';
import { LandingComponentStyles, CommentStyles } from './types';

interface CommentsProps {
  landingInfo: {
    pageUid: string;
    h2?: string;
    placeholder?: string;
    maxComments?: number;
    showNewCommentForm?: boolean;
  };
  styles?: LandingComponentStyles;
  commentStyle?: CommentStyles;
  className?: string;
}

export default function Comments({
  landingInfo,
  styles,
  commentStyle,
  className = ''
}: CommentsProps) {
  const {
    pageUid,
    h2 = 'Comments',
    showNewCommentForm = true
  } = landingInfo;

  return (
    <section
      id="comments"
      className={`comments-section ${className}`}
      style={styles}
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 评论标题 */}
        {h2 && (
          <h2
            className="text-3xl md:text-4xl font-bold text-center mb-8"
            style={{ color: styles?.h2Color || undefined }}
          >
            {h2}
          </h2>
        )}

        {/* 评论列表 */}
        <CommentList
          pageUid={pageUid}
          showNewCommentForm={showNewCommentForm}
          className="comments-list"
          styles={styles}
          commentStyle={commentStyle}
        />
      </div>

    </section>
  );
}
