'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { LandingComponentStyles } from './types';

interface Game {
  title: string;
  image: string | null;
  link: string;
  description?: string;
}

interface GameListProps {
  games: Game[];
  styles?: LandingComponentStyles;
  className?: string;
  cardStyle?: React.CSSProperties;
  buttonStyle?: React.CSSProperties;
}

export default function GameList({ games, styles, className, cardStyle, buttonStyle }: GameListProps) {
  if (!games || games.length === 0) return null;

  return (
    <section
      id="game-list"
      className={`${className || ''}`}
      style={styles}
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-6" style={{ color: styles?.h2Color }}>
          Popular Games
        </h2>
        <p className="text-lg text-center mb-6" style={{ color: styles?.descriptionColor }}>
          Explore our carefully crafted game collection, each blending classic gameplay<br className="hidden md:block" /> with modern innovation
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {games.map((game, index) => {
            // console.log('GameList 卡片数据:', game);
            return (
              <div
                key={index}
                className="rounded-xl overflow-hidden transition-transform duration-300 hover:scale-105 group shadow-lg bg-white/60 backdrop-blur-md"
                style={{
                  ...cardStyle,                              
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div className="relative aspect-[4/3] overflow-hidden flex items-center justify-center" style={{ background: '#f3f4f6' }}>
                  <img
                    src={game.image && game.image !== '' ? game.image : '/icon/favicon.ico'}
                    alt={game.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    onError={(e) => {
                      e.currentTarget.src = "/icon/favicon.ico";
                      e.currentTarget.className = "w-full h-full object-contain bg-gray-800 p-4";
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                  <Link
                    href={game.link}
                    className="w-full text-center py-2 mb-4 mx-4 rounded-lg font-medium transition-all"
                    style={buttonStyle}
                  >
                    Play Now
                  </Link>
                </div>
                </div>
                <div className="flex flex-col p-2">
                  <p
                    className="text-base font-semibold text-center truncate"
                    style={{
                      color: styles?.textColor,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                    title={game.title}
                  >
                    {game.title}
                  </p>
                  {game.description && (
                    <p
                      className="text-sm text-center text-gray-700 mt-1 hidden md:block line-clamp-3"
                      style={{
                        color: styles?.descriptionColor,
                        display: '-webkit-box',
                        WebkitLineClamp: 3,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxHeight: '4.5em', // 3行 * 1.5em
                      }}
                    >
                      {game.description}
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 