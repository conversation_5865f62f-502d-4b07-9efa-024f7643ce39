'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { LandingComponentStyles } from './types';

interface Game {
  title: string;
  image: string | null;
  link: string;
}

interface GameRecommendationsProps {
  landingInfo: { [zoneName: string]: Game[] }; // Allow dynamic zone keys
  styles?: LandingComponentStyles;
}

export default function GameRecommendations({ landingInfo, styles }: GameRecommendationsProps) {
  // Get available zones based on non-empty game arrays
  const availableZones = Object.keys(landingInfo)
    .filter(zoneName => Array.isArray(landingInfo[zoneName]) && landingInfo[zoneName].length > 0);

  // Set initial active tab state to empty string, will be set in useEffect on client
  const [activeTab, setActiveTab] = useState('');

  // Set active tab to the first available zone after component mounts on the client
  useEffect(() => {
    if (availableZones.length > 0 && activeTab === '') {
      setActiveTab(availableZones[0]);
    }
  }, [availableZones, activeTab]); // Re-run effect if availableZones changes or activeTab is still empty

  // 获取当前活动标签的游戏列表
  const getActiveGames = () => {
    const zoneKey = activeTab;
    const games = landingInfo[zoneKey] || [];
    return games;
  };

  // Do not render content that depends on activeTab until it's set (client-side)
  if (activeTab === '') {
      return null; // Return null or a loading state on server and before client effect runs
  }

  // If activeTab is set and there are no games for it, maybe also return null or a message
  const activeGames = getActiveGames();
  if (activeGames.length === 0) {
      return null;
  }

  return (
    <section
      id="gameRecommendations"
      className="py-12 lg:py-16"
      style={styles}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: styles?.h2Color }}>
          Explore More Games
        </h2>
        {/* Dynamic Tabs */}
        <div className="text-center mb-6">
          <div className="justify-center mb-4 border-b border-gray-700">
            {availableZones.map((zone) => (
              <button
                key={zone}
                onClick={() => setActiveTab(zone)}
                className={`px-3 py-2 text-base font-medium transition-all ${activeTab === zone ? 'border-b-2 -mb-px' : 'text-gray-400 hover:text-gray-300'}`}
                style={{
                  borderColor: activeTab === zone ? styles?.tabColor : 'transparent',
                  color: activeTab === zone ? styles?.titleColor : undefined,
                  background: activeTab === zone ? styles?.buttonBackgroundColor : undefined,
                  borderRadius: styles?.buttonBorderRadius,
                  fontWeight: activeTab === zone ? 600 : 400,
                }}
              >
                {zone}
              </button>
            ))}
          </div>
        </div>
        {/* Game Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {activeGames.map((game, index) => (
            <div
              key={index}
              className="rounded-lg overflow-hidden transition-transform duration-300 hover:scale-105 group"
              style={{
                background: styles?.cardBackgroundColor || 'rgba(17, 24, 39, 0.7)',
                borderRadius: styles?.borderRadius,
                boxShadow: styles?.boxShadow,
              }}
            >
              <div className="relative aspect-[4/3] overflow-hidden">
                <img
                  src={(game.image && game.image !== '') ? game.image : '/icon/favicon.ico'}
                  alt={game.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = "/icon/favicon.ico";
                    e.currentTarget.className = "w-full h-full object-contain bg-gray-800 p-4";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                  <Link
                    href={game.link}
                    className="w-full text-center py-2 mb-4 mx-4 rounded-lg font-medium transition-all"
                    style={{
                      background: styles?.buttonBackgroundColor || styles?.buttonColor || '#4f46e5',
                      color: styles?.buttonTextColor || '#ffffff',
                      borderRadius: styles?.buttonBorderRadius,
                    }}
                  >
                    Play Now
                  </Link>
                </div>
              </div>
              <div className="p-4">
                <p
                  className="text-xl font-semibold mb-1 truncate"
                  style={{ color: styles?.textColor || styles?.itemTitleColor || '#ffffff' }}
                >
                  {game.title}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 