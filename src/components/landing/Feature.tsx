"use client";
import Markdown from "react-markdown";
import { useState, useEffect } from "react";
import { useCommonContext } from "~/context/common-context";
import { getGridCol } from "~/utils/strUtil";
import { LandingComponentStyles } from "./types";

interface FeatureItem {
  h3?: string;
  content?: string;
  icon?: string;
}

interface featureProps {
  landingInfo: {
    h2?: string;
    content?: FeatureItem[];
  };
  styles?: LandingComponentStyles;
  cardStyle?: React.CSSProperties;
  className?: string;
}

export default function Feature({ landingInfo, styles, cardStyle, className }: featureProps) {
  const { mobile } = useCommonContext();

  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  return (
    <section id="features" className={className} style={styles}>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-8" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8"
        >
          {landingInfo.content.map((item, index) => (
            <div
              key={index}
              className="flex flex-col items-center text-center"
              style={cardStyle}
            >
              {/* Icon rendering logic can be added here if needed */}
              <h3 className="text-2xl font-semibold mb-4" style={{ color: styles?.h3Color || undefined }}>{item.h3}</h3>
              <p className="text-base" style={{ color: styles?.descriptionColor || undefined }}>{item.content}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
