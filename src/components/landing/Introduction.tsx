'use client';

import Markdown from "react-markdown";
import { LandingComponentStyles } from "./types";

interface IntroductionProps {
  landingInfo: {
    h2?: string;
    content?: string;
  };
  styles?: LandingComponentStyles;
  className?: string;
}

export default function Introduction({ landingInfo, styles, className }: IntroductionProps) {
  if (!landingInfo) {
    return null; // Don't render if no data
  }

  return (
    <section id="introduction" className={className} style={styles}>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12" style={{ maxWidth: 700, margin: '0 auto' }}>
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-6" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
          <p className="text-lg leading-relaxed text-center" style={{ color: styles?.descriptionColor || undefined }}>{landingInfo.content}</p>
        </div>
      </div>
    </section>
  );
}
