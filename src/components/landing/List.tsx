import { useState } from "react";
import { LandingComponentStyles } from "./types";

interface ListProps {
  landingInfo: {
    id?: string;
    title: string;
    description?: string;
    items: {
      name: string;
      description: string;
      banner: string;
      link: string;
    }[];
  };
  styles?: LandingComponentStyles;
}

export default function ListSection({ landingInfo, styles }: ListProps) {
  const {
    backgroundColor = "",
    titleColor = "",
    descriptionColor = "",
    itemBackgroundColor = "#1a1a1a",
    itemTitleColor = "#e4e4e4",
    itemDescriptionColor = "#94a3b8"
  } = styles || {};

  return (
    <div {...(landingInfo?.id ? { id: landingInfo.id } : {})} className="py-4 xl:py-6 my-auto" style={{ backgroundColor }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="pt-16 pb-12 md:pt-32 md:pb-20">
          {/* Section header */}
          <div className="max-w-7xl mx-auto text-center pb-12 md:pb-20">
            {landingInfo.title && (
              <h2 className="h2 bg-clip-text pb-4" style={{ color: titleColor }}>
                {landingInfo.title}
              </h2>
            )}
            {landingInfo.description && (
              <p className="text-lg" style={{ color: descriptionColor }}>
                {landingInfo.description}
              </p>
            )}
          </div>

          <div className="relative">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {landingInfo.items?.map?.((item, index) => (
                <a href={item.link} key={index} className="group relative overflow-hidden rounded-lg transition-all duration-300 hover:scale-105" style={{ backgroundColor: itemBackgroundColor }}>
                  <div className="aspect-w-16 aspect-h-9 h-[200px] overflow-hidden rounded-lg">
                    <img src={item.banner} alt={item.name} className="h-full w-full object-cover" />
                  </div>
                  <div className="p-6">
                    {item.name && (
                      <h3 className="font-inter-tight text-lg font-semibold mb-2" style={{ color: itemTitleColor }}>
                        {item.name}
                      </h3>
                    )}
                    {item.description && (
                      <p className="text-sm line-clamp-2" style={{ color: itemDescriptionColor }}>
                        {item.description}
                      </p>
                    )}
                  </div>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
