'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { LandingComponentStyles } from '~/components/landing/types';
import { ExplorationGamesResult } from '~/lib/api/pages';
import ExplorationGameList from '~/components/landing/ExplorationGameList';

interface ExplorationContentProps {
  pageData: {
    page: {
      json_content: any;
      title: string;
    };
  };
  gamesResult: ExplorationGamesResult;
  styles?: LandingComponentStyles;
  cardStyle?: React.CSSProperties;
  buttonStyle?: React.CSSProperties;
  className?: string;
}

export default function ExplorationContent({
  pageData,
  gamesResult,
  styles,
  cardStyle, 
  buttonStyle,
  className
}: ExplorationContentProps) {

  const router = useRouter();
  const searchParams = useSearchParams();

  const content = pageData.page.json_content || {};

  // 提取页面内容
  const title = content.title || pageData.page.title || 'Explore Games';
  const h1 = content.h1 || title;
  const introduction = content.introduction || '';

  // 分页处理函数
  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());

    const newUrl = `${window.location.pathname}?${params.toString()}`;

    // 使用 Next.js 路由进行页面导航，现在服务端组件应该能响应参数变化
    router.push(newUrl);
    router.refresh(); // 强制刷新服务端组件以重新获取数据
  };

  return (
    <main className="relative z-10">
      {/* Hero Section */}
      <section className="py-8 lg:py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* 主标题 */}
          <h1
            className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight"
            style={{color: styles.h1Color }}
          >
            {h1}
          </h1>

          {/* 描述文本 - 移动端隐藏 */}
          {introduction && (
            <p
              className="hidden md:block text-lg md:text-xl max-w-4xl mx-auto mt-6 leading-relaxed"
              style={{color: styles.descriptionColor}}
            >
              {introduction}
            </p>
          )}

        </div>

        {/* 游戏列表区域 */}      
        <ExplorationGameList
          gamesResult={gamesResult}
          onPageChange={handlePageChange}
          styles={styles}
          cardStyle={cardStyle}
          buttonStyle={buttonStyle}
          className={className}
        />

      </section>
      
    </main>
  );
}
