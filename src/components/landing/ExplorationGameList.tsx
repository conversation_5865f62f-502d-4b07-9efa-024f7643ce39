'use client';

import React from 'react';
import Link from 'next/link';
import { LandingComponentStyles } from './types';
import { ExplorationGamesResult } from '~/lib/api/pages';

interface ExplorationGameListProps {
  gamesResult: ExplorationGamesResult;
  styles?: LandingComponentStyles;
  className?: string;
  cardStyle?: React.CSSProperties;
  buttonStyle?: React.CSSProperties;
  onPageChange?: (page: number) => void;
}

export default function ExplorationGameList({ 
  gamesResult, 
  styles, 
  className,
  cardStyle,
  buttonStyle,
  onPageChange 
}: ExplorationGameListProps) {
  
  const { games, currentPage, totalPages } = gamesResult;

  // 分页处理
  const handlePageChange = (newPage: number) => {
    if (onPageChange) {
      onPageChange(newPage);
    } else {
      const url = new URL(window.location.href);
      url.searchParams.set('page', newPage.toString());
      window.location.href = url.toString();
    }
  };

  // 按钮基础样式 - 遵循现有组件模式
  const baseButtonStyle = {
    backdropFilter: styles?.backdropFilter || 'blur(10px)',
    WebkitBackdropFilter: styles?.backdropFilter || 'blur(10px)',
    transition: 'all 0.3s ease',
    borderRadius: styles?.buttonBorderRadius || '8px',
    ...buttonStyle,
  };

  // 生成分页组件
  const renderPagination = () => {
    if (totalPages <= 1) {
      return (
        <div className="flex flex-col items-center space-y-4 mt-6">
          <div className="flex justify-center items-center">
            <button
              className="px-2 sm:px-3 py-2 text-sm font-medium rounded-lg cursor-default"
              style={{
                ...baseButtonStyle,
                background: styles?.buttonBackgroundColor || '#FF9500',
                color: styles?.buttonTextColor || '#ffffff',
              }}
              title="Current page"
            >
              1
            </button>
          </div>
        </div>
      );
    }

    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // 主要按钮样式（上一页/下一页）
    const primaryButtonStyle = {
      ...baseButtonStyle,
      background: styles?.buttonBackgroundColor || '#FF9500',
      color: styles?.buttonTextColor || '#ffffff',
    };

    // 普通页码按钮样式
    const normalButtonStyle = {
      ...baseButtonStyle,
      background: styles?.cardBackgroundColor,
      color: styles?.textColor,
    };

    // 当前页码按钮样式
    const activeButtonStyle = {
      ...baseButtonStyle,
      background: styles?.buttonBackgroundColor || '#FF9500',
      color: styles?.buttonTextColor || '#ffffff',
    };

    return (
      <div className="flex flex-col items-center space-y-4 mt-6">
        <div className="flex justify-center items-center space-x-1 sm:space-x-2 flex-wrap gap-y-2">
          {/* 上一页 */}
          {currentPage > 1 && (
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              className="px-3 sm:px-4 py-2 text-sm font-medium rounded-lg hover:scale-105 active:scale-95"
              style={primaryButtonStyle}
              title="Previous page"
            >
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">‹</span>
            </button>
          )}

          {/* 首页 */}
          {startPage > 1 && (
            <>
              <button
                onClick={() => handlePageChange(1)}
                className="px-2 sm:px-3 py-2 text-sm font-medium rounded-lg hover:scale-105 active:scale-95"
                style={normalButtonStyle}
                title="Go to page 1"
              >
                1
              </button>
              {startPage > 2 && (
                <span
                  className="px-1 sm:px-2 text-sm"
                  style={{ color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.5)' }}
                >
                  ...
                </span>
              )}
            </>
          )}

          {/* 页码范围 */}
          {Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i).map(pageNum => (
            <button
              key={pageNum}
              onClick={() => handlePageChange(pageNum)}
              className="px-2 sm:px-3 py-2 text-sm font-medium rounded-lg hover:scale-105 active:scale-95"
              style={pageNum === currentPage ? activeButtonStyle : normalButtonStyle}
              title={`Go to page ${pageNum}`}
            >
              {pageNum}
            </button>
          ))}

          {/* 末页 */}
          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && (
                <span
                  className="px-1 sm:px-2 text-sm"
                  style={{ color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.5)' }}
                >
                  ...
                </span>
              )}
              <button
                onClick={() => handlePageChange(totalPages)}
                className="px-2 sm:px-3 py-2 text-sm font-medium rounded-lg hover:scale-105 active:scale-95"
                style={normalButtonStyle}
                title={`Go to page ${totalPages}`}
              >
                {totalPages}
              </button>
            </>
          )}

          {/* 下一页 */}
          {currentPage < totalPages && (
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              className="px-3 sm:px-4 py-2 text-sm font-medium rounded-lg hover:scale-105 active:scale-95"
              style={primaryButtonStyle}
              title="Next page"
            >
              <span className="hidden sm:inline">Next</span>
              <span className="sm:hidden">›</span>
            </button>
          )}
        </div>
      </div>
    );
  };

  return (
    <section
      id="explorationGameList"
      className={className}
      style={styles}
    >
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 游戏网格 */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
          {games.map((game) => (
            <div
              key={game.uid}
              className="rounded-xl overflow-hidden transition-transform duration-300 hover:scale-105 group"
              style={{
                ...cardStyle,
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <div className="relative aspect-square overflow-hidden">
                <img
                  src={(game.cover_img_url && game.cover_img_url !== '') ? game.cover_img_url : '/icon/favicon.ico'}
                  alt={game.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = "/icon/favicon.ico";
                    e.currentTarget.className = "w-full h-full object-contain bg-gray-800 p-4";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                  <Link
                    href={game.jump_url}
                    className="w-full text-center py-2 mb-4 mx-4 rounded-lg font-medium transition-all"
                    style={buttonStyle}
                  >
                    Play Now
                  </Link>
                </div>
              </div>
              <div className="flex flex-col p-2">
                <p
                  className="text-base font-semibold truncate"
                  style={{ color: styles?.textColor }}
                >
                  {game.title}
                </p>
                {game.description && (
                  <p
                    className="text-sm md:block line-clamp-3"
                    style={{ 
                      color: styles?.descriptionColor,
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}
                  >
                    {game.description}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 分页 */}
        {renderPagination()}
      </div>
    </section>
  );
}
