'use client';

import Link from 'next/link';
import Markdown from 'react-markdown';
import { LandingComponentStyles } from './types';

interface CtaProps {
  landingInfo: {
    h2?: string;
    content?: string;
    buttonText?: string;
    buttonLink?: string; // Assuming a link for the button
  };
  styles?: LandingComponentStyles;
  className?: string;
}

export default function Cta({ landingInfo, styles, className }: CtaProps) {
  if (!landingInfo || !landingInfo.h2 || !landingInfo.content) {
    return null; // Don't render if essential data is missing
  }

  const scrollToGame = () => {
    const gameIframe = document.getElementById('game-iframe');
    if (gameIframe) {
      gameIframe.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="cta" className={`text-center ${className || ''}`} style={styles}>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold mb-6" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <p className="text-lg mb-8" style={{ color: styles?.descriptionColor || undefined }}>{landingInfo.content}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {landingInfo.buttonText && landingInfo.buttonLink && (
            <Link 
              href={landingInfo.buttonLink}
              className="inline-block text-xl font-semibold px-8 py-3 rounded-lg transition-colors duration-300"
              style={{
                backgroundColor: styles?.buttonBackgroundColor,
                color: styles?.buttonTextColor,
                borderRadius: styles?.buttonBorderRadius,
              }}
              onMouseOver={e => { if(styles?.buttonHoverBackgroundColor) e.currentTarget.style.backgroundColor = styles.buttonHoverBackgroundColor; }}
              onMouseOut={e => { if(styles?.buttonBackgroundColor) e.currentTarget.style.backgroundColor = styles.buttonBackgroundColor; }}
            >
              {landingInfo.buttonText}
            </Link>
          )}
          <button
            onClick={scrollToGame}
            className="inline-block text-xl font-semibold px-8 py-3 rounded-lg transition-colors duration-300"
            style={{
              backgroundColor: styles?.buttonBackgroundColor,
              color: styles?.buttonTextColor,
              borderRadius: styles?.buttonBorderRadius,
            }}
            onMouseOver={e => { if(styles?.buttonHoverBackgroundColor) e.currentTarget.style.backgroundColor = styles.buttonHoverBackgroundColor; }}
            onMouseOut={e => { if(styles?.buttonBackgroundColor) e.currentTarget.style.backgroundColor = styles.buttonBackgroundColor; }}
          >
            {landingInfo.buttonText || 'Play Now'}
          </button>
        </div>
      </div>
    </section>
  );
}
