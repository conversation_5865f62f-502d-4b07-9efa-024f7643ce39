'use client';

import { useState } from "react";
import { LandingComponentStyles } from "./types";

interface FaqItem {
  h3?: string;
  content?: string;
}

interface FaqProps {
  landingInfo: {
    h2?: string;
    content?: FaqItem[];
  };
  styles?: LandingComponentStyles;
  className?: string;
}

export default function Faq({ landingInfo, styles, className }: FaqProps) {
  // State to manage which FAQ is currently open
  // Moved useState before the early return to fix the React Hook error.
  const [openFAQ, setOpenFAQ] = useState(null);

  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  // Function to toggle FAQ open/close
  const toggleFAQ = (index) => {
    // Toggle the FAQ: if already open, close it; if closed, open it.
    setOpenFAQ(openFAQ === index ? null : index);
  };

  // Function to render heading based on type
  const renderHeading = (question: string, type: string, color?: string) => {
    const className = "font-bold";
    const style = { color };

    switch (type) {
      case "h2":
        return (
          <h2 className={`text-3xl ${className}`} style={style}>
            {question}
          </h2>
        );
      case "h3":
        return (
          <h3 className={`text-2xl ${className}`} style={style}>
            {question}
          </h3>
        );
      default:
        return (
          <h4 className={`text-xl ${className}`} style={style}>
            {question}
          </h4>
        );
    }
  };

  return (
    <section id="faq" className={`py-12 lg:py-16 ${className || ''}`} style={styles}>
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div className="divide-y divide-gray-700">
          {landingInfo.content.map((item, index) => (
            <div key={index} className="py-4 cursor-pointer select-none" onClick={() => toggleFAQ(index)}>
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold mb-0" style={{ color: styles?.h3Color || undefined }}>{item.h3}</h3>
                <span className="ml-2 text-lg text-gray-400">{openFAQ === index ? '−' : '+'}</span>
              </div>
              <p
                className={`prose mt-3 transition-all duration-300 overflow-hidden ${openFAQ === index ? '' : 'hidden'}`}
                style={{ color: styles?.descriptionColor || undefined }}
                aria-hidden={openFAQ === index ? 'false' : 'true'}
              >
                {item.content}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
