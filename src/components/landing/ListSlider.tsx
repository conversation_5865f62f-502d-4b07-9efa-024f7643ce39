import { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import { LandingComponentStyles } from "./types";
// 引入必要的 CSS
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface ListSliderProps {
  landingInfo: {
    title: string;
    description?: string;
    items: {
      name: string;
      description: string;
      banner: string;
      link: string;
    }[];
  };
  styles?: LandingComponentStyles;
}

export default function ListSection({ landingInfo, styles }: ListSliderProps) {
  const [isMobile, setIsMobile] = useState(false);
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);

  const {
    backgroundColor = "",
    titleColor = "",
    descriptionColor = "",
    itemBackgroundColor = "#1a1a1a",
    itemTitleColor = "#e4e4e4",
    itemDescriptionColor = "#94a3b8"
  } = styles || {};

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <div className="py-4 xl:py-6 my-auto" style={{ backgroundColor }}>
      <div className="max-w-7xl mx-auto ">
        <div className="pt-16 pb-12 md:pt-32 md:pb-20">
          {/* Section header */}
          <div className="max-w-7xl mx-auto text-center pb-12 md:pb-20">
            {landingInfo.title && (
              <h2 className="h2 bg-clip-text pb-4" style={{ color: titleColor }}>
                {landingInfo.title}
              </h2>
            )}
            {landingInfo.description && (
              <p className="text-lg" style={{ color: descriptionColor }}>
                {landingInfo.description}
              </p>
            )}
          </div>

          <div className="relative">
            <Swiper
              modules={[Navigation, Pagination]}
              spaceBetween={24}
              slidesPerView={isMobile ? 1 : 4}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              pagination={{
                clickable: true,
                el: ".swiper-pagination",
              }}
              className="overflow-visible"
              wrapperClass="!items-stretch"
              onSlideChange={(swiper) => {
                setIsBeginning(swiper.isBeginning);
                setIsEnd(swiper.isEnd);
              }}
            >
              {landingInfo.items?.map?.((item, index) => (
                <SwiperSlide key={index}>
                  <a href={item.link} className="group block h-full relative overflow-hidden rounded-lg transition-all duration-300 hover:scale-105" style={{ backgroundColor: itemBackgroundColor }}>
                    <div className="aspect-w-16 aspect-h-9 h-[250px] overflow-hidden rounded-lg">
                      <img src={item.banner} alt={item.name} className="h-full w-full object-cover" />
                    </div>
                    <div className="p-6">
                      {item.name && (
                        <h3 className="font-inter-tight text-lg font-semibold mb-2" style={{ color: itemTitleColor }}>
                          {item.name}
                        </h3>
                      )}
                      {item.description && (
                        <p className="text-sm line-clamp-2" style={{ color: itemDescriptionColor }}>
                          {item.description}
                        </p>
                      )}
                    </div>
                  </a>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* 修改导航按钮的实现 */}
            <button
              className={`swiper-button-prev !w-10 !h-10 !bg-white/30 hover:!bg-white/50 rounded-full backdrop-blur-sm transition-all duration-300 !after:!text-white ${
                isBeginning ? "opacity-0 pointer-events-none" : "opacity-100"
              }`}
            >
              <span className="sr-only">Previous</span>
            </button>
            <button
              className={`swiper-button-next !w-10 !h-10 !bg-white/30 hover:!bg-white/50 rounded-full backdrop-blur-sm transition-all duration-300 !after:!text-white ${
                isEnd ? "opacity-0 pointer-events-none" : "opacity-100"
              }`}
            >
              <span className="sr-only">Next</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
