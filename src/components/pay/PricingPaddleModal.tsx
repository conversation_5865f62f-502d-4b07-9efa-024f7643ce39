import { Fragment, useRef, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useCommonContext } from "~/context/common-context";
import { getLinkHrefWithoutStartSlash } from "~/utils/buildLink";
import PricingPaddleComponent from "~/components/pay/PricingPaddleComponent";
import { getPricingWay } from '~/configs/pay/payUtil';

export default function PricingModal({
  locale,
  page
}) {

  const [redirectUrl] = useState(getLinkHrefWithoutStartSlash(locale, page));

  const { showPayModal } = useCommonContext();

  return (
    <Transition.Root show={getPricingWay() == "1" ? showPayModal : false} as={Fragment}>
      <Dialog as="div" className="relative z-40" onClose={() => null}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4"
              enterTo="opacity-100 translate-y-0"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-4"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl">
                <PricingPaddleComponent
                  locale={locale}
                  redirectUrl={redirectUrl}
                />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
