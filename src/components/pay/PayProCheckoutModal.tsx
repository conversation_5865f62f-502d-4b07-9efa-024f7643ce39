
import { Fragment, useRef, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { useCommonContext } from "~/context/common-context";

export default function PayProCheckoutModal({ checkoutUrl }) {

  const { showPayProCheckoutModal, setShowPayProCheckoutModal, setIntervalAvailableTimes } = useCommonContext();

  return (
    <Transition.Root show={showPayProCheckoutModal} as={Fragment}>
      <Dialog as="div" className="relative z-40" onClose={() => null}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-30 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4"
              enterTo="opacity-100 translate-y-0"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-4"
            >
              <Dialog.Panel className="relative w-full h-[95vh] max-w-7xl transform overflow-hidden rounded-lg bg-white text-left shadow-2xl transition-all">
                <button
                  type="button"
                  className="absolute right-4 top-4 z-10 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none"
                  onClick={() => {
                    setShowPayProCheckoutModal(false);
                    setIntervalAvailableTimes(1000);
                  }}
                >
                  <span className="sr-only">关闭</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <iframe
                  src={checkoutUrl}
                  frameBorder="0"
                  className="w-full h-full"
                ></iframe>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
