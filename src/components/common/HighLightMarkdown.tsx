import React from "react";
import ReactMarkdown from "react-markdown";
import { Prism as Syntax<PERSON><PERSON>lighter } from "react-syntax-highlighter";
import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";
import remarkGfm from "remark-gfm";
import { handleBlogUrl } from "~/utils/buildLink";

const HighLightMarkdown = ({ markdown }) => {
  const components = {
    // 添加 heading 组件配置
    h1: ({ node, ...props }) => <h1 id={handleBlogUrl(props.children)} {...props} />,
    h2: ({ node, ...props }) => <h2 id={handleBlogUrl(props.children)} {...props} />,
    h3: ({ node, ...props }) => <h3 id={handleBlogUrl(props.children)} {...props} />,
    h4: ({ node, ...props }) => <h4 id={handleBlogUrl(props.children)} {...props} />,
    h5: ({ node, ...props }) => <h5 id={handleBlogUrl(props.children)} {...props} />,

    code: ({ node, inline, className, children, ...props }) => {
      const match = className?.match(/language-(\w+)/);
      const language = match?.[1] || "javascript";
      const childrenStr = String(children);

      if (inline || childrenStr.split("\n").length === 1) {
        return (
          <code className={className} {...props}>
            {children}
          </code>
        );
      }

      return (
        <SyntaxHighlighter
          language={language}
          customStyle={{
            padding: 0,
            display: "block",
            border: "none",
            background: "transparent",
            overflow: "initial",
          }}
          PreTag="div"
          {...props}
        >
          {childrenStr.replace(/\n$/, "")}
        </SyntaxHighlighter>
      );
    },
    a: ({ node, ...props }) => {
      return (
        <a href={props.href} rel="nofollow">
          {props.children}
        </a>
      );
    },
  };

  return (
    <ReactMarkdown className="custom-html prose" remarkPlugins={[remarkGfm]} components={components}>
      {markdown}
    </ReactMarkdown>
  );
};

export default HighLightMarkdown;
