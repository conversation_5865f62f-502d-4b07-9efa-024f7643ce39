'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import CommentItem from './CommentItem';
import CommentForm from '~/components/common/CommentForm';
import { Comment } from '~/servers/comments/commentService';
import { useCommonContext } from '~/context/common-context';
import { LandingComponentStyles, CommentStyles } from '~/components/landing/types';

interface CommentListProps {
  pageUid: string;
  initialComments?: Comment[];
  showNewCommentForm?: boolean;
  className?: string;
  styles?: LandingComponentStyles;
  commentStyle?: CommentStyles;
}

export default function CommentList({
  pageUid,
  initialComments = [],
  showNewCommentForm = true,
  className = '',
  styles,
  commentStyle
}: CommentListProps) {
  // CommonContext 集成
  const { userData, setShowLoginModal } = useCommonContext();

  // 状态管理
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(initialComments.length === 0); // 如果有初始评论，先假设没有更多
  const [totalComments, setTotalComments] = useState(initialComments.length);
  const [loginSuccessMessage] = useState('');

  const limit = 5; // 每页评论数量

  // 获取评论列表
  const fetchComments = async (pageNum: number = 1, append: boolean = false) => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(
        `/api/comments/get?page_uid=${pageUid}&page=${pageNum}&limit=${limit}`
      );
      const result = await response.json();

      if (result.success) {
        const newComments = result.data.comments;

        if (append) {
          setComments(prev => [...prev, ...newComments]);
        } else {
          setComments(newComments);
        }

        setTotalComments(result.data.total);
        setHasMore(result.data.page < result.data.totalPages);
        setPage(pageNum);
      } else {
        setError(result.error || 'Failed to load comments');
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      setError('Network error, please try again later');
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取评论
  useEffect(() => {
    if (initialComments.length === 0) {
      fetchComments(1);
    }
  }, [pageUid]);

  // 处理新评论提交
  const handleNewComment = async (newComment: Comment) => {
    // 验证新评论数据
    if (!newComment || !newComment.uid) {
      console.error('Invalid comment data:', newComment);
      return;
    }

    // 添加新评论到列表顶部
    setComments(prev => [newComment, ...prev]);
    setTotalComments(prev => prev + 1);
  };

  // 处理回复提交
  const handleReplySubmit = async (_newReply: Comment) => {
    // 重新获取评论列表以显示新回复
    await fetchComments(1);
    setReplyingTo(null);
  };

  // 处理回复按钮点击
  const handleReply = (commentUid: string) => {
    if (!userData || !userData.user_id) {
      setShowLoginModal(true);
      return;
    }
    setReplyingTo(commentUid);
  };

  // 处理删除评论
  const handleDelete = async (commentUid: string) => {
    try {
      const response = await fetch('/api/comments/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ comment_uid: commentUid }),
      });

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // 安全地解析 JSON 响应
      let result: { success: boolean; error?: string };
      try {
        const text = await response.text();
        if (text.trim() === '') {
          // 空响应，但状态码成功，认为删除成功
          result = { success: true };
        } else {
          result = JSON.parse(text);
        }
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        // 如果解析失败但状态码是成功的，认为删除成功
        if (response.status === 200 || response.status === 204) {
          result = { success: true };
        } else {
          throw new Error('Invalid JSON response from server');
        }
      }

      if (result.success) {
        const deletedComment = comments.find(c => c.uid === commentUid);

        // 从列表中移除评论
        setComments(prev => prev.filter(comment => comment.uid !== commentUid));
        setTotalComments(prev => prev - 1);

        // 如果是回复，更新父评论的回复数量
        if (deletedComment?.parent_comment_uid) {
          setComments(prev => prev.map(comment =>
            comment.uid === deletedComment.parent_comment_uid
              ? { ...comment, reply_count: Math.max((comment.reply_count || 0) - 1, 0) }
              : comment
          ));
        }
      } else {
        alert(result.error || 'Delete failed');
      }
    } catch (error) {
      console.error('删除评论失败:', error);
      if (error instanceof Error) {
        alert(`Delete failed: ${error.message}`);
      } else {
        alert('Network error, delete failed');
      }
    }
  };

  // 加载更多评论
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchComments(page + 1, true);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 登录成功消息 */}
      {loginSuccessMessage && (
        <div className="rounded-md p-4 mb-4" style={{
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          border: '1px solid rgba(16, 185, 129, 0.3)',
          color: commentStyle?.textColor || '#10B981'
        }}>
          <p className="text-sm">{loginSuccessMessage}</p>
        </div>
      )}

      {/* 评论统计和登录提示 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <p className="text-lg font-semibold" style={{ color: commentStyle?.textColor || styles?.textColor || '#FFFFFF' }}>
            Comments ({totalComments})
          </p>
        </div>
      </div>

      {/* 发表新评论表单 */}
      {showNewCommentForm && (
        <CommentForm
          pageUid={pageUid}
          onSubmit={handleNewComment}
          placeholder="Share your thoughts..."
          styles={styles}
          commentStyle={commentStyle}
        />
      )}

      {/* 错误信息 */}
      {error && (
        <div className="rounded-md p-4" style={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          color: '#EF4444'
        }}>
          <p className="text-sm">{error}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fetchComments(1)}
            className="mt-2"
            style={{ color: '#EF4444' }}
          >
            Retry
          </Button>
        </div>
      )}

      {/* 评论列表 */}
      <div className="space-y-4">
        {
          comments
            .filter(comment => comment && comment.uid) // 过滤掉无效的评论
            .map((comment, index) => {
              // 确保每个评论都有唯一的 key，使用 uid 或 fallback 到 index
              const commentKey = comment.uid || `comment-${index}`;

              return (
              <div key={commentKey} className="space-y-3">
                <div className={comment.parent_comment_uid ? "comment-reply ml-12" : ""}>
                  <CommentItem
                    comment={comment}
                    onReply={handleReply}
                    onDelete={handleDelete}
                    showReplyButton={!comment.parent_comment_uid} // 只有顶级评论显示回复按钮
                    showDeleteButton={true}
                    styles={styles}
                    commentStyle={commentStyle}
                  />
                </div>

                {/* 回复表单（条件显示，只在顶级评论上显示） */}
                {replyingTo === comment.uid && !comment.parent_comment_uid && (
                  <div key={`reply-form-${commentKey}`} className="ml-12">
                    <CommentForm
                      pageUid={pageUid}
                      parentCommentUid={comment.uid}
                      onSubmit={handleReplySubmit}
                      onCancel={() => setReplyingTo(null)}
                      placeholder={`Reply to ${comment.user_name}...`}
                      showCancel={true}
                      styles={styles}
                      commentStyle={commentStyle}
                    />
                  </div>
                )}
              </div>
            );
          })
        }
      </div>

      {/* 加载更多按钮 */}
      {hasMore && comments.length > 0 && (
        <div className="text-center">
          <Button
            onClick={handleLoadMore}
            disabled={loading}
            className="px-8 py-2 font-medium transition-colors duration-300"
            style={{
              backgroundColor: commentStyle?.buttonBackgroundColor || styles?.buttonBackgroundColor || '#FF9500',
              color: commentStyle?.buttonTextColor || styles?.buttonTextColor || '#FFFFFF',
              borderRadius: commentStyle?.buttonBorderRadius || styles?.buttonBorderRadius || '12px',
              border: 'none',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Loading...' : 'Load More Comments'}
          </Button>
        </div>
      )}

      {/* 加载状态 */}
      {loading && comments.length === 0 && (
        <div className="text-center py-8">
          <p className="text-sm" style={{ color: commentStyle?.secondaryTextColor || 'rgba(255, 255, 255, 0.6)' }}>Loading comments...</p>
        </div>
      )}
    </div>
  );
}
