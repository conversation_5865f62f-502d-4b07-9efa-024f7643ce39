import { toast } from "react-toastify";

const CopyToClipboard = ({ text }) => {
  return (
    <button onClick={() => {
      navigator.clipboard.writeText(text)
      toast("已复制到剪切板！", {
        type: "success",
        position: "top-center"
      });
    }}
      className="ml-2 text-gray-500 hover:text-gray-700"
      title="复制到剪切板"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
      </svg>
    </button>
  )
}

export default CopyToClipboard;