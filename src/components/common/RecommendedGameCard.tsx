'use client';

import Link from 'next/link';
import { RecommendedGame, RecommendedGameStyles } from '../landing/types';

interface RecommendedGameCardProps {
  game: RecommendedGame;
  styles?: RecommendedGameStyles;
  className?: string;
  compact?: boolean; // 紧凑模式，用于侧边栏
}

export default function RecommendedGameCard({ 
  game, 
  styles, 
  className = '',
  compact = false 
}: RecommendedGameCardProps) {
  if (!game) return null;

  // 默认样式
  const defaultCardStyle: React.CSSProperties = {
    backgroundColor: styles?.cardBackgroundColor || 'rgba(0,0,0,0.15)',
    borderRadius: styles?.cardBorderRadius || '1rem',
    padding: styles?.cardPadding || (compact ? '1rem' : '1.5rem'),
    margin: styles?.cardMargin || '0 0 0.5rem 0',
    backdropFilter: styles?.cardBackdropFilter || 'blur(8px)',
    boxShadow: styles?.cardBoxShadow || 'none',
    border: styles?.cardBorderColor ? `1px solid ${styles.cardBorderColor}` : 'none',
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    cursor: 'pointer',
  };

  const titleStyle: React.CSSProperties = {
    color: styles?.titleColor || '#FFFFFF',
    fontSize: styles?.titleFontSize || (compact ? '0.9rem' : '1.1rem'),
    fontWeight: styles?.titleFontWeight || '600',
    margin: '0 0 0.5rem 0',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  };

  const descriptionStyle: React.CSSProperties = {
    color: styles?.descriptionColor || 'rgba(255, 255, 255, 0.8)',
    fontSize: styles?.descriptionFontSize || (compact ? '0.8rem' : '0.9rem'),
    lineHeight: '1.4',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: compact ? 2 : 3,
    WebkitBoxOrient: 'vertical',
    margin: '0 0 1rem 0',
  };

  const buttonStyle: React.CSSProperties = {
    backgroundColor: styles?.buttonBackgroundColor || '#FF9500',
    color: styles?.buttonTextColor || '#FFFFFF',
    borderRadius: styles?.buttonBorderRadius || '8px',
    padding: styles?.buttonPadding || (compact ? '6px 12px' : '8px 16px'),
    fontSize: styles?.buttonFontSize || (compact ? '0.8rem' : '0.9rem'),
    fontWeight: styles?.buttonFontWeight || '500',
    border: 'none',
    textDecoration: 'none',
    display: 'inline-block',
    textAlign: 'center' as const,
    transition: 'background-color 0.3s ease',
    width: '100%',
  };

  const imageStyle: React.CSSProperties = {
    backgroundColor: styles?.imageBackgroundColor || '#f3f4f6',
    borderRadius: styles?.imageBorderRadius || '8px',
    overflow: 'hidden',
  };

  return (
    <div
      className={`recommended-game-card rounded-xl overflow-hidden transition-transform duration-300 hover:scale-105 group ${className}`}
      style={{
        ...defaultCardStyle,
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
      }}
    >
      {/* 游戏封面图片 */}
      <div
        className={`relative overflow-hidden ${compact ? 'aspect-video' : 'aspect-square'}`}
        style={imageStyle}
      >
        <img
          src={(game.cover_img_url && game.cover_img_url !== '') ? game.cover_img_url : (game.image && game.image !== '') ? game.image : '/icon/favicon.ico'}
          alt={game.title}
          className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
          onError={(e) => {
            e.currentTarget.src = "/icon/favicon.ico";
            e.currentTarget.className = "w-full h-full object-contain bg-gray-800 p-4";
          }}
        />
        {/* Hover 覆盖层 - 参考 ExplorationGameList */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
          <Link
            href={game.link}
            className="w-full text-center py-2 mb-2 mx-2 rounded-lg font-medium transition-all"
            style={{
              ...buttonStyle,
              fontSize: compact ? '0.75rem' : '0.875rem',
            }}
          >
            {compact ? 'Play' : 'Play Now'}
          </Link>
        </div>
      </div>

      {/* 游戏信息 - 参考 ExplorationGameList 设计 */}
      <div className={`flex flex-col ${compact ? 'p-2' : 'p-3'}`}>
        <p
          className={`font-semibold truncate ${compact ? 'text-sm' : 'text-base'}`}
          style={titleStyle}
          title={game.title}
        >
          {game.title}
        </p>

        {!compact && game.description && (
          <p
            className="text-xs mt-1 line-clamp-2"
            style={{
              ...descriptionStyle,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              margin: '0.25rem 0 0 0',
            }}
          >
            {game.description}
          </p>
        )}

        {/* 游戏统计信息（可选） - 仅在非紧凑模式下显示 */}
        {!compact && (game.rating || game.playCount) && (
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: '0.5rem',
              fontSize: '0.75rem',
              color: 'rgba(255, 255, 255, 0.6)',
            }}
          >
            {game.rating && (
              <span>⭐ {game.rating.toFixed(1)}</span>
            )}
            {game.playCount && (
              <span>🎮 {game.playCount.toLocaleString()}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
