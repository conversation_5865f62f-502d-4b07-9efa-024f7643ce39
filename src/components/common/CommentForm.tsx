'use client';

import React, { useState, useRef } from 'react';
import { Button } from '~/components/ui/button';
import { Textarea } from '~/components/ui/textarea';
import { useCommonContext } from '~/context/common-context';
import { LandingComponentStyles, CommentStyles } from '~/components/landing/types';

interface CommentFormProps {
  pageUid: string;
  parentCommentUid?: string;
  onSubmit?: (comment: any) => void;
  onCancel?: () => void;
  placeholder?: string;
  showCancel?: boolean;
  className?: string;
  styles?: LandingComponentStyles;
  commentStyle?: CommentStyles;
}

export default function CommentForm({
  pageUid,
  parentCommentUid,
  onSubmit,
  onCancel,
  placeholder = 'Write your comment...',
  showCancel = false,
  className = '',
  styles,
  commentStyle
}: CommentFormProps) {
  const {
    setShowLoginModal,
    userData,
    setShowLogoutModal
  } = useCommonContext();
  
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const maxLength = 1000;
  const minLength = 2;

  // 处理内容变化
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= maxLength) {
      setContent(value);
      setError('');
      setSuccess('');
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 检查登录状态
    if (!userData || !userData.user_id) {
      setShowLoginModal(true);
      return;
    }

    // 验证内容
    const trimmedContent = content.trim();
    if (trimmedContent.length < minLength) {
      setError(`Comment must be at least ${minLength} characters`);
      return;
    }

    if (trimmedContent.length > maxLength) {
      setError(`Comment cannot exceed ${maxLength} characters`);
      return;
    }

    setIsSubmitting(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/comments/post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page_uid: pageUid,
          parent_comment_uid: parentCommentUid || null,
          content: trimmedContent,
        }),
      });

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // 安全地解析 JSON 响应
      let result: { success: boolean; error?: string; data?: any };
      try {
        const text = await response.text();
        if (text.trim() === '') {
          throw new Error('Empty response from server');
        } else {
          result = JSON.parse(text);
        }
      } catch (parseError) {
        console.error('Failed to parse JSON response:', parseError);
        throw new Error('Invalid JSON response from server');
      }

      if (result.success) {
        setContent('');
        setSuccess(parentCommentUid ? 'Reply posted successfully!' : 'Comment posted successfully!');

        // 如果有回调函数，则调用
        if (onSubmit) {
          onSubmit(result.data.comment);
        }

        // 3秒后清除成功消息
        setTimeout(() => {
          setSuccess('');
        }, 3000);
      } else {
        setError(result.error || 'Failed to post comment');
      }
    } catch (error) {
      console.error('Failed to post comment:', error);
      if (error instanceof Error) {
        setError(`Failed to post comment: ${error.message}`);
      } else {
        setError('Network error, please try again later');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理取消按钮点击
  const handleCancel = () => {
    setContent('');
    setError('');
    setSuccess('');
    if (onCancel) {
      onCancel();
    }
  };

  // 处理登出
  const handleLogout = () => {
    setShowLogoutModal(true);
  };

  return (
    <div
      className={`rounded-lg border p-4 ${className}`}
      style={{
        backgroundColor: commentStyle?.backgroundColor || styles?.backgroundColor || 'rgba(255,255,255,0.1)',
        borderColor: commentStyle?.borderColor || 'rgba(255,255,255,0.2)',
        color: commentStyle?.textColor || styles?.textColor || '#FFFFFF',
        borderRadius: commentStyle?.borderRadius || '12px',
        padding: commentStyle?.padding || '16px'
      }}
    >    
    
      {/* 未登录状态显示登录提示 */}
      {!userData || !userData.user_id ? (
        <div className="text-center py-4">
          <p className="mb-4 text-sm">Please log in to leave a comment</p>
          <Button
            onClick={() => setShowLoginModal(true)}
            className="px-6 py-2 font-medium"
            style={{
              backgroundColor: commentStyle?.buttonBackgroundColor || styles?.buttonBackgroundColor || '#FF9500',
              color: commentStyle?.buttonTextColor || styles?.buttonTextColor || '#FFFFFF',
              borderRadius: commentStyle?.buttonBorderRadius || '8px'
            }}
          >
            Log In
          </Button>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          {/* 用户信息行 */}
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              {/* 用户头像 */}
              {userData?.image ? (
                <img
                  src={userData.image}
                  alt={userData.name || 'User'}
                  className="w-8 h-8 rounded-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/icon/favicon.ico';
                  }}
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-600 text-xs font-medium">
                    {userData?.name?.charAt(0)?.toUpperCase() || '?'}
                  </span>
                </div>
              )}
              <span className="text-sm font-medium" style={{ color: commentStyle?.textColor || styles?.textColor || '#FFFFFF' }}>
                {userData?.name || 'Anonymous'}
              </span>
            </div>

            {/* 登出按钮 */}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-xs px-2 py-1"
              style={{ color: commentStyle?.secondaryTextColor || 'rgba(255, 255, 255, 0.7)' }}
            >
              Logout
            </Button>
          </div>

          {/* 评论输入框 */}
          <div className="space-y-2">
            <Textarea
              ref={textareaRef}
              value={content}
              onChange={handleContentChange}
              placeholder={placeholder}
              className="min-h-[120px] resize-none focus:ring-2 focus:ring-orange-500 focus:border-transparent rounded-lg transition-all duration-200"
              disabled={isSubmitting}
              style={{
                backgroundColor: commentStyle?.inputBackgroundColor || 'rgba(255, 255, 255, 0.95)',
                color: commentStyle?.inputTextColor || '#374151',
                borderColor: commentStyle?.inputBorderColor || 'rgba(255, 255, 255, 0.3)',
                border: '1px solid',
                backdropFilter: 'blur(4px)',
                WebkitBackdropFilter: 'blur(4px)',
                fontSize: commentStyle?.inputFontSize || '14px',
                lineHeight: '1.5',
                borderRadius: commentStyle?.inputBorderRadius || '8px',
                padding: commentStyle?.inputPadding || '12px'
              }}
            />

            {/* 字符计数 */}
            <div className="flex justify-between items-center text-xs" style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              <span>
                {content.length}/{maxLength} characters
              </span>
              {content.length > maxLength * 0.9 && (
                <span style={{ color: '#F59E0B' }}>
                  {maxLength - content.length} characters remaining
                </span>
              )}
            </div>
          </div>

          {/* 成功信息 */}
          {success && (
            <div className="text-sm rounded-lg p-3 backdrop-blur-sm mt-4"
                 style={{
                   color: '#10B981',
                   backgroundColor: 'rgba(16, 185, 129, 0.1)',
                   border: '1px solid rgba(16, 185, 129, 0.3)'
                 }}>
              {success}
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="text-sm rounded-lg p-3 backdrop-blur-sm mt-4"
                 style={{
                   color: '#EF4444',
                   backgroundColor: 'rgba(239, 68, 68, 0.1)',
                   border: '1px solid rgba(239, 68, 68, 0.3)'
                 }}>
              {error}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 mt-4">
            {showCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="font-medium transition-all duration-200"
                style={{
                  color: 'rgba(255, 255, 255, 0.8)',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  borderRadius: commentStyle?.buttonBorderRadius || '8px',
                  padding: commentStyle?.buttonPadding || '8px 24px'
                }}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting || content.trim().length < minLength}
              className="font-medium transition-all duration-200"
              style={{
                backgroundColor: isSubmitting || content.trim().length < minLength
                  ? `${commentStyle?.buttonBackgroundColor || styles?.buttonBackgroundColor || '#FF9500'}80`
                  : commentStyle?.buttonBackgroundColor || styles?.buttonBackgroundColor || '#FF9500',
                color: commentStyle?.buttonTextColor || styles?.buttonTextColor || '#FFFFFF',
                border: 'none',
                borderRadius: commentStyle?.buttonBorderRadius || '8px',
                padding: commentStyle?.buttonPadding || '8px 24px',
                opacity: isSubmitting || content.trim().length < minLength ? 0.6 : 1
              }}
            >
              {isSubmitting ? 'Posting...' : parentCommentUid ? 'Reply' : 'Post Comment'}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
