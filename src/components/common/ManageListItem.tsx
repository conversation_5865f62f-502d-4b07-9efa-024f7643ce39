const ManageListItem = ({
  isBold = false,
  textStr,
  redColor = false,
  greenColor = false,
  blueColor = false,
  titleStr = ''
}) => {
  return (
    <div className={"[border-left:1px_solid_rgb(213,_213,_234)] [border-right:1px_solid_rgb(213,_213,_234)]"}>
      <div
        className="h-full flex flex-col justify-center items-center pb-2">
        <p className={
          `break-all line-clamp-6 p-4 text-lg overflow-y-auto
        ${isBold ? 'font-bold' : ''}
        ${redColor ? 'text-red-500' : greenColor ? 'text-green-500' : blueColor ? 'text-blue-500' : ''}`
        } title={titleStr ? titleStr : textStr}>
          {textStr}
        </p>
      </div>
    </div>
  )
}

export default ManageListItem
