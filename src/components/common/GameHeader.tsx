'use client'
import Link from "next/link";
import React, { useState } from "react";
import { LandingComponentStyles } from "../landing/types";
import LoginModal from '../auth/LoginModal';
import LogoutModal from '../auth/LogoutModal';
import { getLinkHref } from "~/utils/buildLink";

interface GameHeaderProps {
  locale?: string;
  page?: string;
  siteName: string;
  navigationLinks: Array<{ label: string; url: string; target?: string; }>;
  styles?: LandingComponentStyles;
  className?: string;
}

const GameHeader: React.FC<GameHeaderProps> = ({
  locale = 'default',
  page = '',
  siteName,
  navigationLinks,
  styles,
  className
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 使用 getLinkHref 生成正确的重定向路径，避免 hydration 不匹配
  const redirectPath = getLinkHref(locale, page);

  return (
    <>
      {/* 登录模态框 */}
      <LoginModal redirectPath={redirectPath} />

      {/* 登出模态框 */}
      <LogoutModal redirectPath={redirectPath} />

      <header
        style={styles}
        className={`relative ${className || ''}`}
      >
      {/* Left section: Icon and Site Name */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 flex items-center justify-center rounded-full overflow-hidden" style={{ marginTop: '-4px' }}>
          <img
            src="/favicon.ico"
            alt="Site Icon"
            className="w-full h-full object-contain"
            suppressHydrationWarning
          />
        </div>
        <Link
          href="/"
          className="flex items-center"
        >
          <img
            src="/logo.svg"
            alt="Site Logo"
            className="h-8 object-contain"
            suppressHydrationWarning
          />
        </Link>
      </div>

      {/* Right section: Navigation Links (Desktop) */}
      <nav className="hidden sm:flex flex-1 justify-end items-center space-x-8">
        {navigationLinks.map((link, idx) => (
          <Link
            key={idx}
            href={link.url} // 直接使用 link.url
            target={link.target || "_self"}
            style={{
              color: styles?.navLinkColor,
              background: styles?.navLinkBackground,
              borderRadius: styles?.navLinkBorderRadius,
              fontWeight: styles?.navLinkFontWeight,
              transition: 'all 0.2s',
              padding: '0.5rem 1rem',
              textDecoration: 'none',
              display: 'inline-block',
            }}
            className={``}
            onMouseOver={e => {
              if (styles?.navLinkHoverColor) e.currentTarget.style.color = styles.navLinkHoverColor;
              if (styles?.navLinkHoverBackground) e.currentTarget.style.background = styles.navLinkHoverBackground;
            }}
            onMouseOut={e => {
              if (styles?.navLinkColor) e.currentTarget.style.color = styles.navLinkColor;
              if (styles?.navLinkBackground) e.currentTarget.style.background = styles.navLinkBackground;
            }}
          >
            {link.label}
          </Link>
        ))}
      </nav>

      {/* Mobile Menu Button (Hamburger icon) */}
      <div className="md:hidden lg:hidden xl:hidden ml-auto">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-white focus:outline-none"
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 flex flex-col items-center justify-center space-y-6 md:hidden"
          style={{
            background: 'rgba(0,0,0,0.18)',
            backdropFilter: 'blur(12px)',
            WebkitBackdropFilter: 'blur(12px)',
            paddingTop: '5rem',
            paddingBottom: '2rem',
          }}
        >
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="absolute top-4 right-4 text-gray-700 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition"
            style={{
              boxShadow: '0 4px 16px 0 rgba(31, 38, 135, 0.10)',
            }}
          >
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
          {navigationLinks.map((link, index) => (
            <Link
              key={index}
              href={link.url}
              target={link.target || "_self"}
              onClick={() => setIsMobileMenuOpen(false)}
              style={{
                background: 'rgba(0,0,0,0.85)',
                color: styles?.navLinkColor || '#4B5563',
                borderRadius: '1rem',
                fontWeight: 700,
                fontSize: '1rem',
                padding: '1rem 1.5rem',
                boxShadow: '0 4px 16px 0 rgba(31, 38, 135, 0.10)',
                margin: '0.5rem 0',
                textAlign: 'center',
                transition: 'all 0.2s',
              }}
              className="hover:bg-blue-100"
            >
              {link.label}
            </Link>
          ))}
        </div>
      )}
    </header>
    </>
  );
};

export default GameHeader; 