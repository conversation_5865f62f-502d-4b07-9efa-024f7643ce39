'use client'
import { useEffect, useRef, useState } from 'react'
import { Dialog } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { GlobeAltIcon } from '@heroicons/react/24/outline'
import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import Link from "next/link";
import { languages } from "~/i18n/config";
import { useCommonContext } from '~/context/common-context'
import LoadingModal from "~/components/common/LoadingModal";
import GeneratingModal from "~/components/common/GeneratingModal";
import LoginButton from '../auth/LoginButton';
import LoginModal from '../auth/LoginModal';
import LogoutModal from "../auth/LogoutModal";
import { getLinkHref } from "~/utils/buildLink";
import OneTapComponent from "~/components/auth/OneTapComponent";
import { ToastContainer, toast } from "react-toastify";
import { checkLogin, domainNameLowercase, isMultiLanguage } from "~/configs/globalConfig";


export default function Header({ locale, page, languageList = languages }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { setShowLoadingModal, userData, commonText, authText, menuText } = useCommonContext();

  const [pageResult, setPageResult] = useState(getLinkHref(locale, page));
  const [isScrolled, setIsScrolled] = useState(false);
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const checkLocalAndLoading = (lang) => {
    setMobileMenuOpen(false);
    if (locale != lang) {
      setShowLoadingModal(true);
    }
  };

  const checkPageAndLoading = (toPage) => {
    setMobileMenuOpen(false);
    if (page != toPage) {
      setShowLoadingModal(true);
    }
  };

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    const searchParams = window.location.search;
    setPageResult(pageResult + searchParams);
    return () => { };
  }, []);

  // 添加一个新的辅助函数用于处理菜单跳转
  const handleMenuClick = (e, item) => {
    // 如果是锚点链接（以 # 开头）
    if (item.link?.startsWith("/#")) {
      e.preventDefault();
      // 如果当前在首页
      if (page == "") {
        const element = document.getElementById(item.link.substring(2));
        if (element) {
          element.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      } else {
        // 如果在子页面，先跳转到首页对应位置
        window.location.href = getLinkHref(locale, "") + item.link;
      }
      setMobileMenuOpen(false);
    } else {
      // 如果是普通链接，执行页面跳转
      checkPageAndLoading(item.link);
    }
  };

  return (
    <header className="top-0 z-20 w-full">
      {/* {checkLogin ? <OneTapComponent /> : null} */}
      <LoadingModal loadingText={commonText.loadingText} />
      <GeneratingModal generatingText={commonText.generateText} />
      <LoginModal redirectPath={pageResult} />
      <LogoutModal redirectPath={pageResult} />
      <ToastContainer />
      {/* <PricingModal locale={locale} page={page} /> */}
      <header
        className={`h-16 md:h-14 w-full shadow-sm flex justify-between px-2 2xl:px-20 items-center z-30 top-0 fixed transition-all duration-300 box-shadow-game-1 text-slate-200`}
        style={{
          color: "#ffffff",
          backgroundColor: "#000000f5",
          boxShadow: "rgba(192, 162, 162, 0.16) 0px 5px 8px, rgba(0, 0, 0, 0.23) 0px 5px 8px",
        }}
      >
        {/* Site branding */}

        <div className="flex items-center">
          {/* Icon */}
          <a href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5" onClick={() => checkPageAndLoading("manage/dashboard")}>
            <img className="h-8 w-auto" src={"/icon/favicon.ico"} width={22} height={24} alt={domainNameLowercase} />
          </a>
          {/* Logo */}
          <a href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5" onClick={() => checkPageAndLoading("manage/dashboard")}>
            <img className="h-12 md:h-8 w-auto" src={"/icon/logo.svg"} width={22} height={24} alt={domainNameLowercase} />
          </a>
          {/* <h2 className="ml-2  text-md font-semibold">{domainNameLowercase}</h2> */}
        </div>
        {/* Navigation links - 动态渲染菜单 */}
        <div className="flex gap-x-6 items-center">
          <nav className="flex justify-center" aria-label="Global">
            <div className="hidden ltr:lg:ml-14 rtl:lg:mr-14 lg:flex lg:gap-x-6 items-center sm:gap-x-3 text-md font-semibold">

            </div>
          </nav>
          <div className="flex justify-end">
            <div className="flex lg:hidden">
              <button type="button" className="rounded-lg  dark:hover:bg-gray-800/30 py-1.5 px-3" onClick={() => setMobileMenuOpen(true)}>
                <span className="sr-only">Open main menu</span>
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            {isMultiLanguage ? (
              <Menu as="div" className="hidden lg:relative lg:inline-block lg:text-left z-30">
                <div>
                  <Menu.Button className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold hover:border-[rgba(255,255,255,0.9)]">
                    <GlobeAltIcon className="w-5 h-5 text-white" />
                    {locale == "default" ? "EN" : locale.toUpperCase()}
                    <ChevronDownIcon className="ltr:-mr-1 rtl:-ml-1 h-5 w-5 " aria-hidden="true" />
                  </Menu.Button>
                </div>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 z-30 mt-2 w-26 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div className="py-1 z-30">
                      {languageList.map((item) => {
                        let hrefValue = `/${item.lang}`;
                        if (page) {
                          hrefValue = `/${item.lang}/${page}`;
                        }
                        return (
                          <Menu.Item key={item.lang}>
                            <Link prefetch={false} href={hrefValue} onClick={() => checkLocalAndLoading(item.lang)} className={"z-30"}>
                              <span className={"block px-4 py-2 text-sm hover:text-[#2d6ae0] z-30"}>{item.language}</span>
                            </Link>
                          </Menu.Item>
                        );
                      })}
                    </div>
                  </Menu.Items>
                </Transition>
              </Menu>
            ) : null}
            {/* {checkLogin ? (
              <div className="hidden ltr:lg:ml-2 rtl:lg:mr-2 lg:relative lg:inline-block lg:text-left ">
                <LoginButton buttonType={userData?.email ? 1 : 0} />
              </div>
            ) : null} */}
          </div>
        </div>
      </header>
      <Dialog as="div" className="lg:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
        <div className="fixed inset-0 z-30" />
        <Dialog.Panel
          className="fixed inset-y-0 ltr:right-0 rtl:left-0 z-30 w-full overflow-y-auto px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10"
          style={{
            color: "#ffffff",
            backgroundColor: "#000000",
            boxShadow: "rgba(192, 162, 162, 0.16) 0px 5px 8px, rgba(0, 0, 0, 0.23) 0px 5px 8px",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex">
              <a href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5" onClick={() => checkPageAndLoading("manage/dashboard")}>
                <img className="h-8 w-auto" src={"/icon/logo.svg"} width={22} height={24} alt={domainNameLowercase} />
              </a>
            </div>
            <button type="button" className="-m-2.5 rounded-md p-2.5  z-20" onClick={() => setMobileMenuOpen(false)}>
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
              </div>

              {isMultiLanguage ? (
                <div className="ltr:ml-2 rtl:mr-2 py-4">
                  <Menu as="div" className="relative inline-block text-left z-20">
                    <div>
                      <Menu.Button className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold text hover:border-[rgba(255,255,255,0.9)]">
                        <GlobeAltIcon className="w-5 h-5 text-white" />
                        {locale == "default" ? "EN" : locale.toUpperCase()}
                        <ChevronDownIcon className="ltr:-mr-1 rtl:-mr-1 h-5 w-5 text-white" aria-hidden="true" />
                      </Menu.Button>
                    </div>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 z-10 mt-2 w-26 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <div className="py-1">
                          {languageList.map((item) => {
                            let hrefValue = `/${item.lang}`;
                            if (page) {
                              hrefValue = `/${item.lang}/${page}`;
                            }
                            return (
                              <Menu.Item key={item.lang}>
                                <Link prefetch={false} href={hrefValue} onClick={() => checkLocalAndLoading(item.lang)}>
                                  <span className={"text-gray-700 block px-4 py-2 text-sm hover:text-[#2d6ae0]"}>{item.language}</span>
                                </Link>
                              </Menu.Item>
                            );
                          })}
                        </div>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </div>
              ) : null}
              {/* {checkLogin ? (
                <div className="relative inline-block text-left text-base font-semibold text-white ltr:ml-2 rtl:mr-2">
                  <LoginButton buttonType={userData?.email ? 1 : 0} />
                </div>
              ) : null} */}
            </div>
          </div>
        </Dialog.Panel>
      </Dialog>
    </header>
  );
}