import Link from "next/link";
import { getLinkHref } from "~/utils/buildLink";
import { useCommonContext } from "~/context/common-context";
import React from "react";
import { getPricingWay } from "~/configs/pay/payUtil";
import { checkAvailableTime, copyright, domainNameLowercase, supportEmail } from "~/configs/globalConfig";
import AnalyticsComponent from "./AnalyticsComponent";

export default function Footer({
  locale,
  page
}) {
  const {
    userData,
    setShowLoadingModal,
    footerLink,
    commonText,
    menuText,
    showManageBilling,
    showPaddleCancelToastText,
    setToastText,
    setShowToastModal,
    pricingText
  } = useCommonContext();

  const manageBilling = async () => {
    if (!userData?.user_id) {
      return
    }
    const user_id = userData?.user_id;
    const requestData = {
      user_id: user_id
    }
    setShowLoadingModal(true);
    const responseData = await fetch(`/api/stripe/create-portal-link`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
    const result = await responseData.json();
    if (result.url) {
      window.location.href = result.url;
    }
  }

  const managePaddleBilling = async () => {
    if (showPaddleCancelToastText) {
      setToastText(pricingText.subscribeHasCanceled);
      setShowToastModal(true);
      return;
    }
    if (!userData?.user_id) {
      return
    }
    setShowLoadingModal(true);
    const responseData = await fetch(`/api/paddle/create-portal-link`);
    const result = await responseData.json();
    if (result.cancel_url) {
      window.location.href = result.cancel_url;
    }
  }

  const checkPageAndLoading = (toPage) => {
    if (page != toPage) {
      setShowLoadingModal(true);
    }
  }

  return (
    <footer
      aria-labelledby="footer-heading"
      className="w-full md:mt-8 mt-6 mb-6 shadow-lg"
      style={{
        background: 'rgba(0,0,0,0.18)',
        color: '#E5E7EB',
        borderRadius: '1.5rem',
        // boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.10)',
        backdropFilter: 'blur(12px)',
        WebkitBackdropFilter: 'blur(12px)',
        paddingTop: '24px',
        paddingBottom: '12px',
        margin: 0,
        // maxWidth: '1200px',
      }}
    >
      <div className="w-full" style={{ backgroundColor: 'transparent' }}>
        <div className="mx-auto w-full px-8">
          <div className="flex flex-col md:flex-row items-center md:items-center md:justify-between text-center md:text-left gap-2 mt-2 mb-2">
            <div className="flex flex-col md:flex-row items-center md:items-center text-center md:text-left gap-2 md:gap-3 mb-2 md:mb-0 md:justify-start md:w-auto w-full justify-center">
              <Link className="flex items-center justify-center md:justify-start" prefetch={false} href={getLinkHref(locale, "")}
                style={{ background: 'transparent' }}>
                <img className="h-8 rounded-lg shadow-md bg-transparent" src={"/icon/logo.svg"} alt={domainNameLowercase} />
              </Link>
              <span className="text-gray-400 ml-0 md:ml-2">© 2025 TinyFun.io. All rights reserved.</span>
            </div>
            <div className="hidden md:flex gap-x-6 ml-auto">
              <a
                href="/privacy-policy"
                className="font-medium text-gray-200 hover:text-[#FF9500] hover:underline transition-colors duration-200"
              >
                Privacy Policy
              </a>
              <a
                href="/terms-of-service"
                className="font-medium text-gray-200 hover:text-[#FF9500] hover:underline transition-colors duration-200"
              >
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
      <AnalyticsComponent />
    </footer>
  );
}
