'use client';

import React, { useState } from 'react';
import { Button } from '~/components/ui/button';
import { useCommonContext } from '~/context/common-context';
import { Comment } from '~/servers/comments/commentService';
import { LandingComponentStyles, CommentStyles } from '~/components/landing/types';
import { checkAdminUser } from "~/utils/checkWhiteUser";

interface CommentItemProps {
  comment: Comment;
  onReply?: (commentUid: string) => void;
  onDelete?: (commentUid: string) => Promise<void>;
  showReplyButton?: boolean;
  showDeleteButton?: boolean;
  className?: string;
  styles?: LandingComponentStyles;
  commentStyle?: CommentStyles;
}

export default function CommentItem({
  comment,
  onReply,
  onDelete,
  showReplyButton = true,
  showDeleteButton = true,
  className = '',
  styles,
  commentStyle
}: CommentItemProps) {
  const { userData } = useCommonContext();
  const [isDeleting, setIsDeleting] = useState(false);

  // 格式化时间显示
  const formatTime = (dateString: string | Date) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else if (diffInSeconds < 2592000) {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  // 检查是否是评论作者
  const isCommentAuthor = () => {
    return userData?.user_id === comment.user_id;
  };

  // 检查是否可以回复
  const canReply = () => {
    return Boolean(userData?.user_id); // 已登录用户可以回复
  };

  // 检查是否可以删除
  const canDelete = () => {
    // 评论作者或管理员可以删除
    return userData?.user_id === comment.user_id || checkAdminUser(userData);
  };

  // 处理删除评论
  const handleDelete = async () => {
    if (!canDelete() || isDeleting) return;

    const confirmMessage = comment.parent_comment_uid
      ? 'Are you sure you want to delete this reply? This action cannot be undone.'
      : 'Are you sure you want to delete this comment? This action cannot be undone.';

    if (window.confirm(confirmMessage)) {
      setIsDeleting(true);
      try {
        if (onDelete) {
          await onDelete(comment.uid);
        }
      } catch (error) {
        console.error('Failed to delete comment:', error);
        alert('Failed to delete comment. Please try again later.');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  // 处理回复
  const handleReply = () => {
    onReply?.(comment.uid);
  };

  return (
    <div
      className={`transition-all duration-200 hover:backdrop-blur-md ${className}`}
      style={{
        // 使用 commentStyle 对象的样式属性
        backgroundColor: commentStyle?.backgroundColor || 'rgba(0,0,0,0.15)',
        borderColor: commentStyle?.borderColor || 'rgba(255, 255, 255, 0.15)',
        borderRadius: commentStyle?.borderRadius || '1rem',
        padding: commentStyle?.padding || '1.5rem',
        margin: commentStyle?.margin || '0 0 1.5rem 0',
        border: commentStyle?.borderColor ? '1px solid' : 'none',
        backdropFilter: commentStyle?.backdropFilter || 'blur(8px)',
        WebkitBackdropFilter: commentStyle?.backdropFilter || 'blur(8px)',
        boxShadow: commentStyle?.boxShadow || 'none'
      }}
    >
      {/* 用户信息行 */}
      <div className="flex items-start space-x-3">
        {/* 用户头像 */}
        <div className="flex-shrink-0">
          {comment.user_image ? (
            <img
              src={comment.user_image}
              alt={comment.user_name || 'User'}
              className="w-10 h-10 rounded-full object-cover"
              onError={(e) => {
                // 头像加载失败时显示默认头像
                const target = e.target as HTMLImageElement;
                target.src = '/icon/favicon.ico';
              }}
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
              <span className="text-gray-600 text-sm font-medium">
                {comment.user_name?.charAt(0)?.toUpperCase() || '?'}
              </span>
            </div>
          )}
        </div>

        {/* 评论内容区域 */}
        <div className="flex-1 min-w-0">
          {/* 用户名和时间 */}
          <div className="flex items-center space-x-3 mb-3">
            <p className="text-sm font-semibold truncate" style={{ color: commentStyle?.textColor || styles?.textColor || '#FFFFFF' }}>
              {comment.user_name || 'Anonymous'}
            </p>
            {/* 作者标识 */}
            {isCommentAuthor() && (
              <span className="text-xs px-2.5 py-1 rounded-full font-medium" 
                    style={{ 
                      backgroundColor: commentStyle?.badgeBackgroundColor || 'rgba(59, 130, 246, 0.2)', 
                      color: commentStyle?.badgeTextColor || '#60A5FA',
                      border: `1px solid ${commentStyle?.badgeBorderColor || 'rgba(59, 130, 246, 0.3)'}`
                    }}>
                You
              </span>
            )}
            {/* 管理员标识 */}
            {checkAdminUser(userData) && comment.user_id === userData.user_id && (
              <span className="text-xs px-2.5 py-1 rounded-full font-medium"
                    style={{ 
                      backgroundColor: commentStyle?.adminBadgeBackgroundColor || 'rgba(168, 85, 247, 0.2)', 
                      color: commentStyle?.adminBadgeTextColor || '#C084FC',
                      border: `1px solid ${commentStyle?.adminBadgeBorderColor || 'rgba(168, 85, 247, 0.3)'}`
                    }}>
                Admin
              </span>
            )}
            <span className="text-xs" style={{ color: commentStyle?.timeColor || commentStyle?.secondaryTextColor || 'rgba(255, 255, 255, 0.6)' }}>
              {formatTime(comment.created_at!)}
            </span>
          </div>

          {/* 评论内容 */}
          <div className="text-sm leading-relaxed mb-4">
            <p className="whitespace-pre-wrap break-words" style={{ color: commentStyle?.textColor || styles?.textColor || 'rgba(255, 255, 255, 0.9)' }}>
              {comment.content}
            </p>
          </div>

          {/* 操作按钮区域 */}
          <div className="flex items-center space-x-4">
            {/* 回复数量显示 */}
            {comment.reply_count > 0 && (
              <span className="text-xs font-medium" style={{ color: commentStyle?.secondaryTextColor || 'rgba(255, 255, 255, 0.7)' }}>
                {comment.reply_count} {comment.reply_count === 1 ? 'reply' : 'replies'}
              </span>
            )}

            {/* 回复按钮 */}
            {showReplyButton && canReply() && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReply}
                className="text-xs transition-all duration-200 hover:backdrop-blur-sm"
                style={{
                  color: commentStyle?.buttonTextColor || 'rgba(255, 255, 255, 0.8)',
                  backgroundColor: commentStyle?.buttonBackgroundColor || 'rgba(255, 255, 255, 0.1)',
                  border: commentStyle?.buttonBorder || 'none',
                  borderRadius: commentStyle?.buttonBorderRadius || '8px',
                  padding: commentStyle?.buttonPadding || '6px 12px'
                }}
                title="Reply to this comment"
              >
                Reply
              </Button>
            )}

            {/* 删除按钮 */}
            {showDeleteButton && canDelete() && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-xs transition-all duration-200 hover:backdrop-blur-sm"
                style={{
                  color: isDeleting ? 'rgba(255, 255, 255, 0.5)' : 'rgba(239, 68, 68, 0.9)',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)',
                  border: 'none',
                  borderRadius: commentStyle?.buttonBorderRadius || '8px',
                  padding: commentStyle?.buttonPadding || '6px 12px',
                  opacity: isDeleting ? 0.6 : 1
                }}
                title={isCommentAuthor() ? "Delete your comment" : "Delete this comment (Admin)"}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
