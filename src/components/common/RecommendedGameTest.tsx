'use client';

import { RecommendedGame, RecommendedGameStyles } from '../landing/types';
import RecommendedGameCard from './RecommendedGameCard';
import RecommendedGameSidebar from './RecommendedGameSidebar';

// 测试数据
const testGames: RecommendedGame[] = [
  {
    title: 'Adventure Quest',
    image: '/icon/favicon.ico',
    link: '/game/adventure-quest',
    description: 'Embark on an epic adventure through mystical lands filled with treasures and challenges.',
    category: 'Adventure',
    rating: 4.5,
    playCount: 12500
  },
  {
    title: 'Puzzle Master',
    image: null,
    link: '/game/puzzle-master',
    description: 'Test your mind with challenging puzzles that will keep you engaged for hours.',
    category: 'Puzzle',
    rating: 4.2,
    playCount: 8900
  },
  {
    title: 'Racing Thunder',
    image: '/icon/favicon.ico',
    link: '/game/racing-thunder',
    description: 'High-speed racing action with stunning graphics and realistic physics.',
    category: 'Racing',
    rating: 4.7,
    playCount: 15600
  },
  {
    title: 'Strategy Empire',
    image: null,
    link: '/game/strategy-empire',
    description: 'Build your empire and conquer the world in this strategic masterpiece.',
    category: 'Strategy',
    rating: 4.3,
    playCount: 9800
  },
  {
    title: 'Space Explorer',
    image: '/icon/favicon.ico',
    link: '/game/space-explorer',
    description: 'Explore the vast universe and discover new planets in this space adventure.',
    category: 'Adventure',
    rating: 4.6,
    playCount: 11200
  },
  {
    title: 'Card Battle',
    image: null,
    link: '/game/card-battle',
    description: 'Strategic card battles with unique characters and powerful spells.',
    category: 'Card',
    rating: 4.1,
    playCount: 7300
  }
];

// 测试样式
const testStyles: RecommendedGameStyles = {
  // 卡片样式
  cardBackgroundColor: 'rgba(0,0,0,0.15)',
  cardBorderRadius: '1rem',
  cardPadding: '1.5rem',
  cardBackdropFilter: 'blur(8px)',
  
  // 文本样式
  titleColor: '#FFFFFF',
  titleFontSize: '1.1rem',
  titleFontWeight: '600',
  descriptionColor: 'rgba(255, 255, 255, 0.8)',
  descriptionFontSize: '0.9rem',
  
  // 按钮样式
  buttonBackgroundColor: '#FF9500',
  buttonTextColor: '#FFFFFF',
  buttonHoverBackgroundColor: '#E6850E',
  buttonBorderRadius: '8px',
  
  // 侧边栏样式
  sidebarBackgroundColor: 'rgba(0,0,0,0.18)',
  sidebarBorderRadius: '1rem',
  sidebarPadding: '1.5rem',
  sidebarBackdropFilter: 'blur(12px)',
  sidebarTitleColor: '#FFFFFF',
  sidebarTitleFontSize: '1.5rem',
  sidebarTitleFontWeight: '700',
};

export default function RecommendedGameTest() {
  const containerStyle: React.CSSProperties = {
    background: 'rgba(0,0,0,0.18)',
    backgroundImage: 'url("/images/background.jpg")',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundAttachment: 'fixed',
    backdropFilter: 'blur(10px)',
    minHeight: '100vh',
    color: '#fff',
    fontFamily: 'Inter, Roboto, Arial, sans-serif',
    padding: '2rem',
  };

  return (
    <div style={containerStyle}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <h1 style={{ 
          color: '#FFFFFF', 
          fontSize: '2rem', 
          fontWeight: '700', 
          textAlign: 'center',
          marginBottom: '2rem' 
        }}>
          Recommended Game Components Test
        </h1>

        {/* 单个卡片测试 */}
        <section style={{ marginBottom: '3rem' }}>
          <h2 style={{ 
            color: '#FF9500', 
            fontSize: '1.5rem', 
            marginBottom: '1rem' 
          }}>
            Single Game Card (Normal Mode)
          </h2>
          <div style={{ maxWidth: '400px' }}>
            <RecommendedGameCard
              game={testGames[0]}
              styles={testStyles}
            />
          </div>
        </section>

        {/* 紧凑模式卡片测试 */}
        <section style={{ marginBottom: '3rem' }}>
          <h2 style={{ 
            color: '#FF9500', 
            fontSize: '1.5rem', 
            marginBottom: '1rem' 
          }}>
            Single Game Card (Compact Mode)
          </h2>
          <div style={{ maxWidth: '300px' }}>
            <RecommendedGameCard
              game={testGames[1]}
              styles={testStyles}
              compact={true}
            />
          </div>
        </section>

        {/* 侧边栏测试 */}
        <section>
          <h2 style={{ 
            color: '#FF9500', 
            fontSize: '1.5rem', 
            marginBottom: '1rem' 
          }}>
            Game Sidebar
          </h2>
          <div style={{ maxWidth: '350px' }}>
            <RecommendedGameSidebar
              games={testGames}
              styles={testStyles}
              title="Popular Games"
              maxInitialGames={3}
              showLoadMore={true}
            />
          </div>
        </section>
      </div>
    </div>
  );
}
