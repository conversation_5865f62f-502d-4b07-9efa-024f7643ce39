import Script from "next/script";
import { googleAdsId, googleAdsOpen } from "~/configs/globalConfig";



const GoogleAdsense = () => {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  if (!googleAdsOpen) {
    return null;
  }

  return (
    <Script
      async
      src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${googleAdsId}`}
      crossOrigin="anonymous"
      strategy="afterInteractive"
    />
  );
};

export default GoogleAdsense;