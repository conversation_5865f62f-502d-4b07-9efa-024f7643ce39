"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";

type Site = {
  id: number;
  name: string;
  domain: string;
  [key: string]: any;
};

export function useCurrentSite() {
  const [site, setSite] = useState<Site | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pathname = usePathname();

  const setCurrentSite = async (siteId: number) => {
    try {
      setLoading(true);

      // Store selection in localStorage for persistence
      if (typeof window !== "undefined") {
        localStorage.setItem("currentSiteId", siteId.toString());
      }

      const response = await fetch(`/api/manage/sites/${siteId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch site: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        setSite(data.data);
      } else {
        throw new Error(data.error || "Failed to load site");
      }
    } catch (error) {
      console.error("Error setting current site:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    site,
    loading,
    error,
    setCurrentSite,
  };
}
