import { getDb } from "~/libs/db/db";
import { SiteConfig, RecommendationZoneConfig } from "~/types/site";

export type { RecommendationZoneConfig };

const db = getDb();

// 定义推荐游戏的数据结构
export interface RecommendedGame {
  uid: string;
  site_uid: string;
  page_uid: string | null;
  recommendation_zone: string;
  title: string;
  description: string | null;
  cover_img_url: string | null;
  jump_url: string;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

// 获取推荐游戏列表 (组合页面级和站点级推荐)
export async function getRecommendedGamesForPage(siteUid: string, pageUid: string): Promise<RecommendedGame[]> {
  try {
    // 简化查询：仅根据 site_uid 获取所有推荐，暂时不考虑 page_uid 过滤，
    // 除非 page_uid 功能明确需要在这里处理
    const query = `
      SELECT * FROM recommended_games
      WHERE site_uid = $1
      ORDER BY sort_order ASC, created_at DESC; -- 按排序权重和创建时间排序
    `;
    const { rows } = await db.query(query, [siteUid]); // 只传入 siteUid

    return rows as RecommendedGame[]; // 直接返回查询结果

  } catch (error) {
    console.error('[getRecommendedGamesForPage] Error fetching recommended games:', error);
    return []; // 返回空数组，不影响页面渲染
  }
}

// 获取站点的推荐区域配置
export async function getSiteRecommendationZones(siteUid: string): Promise<RecommendationZoneConfig[]> {
  try {
    const query = `
      SELECT config FROM sites
      WHERE uid = $1
      LIMIT 1;
    `;
    const { rows } = await db.query(query, [siteUid]);

    if (rows.length === 0 || !rows[0].config) {
      return [];
    }

    const siteConfig: SiteConfig = rows[0].config;

    return siteConfig.recommendationZones || [];
  } catch (error) {
    console.error('[getSiteRecommendationZones] Error fetching recommendation zones:', error);
    return [];
  }
}

// 检查站点配置
export async function checkSiteConfig(siteUid: string): Promise<void> {
    try {
        const query = `
            SELECT uid, domain, config FROM sites
            WHERE uid = $1
            LIMIT 1;
        `;
        const { rows } = await db.query(query, [siteUid]);

        if (rows.length === 0) {
            return;
        }

        const site = rows[0];

        if (!site.config) {
            return;
        }

        const config = site.config;
    } catch (error) {
        console.error('[checkSiteConfig] Error checking site configuration:', error);
    }
}