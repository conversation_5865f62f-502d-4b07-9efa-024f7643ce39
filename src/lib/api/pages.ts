import { getDb } from "~/libs/db/db";

// 引入推荐游戏和区域配置的类型定义
import { RecommendedGame, RecommendationZoneConfig } from "./recommendations";

const db = getDb();

export interface PageContent {
  title: string;
  description?: string;
  h1?: string;
  name?: string;
  iframe_url?: string;
  components?: any[];
  introduction?: any;
  features?: any;
  how?: any;
  tips?: any;
  faq?: any;
  cta?: any;
}

export interface Page {
  uid: string;
  game_uid: string;
  site_uid: string;
  template_key: string;
  title: string;
  slug: string;
  json_content: any;
  status: number;
  iframe_url?: string;
  cover_img_url?: string;
  public_url: string;
  locale?: string;
  domain: string;
  is_homepage?: boolean;
  is_exploration_page?: boolean;
  exploration_sort_by?: 'newest' | 'popular';
}

// 使用 RecommendedGame 类型替代 ExplorationGame
export type ExplorationGame = RecommendedGame;

export interface ExplorationGamesResult {
  games: ExplorationGame[];
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

// Helper function to safely get nested value from an object
function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.');
  let current = obj;
  for (const key of keys) {
    if (current === null || typeof current !== 'object' || !(key in current)) {
      return undefined; // Path does not exist
    }
    current = current[key];
  }
  return current;
}

// 根据 slug 和 domain 获取页面
export async function getPageBySlug(slug: string, domain: string, isHomepage: boolean = false): Promise<Page | null> {
  const maxRetries = 3;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      let query = `SELECT * FROM pages WHERE domain = $1 AND status = 1`;
      let params: any[] = [domain];
      if (isHomepage) {
        query += ` AND is_homepage = true`;
      } else {
        query += ` AND slug = $2`;
        params.push(slug);
      }
      query += ` LIMIT 1`;

      const { rows } = await db.query(query, params);

      if (rows.length === 0) {
        return null;
      }

      const page = rows[0] as Page;
      return page;

    } catch (error: any) {
      lastError = error;

      // 如果是连接错误，等待后重试
      if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }

      // 对于其他错误，直接抛出
      if (attempt === maxRetries) {
        throw error;
      }
    }
  }

  throw lastError;
}

// 辅助函数：执行查询并返回分页结果
async function executeExplorationQuery(
  query: string, 
  params: any[], 
  siteUid: string, 
  page: number, 
  pageSize: number
): Promise<ExplorationGamesResult> {
  const { rows: gameRows } = await db.query(query, params);
  
  const countQuery = `SELECT COUNT(*) as total FROM recommended_games WHERE site_uid = $1`;
  const { rows: countRows } = await db.query(countQuery, [siteUid]);
  
  const total = parseInt(countRows[0].total);
  const totalPages = Math.ceil(total / pageSize);

  return {
    games: gameRows,
    total,
    totalPages,
    currentPage: page,
    pageSize
  };
}

// 获取探索页面的游戏列表
export async function getExplorationGames(
  siteUid: string,
  sortBy: 'newest' | 'popular',
  page: number = 1,
  pageSize: number = 12
): Promise<ExplorationGamesResult> {
  try {
    const offset = (page - 1) * pageSize;

    if (sortBy === 'popular') {
      const query = `
        SELECT rg.*,
               COALESCE(p.play_count, 0) as play_count,
               COALESCE(p.comment_count, 0) as comment_count
        FROM recommended_games rg
        LEFT JOIN pages p ON (
          p.site_uid = rg.site_uid 
          AND p.slug = REGEXP_REPLACE(rg.jump_url, '^.*/game/([^/?#]+).*$', '\\1')
          AND p.status = 1
        )
        WHERE rg.site_uid = $1
        ORDER BY 
          COALESCE(p.play_count, 0) DESC,
          COALESCE(p.comment_count, 0) DESC,
          rg.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      return await executeExplorationQuery(query, [siteUid, pageSize, offset], siteUid, page, pageSize);
    }

    // newest 排序
    const query = `
      SELECT * FROM recommended_games
      WHERE site_uid = $1
      ORDER BY created_at DESC
      LIMIT $2 OFFSET $3
    `;

    return await executeExplorationQuery(query, [siteUid, pageSize, offset], siteUid, page, pageSize);

  } catch (error) {
    console.error('[getExplorationGames] Error fetching exploration games:', error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page,
      pageSize
    };
  }
}
