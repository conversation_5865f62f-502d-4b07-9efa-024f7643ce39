import { storageUrl } from "~/configs/globalConfig";

export const getLinkHref = (locale = "en", page = "") => {
  if (page == "") {
    if (locale == "en") {
      return "/";
    }
    return `/${locale}/`;
  }
  if (locale == "en") {
    return `/${page}`;
  }
  return `/${locale}/${page}`;
};

export const getLinkHrefWithoutStartSlash = (locale = "en", page = "") => {
  if (page == "") {
    if (locale == "en") {
      return "";
    }
    return `${locale}/`;
  }
  if (locale == "en") {
    return `${page}`;
  }
  return `${locale}/${page}`;
};

export const getCompressionImageLink = (url) => {
  const beginUrl = storageUrl + "/cdn-cgi/image/w=256,q=75";
  const newUrl = url.replace(storageUrl, "");
  return beginUrl + newUrl;
};

export const getCompressionMiniImageLink = (url) => {
  const beginUrl = storageUrl + "/cdn-cgi/image/w=50,q=65";
  const newUrl = url.replace(storageUrl, "");
  return beginUrl + newUrl;
};

export const getCompressionImageLink512 = (url) => {
  const beginUrl = storageUrl + "/cdn-cgi/image/w=512,q=75";
  const newUrl = url.replace(storageUrl, "");
  return beginUrl + newUrl;
};
export const handleBlogUrl = (children) => {
  // 如果是数组，将所有子元素转换为字符串
  if (Array.isArray(children)) {
    return handleBlogUrl(children.map((child) => (typeof child === "object" ? child.props?.children : child)).join(""));
  }

  // 如果是对象（比如加粗文本），获取其 children
  if (typeof children === "object" && children !== null) {
    return handleBlogUrl(children.props?.children || "");
  }

  // 如果是字符串，进行原有的处理
  return children
    ?.toString()
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");
};