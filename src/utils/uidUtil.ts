import { v4 as uuidv4 } from "uuid";
import short from "short-uuid";
import ShortUniqueId from "short-unique-id";

export const generateNewUID = () => {
  return generateNewLongUID();
};

export const generateNewShortUID = () => {
  return short.generate();
};

export const generateNewLongUID = () => {
  return uuidv4().replace(/-/g, "");
};

export const generateNewShortUniqueUID = () => {
  const { randomUUID } = new ShortUniqueId({ length: 12, dictionary: "alphanum_lower" });
  return randomUUID();
};

export const generateInviteCode = () => {
  const { randomUUID } = new ShortUniqueId({ length: 6, dictionary: "alphanum_lower" });
  return randomUUID();
};

export const generatePageBaseId = () => {
  const { randomUUID } = new ShortUniqueId({ length: 8, dictionary: "alphanum_lower" });
  return randomUUID();
};

export const generateSectionBaseId = () => {
  const { randomUUID } = new ShortUniqueId({ length: 8, dictionary: "alphanum_lower" });
  return randomUUID();
};

export const generateContentBaseId = () => {
  const { randomUUID } = new ShortUniqueId({ length: 8, dictionary: "alphanum_lower" });
  return randomUUID();
};
export const generateFourDigitUID = () => {
  const { randomUUID } = new ShortUniqueId({ length: 6, dictionary: "alphanum_lower" });
  return randomUUID();
};