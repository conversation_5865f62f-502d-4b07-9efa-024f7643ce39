interface ToggleFullscreenProps {
  element: HTMLElement;
  isFullScreen: boolean;
  onEnter?: () => void;
  onExit?: () => void;
  isMobile?: boolean;
}

export async function toggleFullscreen({ element, isFullScreen, onEnter, onExit, isMobile }: ToggleFullscreenProps) {
  try {
    if (!isFullScreen) {
      // 进入全屏
      if (isMobile && element.tagName.toLowerCase() === "iframe") {
        // 将页面的 header隐藏
        const header = document.querySelector("header");
        if (header) {
          header.style.display = "none";
        }
        // 移动端 iframe 特殊处理
        element.style.position = "fixed";
        element.style.top = "0";
        element.style.left = "0";
        element.style.width = "100vh";
        element.style.height = "100vw";
        element.style.transform = "rotate(90deg) translate(0, -100%)";
        element.style.transformOrigin = "top left";
        element.style.zIndex = "9998";

        // 创建退出全屏按钮
        const exitButton = document.createElement("button");
        exitButton.id = "exit-fullscreen-btn";
        exitButton.innerHTML = `Exit Fullscreen`;
        exitButton.style.cssText = `
          position: fixed;
          z-index: 9999;
          bottom: 150px;
          left: 5px;
          padding: 8px;
          background: rgba(0, 0, 0, 0.5);
          color: white;
          border: none;
          border-radius: 4px;
          transform: rotate(90deg);
          transform-origin: bottom left;
        `;
        document.body.appendChild(exitButton);

        exitButton.onclick = () => {
          element.style.position = "";
          element.style.top = "";
          element.style.left = "";
          element.style.width = "";
          element.style.height = "";
          element.style.transform = "";
          element.style.transformOrigin = "";
          element.style.zIndex = "";
          exitButton.remove();
          // 恢复显示 header
          const header = document.querySelector("header");
          if (header) {
            header.style.display = "block";
          }
          onExit?.();
        };

        onEnter?.();
      } else {
        // 原有的全屏逻辑
        if (element.requestFullscreen) {
          await element.requestFullscreen();
        } else if ((element as any).webkitRequestFullscreen) {
          await (element as any).webkitRequestFullscreen();
        } else if ((element as any).mozRequestFullScreen) {
          await (element as any).mozRequestFullScreen();
        } else if ((element as any).msRequestFullscreen) {
          await (element as any).msRequestFullscreen();
        }
      }
    } else {
      // 退出全
      if (document.exitFullscreen) {
        await document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen();
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen();
      }
    }
  } catch (error) {
    console.error("全屏切换失败:", error);
  }
}
