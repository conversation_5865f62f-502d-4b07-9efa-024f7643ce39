import { Site, SiteConfig, NavigationLink, RecommendationZoneConfig } from '~/types/site';
import { Page } from '~/lib/api/pages';

export interface Game {
  id: number;
  keyword: string;
  uid?: string;
  slug?: string;
  iframe_url?: string;
  reference_data?: string;
  tags?: string[] | string;
  created_at: string;
  updated_at: string;
  is_delete: boolean;
}

export interface Template {
  id: number;
  uid?: string;
  name: string;
  content?: string;
  preview_url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Prompt {
  id: number;
  key: string;
  name?: string;
  description?: string;
  llmModelCompatibility?: string[];
  version?: string;
  contentTemplate?: string;
  created_at?: string;
  updated_at?: string;
  is_delete: boolean;
}

export interface RecommendedItem {
  id: number;
  site_id: number;
  owning_page_id?: number;
  recommendation_zone: string;
  title: string;
  description?: string;
  cover_img_url?: string;
  target_type: RecommendedTargetType;
  target_page_id?: number;
  external_jump_url?: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
  is_delete: boolean;
  
  // Optional joined data
  site?: Site;
  owningPage?: Page;
  targetPage?: Page;
}

export enum RecommendedTargetType {
  INTERNAL_PAGE = 'INTERNAL_PAGE',
  EXTERNAL_URL = 'EXTERNAL_URL',
} 