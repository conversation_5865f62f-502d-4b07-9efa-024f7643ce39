export const classNames = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

export const getFileExtension = (filename) => {
  return filename.split(".").pop();
};

export const getGenerator = (generator) => {
  return `${generator?.split("/")[1]}`;
};

// 判断是否是移动端
export const isMobile = (userAgent) => {
  if (/Mobile|Android|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(userAgent)) {
    return true;
  } else {
    return false;
  }
};
// 返回grid列数 类名
export const getGridCol = (cols) => {
  return `md:grid-cols-${cols}`
}