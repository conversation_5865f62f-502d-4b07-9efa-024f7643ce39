import { NextResponse } from 'next/server';

/**
 * Standard success response format for APIs
 */
export function apiSuccess<T>(data: T, status: number = 200) {
  return NextResponse.json(
    { success: true, data },
    { status }
  );
}

/**
 * Standard error response format for APIs
 */
export function apiError(
  message: string,
  status: number = 500,
  errorCode?: string,
  details?: any
) {
  const responseBody: {
    success: false;
    error: string;
    errorCode?: string;
    details?: any;
  } = {
    success: false,
    error: message,
  };

  if (errorCode) {
    responseBody.errorCode = errorCode;
  }

  if (details) {
    responseBody.details = details;
  }

  return NextResponse.json(responseBody, { status });
}

/**
 * HTTP status codes used in API responses
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
};

/**
 * Error codes used in API responses
 */
export const ERROR_CODES = {
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Validation errors
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  
  // Database errors
  RECORD_NOT_FOUND: 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD: 'DUPLICATE_RECORD',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // LLM errors
  LLM_API_ERROR: 'LLM_API_ERROR',
  LLM_CONTENT_INVALID: 'LLM_CONTENT_INVALID',
  
  // GitHub errors
  GITHUB_API_ERROR: 'GITHUB_API_ERROR',
  GITHUB_PUSH_FAILED: 'GITHUB_PUSH_FAILED',
  
  // Vercel errors
  VERCEL_API_ERROR: 'VERCEL_API_ERROR',
  VERCEL_DEPLOY_FAILED: 'VERCEL_DEPLOY_FAILED',
  
  // General errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
};

/**
 * Common error handlers
 */
export const commonErrors = {
  notFound: (entity: string) => 
    apiError(`${entity} not found`, HTTP_STATUS.NOT_FOUND, ERROR_CODES.RECORD_NOT_FOUND),
  
  badRequest: (message: string = 'Invalid request data') => 
    apiError(message, HTTP_STATUS.BAD_REQUEST, ERROR_CODES.INVALID_INPUT),
  
  unauthorized: () => 
    apiError('Unauthorized', HTTP_STATUS.UNAUTHORIZED, ERROR_CODES.UNAUTHORIZED),
  
  forbidden: () => 
    apiError('Forbidden', HTTP_STATUS.FORBIDDEN, ERROR_CODES.FORBIDDEN),
  
  duplicate: (entity: string) => 
    apiError(`${entity} already exists`, HTTP_STATUS.CONFLICT, ERROR_CODES.DUPLICATE_RECORD),
  
  serverError: (message: string = 'Internal server error') => 
    apiError(message, HTTP_STATUS.INTERNAL_SERVER_ERROR, ERROR_CODES.UNKNOWN_ERROR),
  
  validationFailed: (details: any) => 
    apiError('Validation failed', HTTP_STATUS.UNPROCESSABLE_ENTITY, ERROR_CODES.VALIDATION_FAILED, details),
}; 