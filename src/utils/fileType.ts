export const getFileExtensionByFileType = (fileType, urlExtension) => {
  let fileExtension = urlExtension;
  if (fileType == "image/jpeg") {
    fileExtension = "jpg";
  } else if (fileType == "image/png") {
    fileExtension = "png";
  } else if (fileType == "image/gif") {
    fileExtension = "gif";
  } else if (fileType == "image/webp") {
    fileExtension = "webp";
  } else if (fileType == "image/svg+xml") {
    fileExtension = "svg";
  } else if (fileType == "image/bmp") {
    fileExtension = "bmp";
  } else if (fileType == "image/tiff") {
    fileExtension = "tiff";
  } else if (fileType == "image/vnd.microsoft.icon") {
    fileExtension = "ico";
  } else if (fileType == "webp") {
    fileExtension = "webp";
  } else if (fileType == "audio/mpeg") {
    fileExtension = "mp3";
  } else if (fileType == "audio/wav") {
    fileExtension = "wav";
  } else if (fileType == "audio/ogg") {
    fileExtension = "ogg";
  } else if (fileType == "audio/flac") {
    fileExtension = "flac";
  } else if (fileType == "video/mp4") {
    fileExtension = "mp4";
  } else if (fileType == "video/quicktime") {
    fileExtension = "mov";
  } else if (fileType == "application/pdf") {
    fileExtension = "pdf";
  } else if (fileType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document") {
    fileExtension = "docx";
  } else if (fileType == "application/x-tex") {
    fileExtension = "tex";
  } else if (fileType == "text/plain") {
    fileExtension = "txt";
  }

  return fileExtension;
};

export const getContentTypeByFileExtension = (fileExtension) => {
  let contentType = fileExtension;
  if (fileExtension == "jpg") {
    contentType = "image/jpeg";
  } else if (fileExtension == "jpeg") {
    contentType = "image/jpeg";
  } else if (fileExtension == "png") {
    contentType = "image/png";
  } else if (fileExtension == "gif") {
    contentType = "image/gif";
  } else if (fileExtension == "webp") {
    contentType = "image/webp";
  } else if (fileExtension == "svg") {
    contentType = "image/svg+xml";
  } else if (fileExtension == "bmp") {
    contentType = "image/bmp";
  } else if (fileExtension == "tiff") {
    contentType = "image/tiff";
  } else if (fileExtension == "ico") {
    contentType = "image/vnd.microsoft.icon";
  } else if (fileExtension == "mp4") {
    contentType = "video/mp4";
  } else if (fileExtension == "mov") {
    contentType = "video/quicktime";
  } else if (fileExtension == "avi") {
    contentType = "video/x-msvideo";
  } else if (fileExtension == "mkv") {
    contentType = "video/x-matroska";
  } else if (fileExtension == "mp3") {
    contentType = "audio/mpeg";
  } else if (fileExtension == "wav") {
    contentType = "audio/wav";
  } else if (fileExtension == "ogg") {
    contentType = "audio/ogg";
  } else if (fileExtension == "flac") {
    contentType = "audio/flac";
  } else if (fileExtension == "pdf") {
    contentType = "application/pdf";
  } else if (fileExtension == "docx") {
    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  } else if (fileExtension == "tex") {
    contentType = "application/x-tex";
  } else if (fileExtension == "txt") {
    contentType = "text/plain";
  }
  return contentType;
};
