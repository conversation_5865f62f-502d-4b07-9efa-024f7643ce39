import type { MetadataRoute } from "next";
import { locales } from "~/i18n/config";

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: "*",
      allow: "*",
      disallow: getDisallowedPaths(),
    },
    sitemap: `${process.env.NEXT_PUBLIC_SITE_URL}/sitemap.xml`,
  };
}

function getDisallowedPaths() {
  const disallowPaths = [""];
  disallowPaths.push("/_next/");
  return disallowPaths;
}
