import { setRequestLocale } from 'next-intl/server';
import { RecommendationsComponent } from '~/components/manage/recommendations/RecommendationsComponent';
import HeaderManage from '~/components/common/HeaderManage';

export default async function RecommendationsPage(props) {
  const { locale } = await props.params;
  setRequestLocale(locale);
  return (
    <>
      <HeaderManage locale={locale} page="manage/recommendations" />
      <div className="container mx-auto px-4 py-8">
        <RecommendationsComponent locale={locale} />
      </div>
    </>
  );
} 