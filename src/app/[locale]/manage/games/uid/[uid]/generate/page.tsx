import { setRequestLocale } from 'next-intl/server';
import { GameGeneratePagesComponent } from '~/components/manage/games/GameGeneratePagesComponent';

export default async function GenerateGamePagesByUid(props) {
  // Make sure to await the params before using them
  const params = await props.params;
  const locale = params?.locale || '';
  const uid = params?.uid || '';

  setRequestLocale(locale);

  return (
    <GameGeneratePagesComponent locale={locale} gameUid={uid} />
  );
} 