import { Metadata } from "next";
import GameDetailComponent from "~/components/manage/games/GameDetailComponent";

export const metadata: Metadata = {
  title: "Game Management - Detail",
  description: "Game management detail page",
};

export default async function GameDetailPage(props) {
  const params = await props.params;
  const { locale, uid } = params;
  return <GameDetailComponent locale={locale} gameUid={uid} />;
} 