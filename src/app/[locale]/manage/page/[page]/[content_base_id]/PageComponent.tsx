'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useInterval } from "ahooks";
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { toast } from "react-toastify";
import Cta from "~/components/landing/Cta";
import Faq from "~/components/landing/Faq";
import Feature from "~/components/landing/Feature";
import HowToUse from "~/components/landing/HowToUse";
import IframeCard from "~/components/landing/IframeCard";
import Introduction from "~/components/landing/Introduction";
import List from "~/components/landing/List";
import ListSlider from "~/components/landing/ListSlider";
import { pagePage } from "~/configs/globalConfig";
import { HexColorPicker } from "react-colorful";

const PageComponent = ({ locale, page, content_base_id, contentSection }) => {
  const router = useRouter();
  const [pagePath] = useState(`manage/page/${page}/${content_base_id}`);

  const { setShowLoadingModal, userData } = useCommonContext();
  const [openColorPicker, setOpenColorPicker] = useState<string | null>(null);
  const [isStylesExpanded, setIsStylesExpanded] = useState(false);
  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => { };
  }, []);

  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  const checkUser = async () => {
    if (!userData?.user_id) {
    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        getPublishedPageList();
      }
    }
  };
  useInterval(() => {
    checkUser();
  }, intervalCheckUser);

  const [publishedPageList, setPublishedPageList] = useState([]);

  const getPublishedPageList = async () => {
    // 如果 section_name 为 ListSlider / List 则 获取已发布的 page
    if (contentSection.section_name === "ListSlider" || contentSection.section_name === "List") {
      const response = await fetch(`/api/manage/page/getPublishedPageList`);

      const result = await response.json();
      setPublishedPageList(result);
    }
  };

  const [sectionJson, setSectionJson] = useState(contentSection?.section_json || {});
  const [contentJson, setContentJson] = useState(contentSection?.content_json || {});
  const [contentStyles, setContentStyles] = useState(contentSection?.content_styles || {});

  const saveContent = async () => {
    setShowLoadingModal(true);
    const requestData = {
      content_base_id,
      content_json: contentJson,
      content_styles: contentStyles,
    };
    const response = await fetch(`/api/manage/content/saveContentJson`, {
      method: "POST",
      body: JSON.stringify(requestData),
    });
    const result = await response.json();
    setShowLoadingModal(false);
    if (result.code === 200) {
      toast(result.message, {
        type: "success",
      });
    }
  };

  if (!whiteUser) {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page={pagePath} />
      </>
    );
  }
  // 获取网站配置的背景色和文字颜色,如果没有配置则使用默认值
  const backgroundColor = "#ffffff";
  const textColor = "#000000";

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <nav className="max-w-[80%] mx-auto px-4 sm:px-6 lg:px-8 mb-4">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <a href="/manage/page" className="hover:text-gray-700">
                game管理
              </a>
            </li>
            <li>
              <span className="mx-2">/</span>
            </li>
            <li>
              <a href={`/manage/page/${page}`} className="hover:text-gray-700">
                {page}
              </a>
            </li>
            <li>
              <span className="mx-2">/</span>
            </li>
            <li>
              <span className="hover:text-gray-700 text-green-600 font-bold">{contentSection.section_name}</span>
            </li>
          </ol>
        </nav>
        <div className="max-w-[80%] mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow rounded-lg p-6 max-w-[60%] mx-auto">
            <div className="space-y-6">
              {sectionJson &&
                Object.entries(sectionJson).map(([key, value]) => {
                  return (
                    <div key={key} className="flex flex-col space-y-2 p-4 border border-gray-200 rounded-md hover:border-indigo-300 transition-colors">
                      <label className="text-sm font-medium text-gray-700 flex items-center">
                        <span className="bg-gray-100 px-2 py-1 rounded">{key}</span>
                      </label>

                      {value === "string" && (
                        <textarea
                          value={contentJson[key] ? contentJson[key].replace(/\n/g, "\\n") : ""}
                          onChange={(e) => {
                            setContentJson((prev) => ({
                              ...prev,
                              [key]: e.target.value.replace(/\\n/g, "\n"),
                            }));
                          }}
                          ref={(textArea) => {
                            if (textArea) {
                              textArea.style.height = "auto";
                              textArea.style.height = textArea.scrollHeight + "px";
                            }
                          }}
                          className="py-3 px-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm custom-textarea whitespace-pre-wrap"
                        />
                      )}

                      {value === "boolean" && (
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={contentJson[key]}
                            onChange={(e) => {
                              setContentJson((prev) => ({
                                ...prev,
                                [key]: e.target.checked,
                              }));
                            }}
                            className="py-3 px-2 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-600">{contentJson[key] ? "是" : "否"}</span>
                        </div>
                      )}

                      {Array.isArray(value) && (
                        <div className="space-y-2">
                          {contentJson[key]?.map((item, index) => (
                            <div key={index} className="flex flex-col gap-2 p-4 border border-gray-200 rounded-md">
                              {Object.entries(item).map(([itemKey, itemValue]) => (
                                <div key={itemKey} className="flex flex-col gap-2">
                                  <label className="text-sm font-medium text-gray-700">{itemKey} </label>
                                  {sectionJson[key][0][itemKey] === "selector" ? (
                                    <select
                                      title="选择question的层级"
                                      value={String(itemValue) || "h4"}
                                      onChange={(e) => {
                                        const newArray = [...contentJson[key]];
                                        newArray[index] = {
                                          ...newArray[index],
                                          [itemKey]: e.target.value,
                                        };
                                        setContentJson((prev) => ({
                                          ...prev,
                                          [key]: newArray,
                                        }));
                                      }}
                                      className="py-3 px-2 border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm rounded-lg block w-full"
                                    >
                                      <option value="h2">h2</option>
                                      <option value="h3">h3</option>
                                      <option value="h4">h4</option>
                                    </select>
                                  ) : (
                                    <textarea
                                      value={String(itemValue)}
                                      ref={(textArea) => {
                                        if (textArea) {
                                          textArea.style.height = "auto";
                                          textArea.style.height = textArea.scrollHeight + "px";
                                        }
                                      }}
                                      onChange={(e) => {
                                        const newArray = [...contentJson[key]];
                                        newArray[index] = {
                                          ...newArray[index],
                                          [itemKey]: e.target.value,
                                        };
                                        setContentJson((prev) => ({
                                          ...prev,
                                          [key]: newArray,
                                        }));
                                        // 自动调整高度
                                        e.target.style.height = "auto";
                                        e.target.style.height = e.target.scrollHeight + "px";
                                      }}
                                      className="py-3 px-2 border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm custom-textarea rounded-lg block w-full text-slate-600 placeholder:text-gray-400 text-lg p-4 "
                                    />
                                  )}
                                </div>
                              ))}
                              <div className="flex justify-end">
                                <button
                                  onClick={() => {
                                    const newArray = contentJson[key].filter((_, i) => i !== index);
                                    setContentJson((prev) => ({
                                      ...prev,
                                      [key]: newArray,
                                    }));
                                  }}
                                  className="p-2 text-red-600 hover:text-red-800"
                                >
                                  删除
                                </button>
                              </div>
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              const emptyItem = {};
                              if (contentJson[key]?.[0]) {
                                Object.keys(contentJson[key][0]).forEach((k) => {
                                  emptyItem[k] = "";
                                });
                              } else if (sectionJson[key]?.[0]) {
                                Object.keys(sectionJson[key][0]).forEach((k) => {
                                  emptyItem[k] = "";
                                });
                              }
                              setContentJson((prev) => ({
                                ...prev,
                                [key]: [...(prev[key] || []), emptyItem],
                              }));
                            }}
                            className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            添加新项
                          </button>
                        </div>
                      )}
                    </div>
                  );
                })}

              <div className="mt-4 space-y-4">
                <div className="flex items-center justify-between cursor-pointer" onClick={() => setIsStylesExpanded(!isStylesExpanded)}>
                  <h4 className="text-sm font-medium text-gray-700">样式配置</h4>
                  <svg className={`w-5 h-5 transition-transform ${isStylesExpanded ? "rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>

                {isStylesExpanded &&
                  Object.entries(contentSection.styles).map(([styleKey, styleType]) => (
                    <div key={styleKey} className="flex flex-col gap-2">
                      <label className="text-sm font-medium text-gray-700 flex items-center">
                        <span className="bg-gray-100 px-2 py-1 rounded">{styleKey}</span>
                      </label>
                      <div className="relative flex items-start ml-2">
                        <div
                          className="w-12 h-12 rounded cursor-pointer border border-gray-300"
                          style={{ backgroundColor: contentStyles[styleKey] || "#ffffff" }}
                          onClick={() => setOpenColorPicker(styleKey)}
                        />
                        <div className="mt-2 flex items-center justify-between ml-4">
                          <input
                            type="text"
                            value={contentStyles[styleKey] || ""}
                            onChange={(e) => {
                              setContentStyles((prev) => ({
                                ...prev,
                                [styleKey]: e.target.value,
                              }));
                            }}
                            className="w-full text-sm px-2 py-1 border rounded"
                          />
                        </div>
                        {openColorPicker === styleKey && (
                          <div className="absolute left-0 top-full mt-2 z-20">
                            <div className="fixed inset-0" onClick={() => setOpenColorPicker(null)} />
                            <div className="relative">
                              <HexColorPicker
                                className="w-64 h-64 z-20"
                                color={contentStyles[styleKey] || "#ffffff"}
                                onChange={(color) => {
                                  setContentStyles((prev) => ({
                                    ...prev,
                                    [styleKey]: color,
                                  }));
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>

              {(contentSection.section_name === "ListSlider" || contentSection.section_name === "List") && publishedPageList?.length > 0 && (
                <div className="mt-8 border-t border-gray-200 pt-8">
                  <h3 className="text-lg font-medium leading-6 text-gray-900 mb-4">选择要展示的已发布页面</h3>
                  <div className="space-y-4">
                    {publishedPageList.map((item) => (
                      <div key={item.id} className="flex items-center">
                        <input
                          type="checkbox"
                          name="page"
                          id={`page-${item.id}`}
                          checked={contentJson.items?.some((i) => i.link === `/${pagePage}/${item.id}`)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              // 添加新项
                              const newItem = {
                                name: item.page_name,
                                description: item.page_description || "",
                                banner: item.page_banner || "",
                                link: `/${pagePage}/${item.id}`,
                              };
                              setContentJson((prev) => ({
                                ...prev,
                                items: [...(prev.items || []), newItem],
                              }));
                            } else {
                              // 移除项
                              setContentJson((prev) => ({
                                ...prev,
                                items: prev.items.filter((i) => i.link !== `/${pagePage}/${item.id}`),
                              }));
                            }
                          }}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`page-${item.id}`} className="ml-3 block text-sm font-medium">
                          <span className="text-gray-700">
                            {pagePage}/{item.page_url}
                          </span>
                          <span className="text-blue-600 ml-2">({item.page_name})</span>
                          <span className={`ml-2 ${item.show_in_index_page ? "text-purple-600" : "text-gray-500"}`}>[{item.show_in_index_page ? "首页Game" : "非首页Game"}]</span>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="mt-4 flex justify-center space-x-4">
              <button
                onClick={saveContent}
                className="inline-flex items-center px-24 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              >
                保 存
              </button>
            </div>
          </div>
          <div className="mt-4 max-w-[60%] mx-auto">
            <div className="bg-gray-100 p-4 rounded-lg">
              <div className="font-medium text-gray-700 mb-2">JSON 预览与编辑:</div>
              <textarea
                value={JSON.stringify(Object.keys(contentJson).length === 0 ? sectionJson : contentJson, null, 2)}
                onChange={(e) => {
                  try {
                    const newJson = JSON.parse(e.target.value);
                    setContentJson(newJson);
                  } catch (error) {
                    console.error("JSON 解析错误");
                  }
                }}
                className="w-full h-64 font-mono text-sm p-2 border border-gray-300 rounded focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500"
                spellCheck="false"
              />
            </div>
          </div>
          <div
            className="mt-4 bg-background"
            style={{
              color: textColor,
              backgroundColor: backgroundColor,
            }}
          >
            {contentSection.section_name === "Cta" ? <Cta landingInfo={contentJson} styles={contentStyles} /> : null}
            {contentSection.section_name === "Faq" ? <Faq landingInfo={contentJson} styles={contentStyles} /> : null}
            {contentSection.section_name === "Feature" ? <Feature landingInfo={contentJson} styles={contentStyles} /> : null}
            {contentSection.section_name === "HowToUse" ? <HowToUse landingInfo={contentJson} styles={contentStyles} /> : null}
            {contentSection.section_name === "Introduction" ? <Introduction landingInfo={contentJson} styles={contentStyles} /> : null}
            {contentSection.section_name === "IframeCard" && contentJson.src ? (
              <IframeCard
                landingInfo={{
                  src: contentJson.src,
                  title: contentJson.title,
                  description: contentJson.description,
                }}
                styles={contentStyles}
              />
            ) : null}
            {contentSection.section_name === "List" ? <List landingInfo={contentJson} /> : null}
            {contentSection.section_name === "ListSlider" ? <ListSlider landingInfo={contentJson} /> : null}
          </div>
        </div>
      </div>
    </>
  );
};

export default PageComponent;
