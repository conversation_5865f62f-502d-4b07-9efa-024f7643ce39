import { getPageData, getPageContentSection } from "~/servers/manage/page/page";
import PageComponent from "./PageComponent";

export default async function Page(props) {
  const params = await props.params;

  const {
    locale,
    page
  } = params;

  const pageData = await getPageData(page, 0);
  const contentSection = await getPageContentSection(page);
  return <PageComponent locale={locale} page={page} pageData={pageData} contentSection={contentSection} />;
}
