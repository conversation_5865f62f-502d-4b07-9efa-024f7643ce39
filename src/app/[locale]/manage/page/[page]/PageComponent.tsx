'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";
import { manageLandingPage } from "~/configs/globalConfig";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { useInterval } from "ahooks";
import { toast } from "react-toastify";
import ConfirmModal from "~/components/common/ConfirmModal";
import CopyToClipboard from "~/components/common/CopyToClipboard";

const PageComponent = ({
  locale,
  page,
  pageData
}) => {
  const router = useRouter();
  const [pagePath] = useState(manageLandingPage + "/page/" + page);

  const {
    setShowLoadingModal,
    userData
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => {
    }
  }, []);

  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  const checkUser = async () => {
    if (!userData?.user_id) {

    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        getSectionList();
        getPageSectionList();
        getContentList();
      }
    }
  }
  useInterval(() => {
    checkUser();
  }, intervalCheckUser);


  const [sectionList, setSectionList] = useState([]);

  const getSectionList = async () => {
    setShowLoadingModal(true);
    const res = await fetch("/api/manage/section/getPublishList");
    const data = await res.json();
    if (data.code === 200) {
      setSectionList(data.resultList);
    }
    setShowLoadingModal(false);
  }

  const [selectedSectionList, setSelectedSectionList] = useState([]);

  const savePageSection = async () => {
    setShowLoadingModal(true);
    const requestData = {
      page_url: page,
      sectionList: selectedSectionList
    }
    const res = await fetch("/api/manage/content/savePageContent", {
      method: "POST",
      body: JSON.stringify(requestData)
    });
    const data = await res.json();
    setShowLoadingModal(false);
    if (data.code === 200) {
      toast.success("保存成功");
      getContentList();
    } else {
      toast.error(data.message || "保存失败");
    }
  }

  const getPageSectionList = async () => {
    setShowLoadingModal(true);
    const res = await fetch("/api/manage/content/getPageSectionList", {
      method: "POST",
      body: JSON.stringify({ page_url: page })
    });
    const data = await res.json();
    if (data.code === 200) {
      setSelectedSectionList(data.resultList);
    }
    setShowLoadingModal(false);
  }

  const [contentList, setContentList] = useState([]);

  const getContentList = async () => {
    setShowLoadingModal(true);
    const res = await fetch("/api/manage/content/getPageContentList", {
      method: "POST",
      body: JSON.stringify({ page_url: page })
    });
    const data = await res.json();
    if (data.code === 200) {
      setContentList(data.resultList);
    }
    setShowLoadingModal(false);
  }

  const changeContentStatus = async (content) => {
    if (!content.content_json) {
      toast("请先编辑内容并保存", {
        type: "error",
      });
      return;
    }
    const requestData = {
      id: content.id,
      status: content.status === 1 ? 0 : 1
    }
    setShowLoadingModal(true);
    const res = await fetch("/api/manage/content/changeStatus", {
      method: "POST",
      body: JSON.stringify(requestData)
    });
    const data = await res.json();
    setShowLoadingModal(false);
    if (data.code === 200) {
      toast(data.message, {
        type: "success",
      });
      getContentList();
    } else {
      toast(data.message || "操作失败", {
        type: "error",
      });
    }
  }

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteContent, setDeleteContent] = useState(null);

  const deleteContentById = async (content) => {
    setShowLoadingModal(true);
    if (!content) {
      return;
    }
    const requestData = {
      id: content.id
    }
    const res = await fetch("/api/manage/content/delete", {
      method: "POST",
      body: JSON.stringify(requestData)
    });
    const data = await res.json();
    setShowLoadingModal(false);
    if (data.code === 200) {
      toast(data.message, {
        type: "success",
      });
      getContentList();
    } else {
      toast(data.message || "操作失败", {
        type: "error",
      });
    }
  }

  const updateContentSort = async (content, newSort) => {
    const requestData = {
      id: content.id,
      content_sort: newSort
    }
    setShowLoadingModal(true);
    const res = await fetch("/api/manage/content/updateSort", {
      method: "POST",
      body: JSON.stringify(requestData)
    });
    const data = await res.json();
    setShowLoadingModal(false);
    if (data.code === 200) {
      toast(data.message, {
        type: "success",
      });
      getContentList();
    } else {
      toast(data.message || "操作失败", {
        type: "error",
      });
    }
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />
      <ConfirmModal isOpen={isDeleteModalOpen} onClose={() => setIsDeleteModalOpen(false)} onConfirm={() => deleteContentById(deleteContent)} message="Are you sure you want to delete this item?" />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <nav className="max-w-[80%] mx-auto px-4 sm:px-6 lg:px-8 mb-4">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li>
              <a href="/manage/page" className="hover:text-gray-700">
                game管理
              </a>
            </li>
            <li>
              <span className="mx-2">/</span>
            </li>
            <li>
              <a href={`/manage/page/${page}`} className="hover:text-gray-700 text-green-600 font-bold">
                {page}
              </a>
            </li>
          </ol>
        </nav>
        <div className="mx-auto max-w-[80%] px-4">
          <div className="flex gap-2">
            {/* 左侧列表 */}
            <div className="w-1/3 bg-gray-50 rounded-lg p-4">
              <h2 className="text-lg font-medium mb-4">可选 Content</h2>
              <div className="space-y-2">
                {sectionList.map((section) => (
                  <div
                    key={section.id}
                    className="flex items-center justify-between p-3 bg-white rounded-md shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => {
                      // 创建新对象以避免引用问题
                      const newSection = { ...section, uniqueid: Date.now() };
                      setSelectedSectionList([...selectedSectionList, newSection]);
                    }}
                  >
                    <div>
                      <div className="font-medium">{section.section_name}</div>
                      <div className="text-sm text-gray-500">类型: {section.section_type}</div>
                    </div>
                    <button className="text-blue-600 hover:text-blue-800">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* 右侧已选列表 */}
            <div className="w-2/3 bg-gray-50 rounded-lg p-4">
              <h2 className="text-lg font-medium mb-4">已选 Content</h2>
              <div className="space-y-2">
                <div className="p-4 bg-white rounded-md shadow-sm">
                  <div className="text-gray-400 text-center">
                    {selectedSectionList.length === 0 ? (
                      <div>暂无已选择Content</div>
                    ) : (
                      <div
                        className="space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto"
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.dataTransfer.dropEffect = "move";
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          const fromIndex = Number(e.dataTransfer.getData("text/plain"));
                          const toIndex = Number((e.target as HTMLElement).closest("[data-index]")?.getAttribute("data-index"));

                          if (!isNaN(fromIndex) && !isNaN(toIndex) && fromIndex !== toIndex) {
                            const newList = [...selectedSectionList];
                            const [movedItem] = newList.splice(fromIndex, 1);
                            newList.splice(toIndex, 0, movedItem);
                            setSelectedSectionList(newList);
                          }
                        }}
                      >
                        {selectedSectionList.map((section, index) => (
                          <div
                            key={section.uniqueid || section.id}
                            data-index={index}
                            draggable
                            onDragStart={(e) => {
                              e.dataTransfer.setData("text/plain", String(index));
                              e.currentTarget.classList.add("opacity-50");
                            }}
                            onDragEnd={(e) => {
                              e.currentTarget.classList.remove("opacity-50");
                            }}
                            className="flex items-center justify-between p-3 mb-2 bg-white rounded-md shadow-sm cursor-move hover:shadow-md"
                          >
                            <div className="flex flex-col w-full">
                              <div className="flex items-center justify-between">
                                <div className="font-medium text-gray-800">{section.section_name}</div>
                                <div className="text-sm text-gray-500 flex items-center gap-1">
                                  <span className="text-gray-400">保存后的排序:</span>
                                  <span className="px-2 py-0.5 bg-green-50 text-green-600 rounded-full text-xs font-medium">{section.content_sort}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-4 mt-3">
                                <div className="text-sm text-gray-500 flex items-center gap-1">
                                  <span className="text-gray-400">类型:</span>
                                  <span className="px-2 py-0.5 bg-blue-50 text-blue-600 rounded-full text-xs font-medium">{section.section_type}</span>
                                </div>
                                <div className="text-sm text-gray-500 flex items-center gap-1">
                                  <span className="text-gray-400">标题:</span>
                                  <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">{section.content_json?.title || "-"}</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-4 ml-4">
                              <div className="cursor-move">
                                <svg className="w-5 h-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8h16M4 16h16" />
                                </svg>
                              </div>
                              <button
                                className="text-red-500 hover:text-red-700 transition-colors duration-200"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedSectionList(selectedSectionList.filter((_, i) => i !== index));
                                }}
                              >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex justify-center w-full mt-4">
                <button
                  className="inline-flex items-center px-24 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  onClick={() => savePageSection()}
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8 w-full max-w-[80%] mx-auto px-4">
          <div className="flex items-center gap-4 mb-6">
            <h2 className="text-xl font-semibold text-gray-800">Content管理</h2>
            <button
              onClick={() => getContentList()}
              className="inline-flex items-center px-5 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 ease-in-out"
            >
              <svg className="w-5 h-5 mr-2 animate-spin-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              刷新列表
            </button>
          </div>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      section_name
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      section_type
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      content_json
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      styles
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      content_json.title
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      排序
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      状态
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      操作
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      更新时间
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {contentList?.map((content) => (
                    <tr key={content.uniqueId} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {content?.section_name}
                          <CopyToClipboard text={content?.section_name} />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {content?.section_type}
                          <CopyToClipboard text={content?.section_type} />
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm font-medium text-gray-900 cursor-pointer break-words max-h-24 overflow-y-auto">
                          {content.content_json ? JSON.stringify(content.content_json) : ""}
                          {content.content_json && <CopyToClipboard text={content.content_json ? JSON.stringify(content.content_json) : ""} />}
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm font-medium text-gray-900 cursor-pointer break-words max-h-24 overflow-y-auto">
                          {content.content_styles ? JSON.stringify(content.content_styles) : ""}
                          {content.content_styles && <CopyToClipboard text={content.content_styles ? JSON.stringify(content.content_styles) : ""} />}
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm font-medium text-gray-900 cursor-pointer break-words max-h-24 overflow-y-auto">
                          {content?.content_json?.title}
                          {content?.content_json?.title && <CopyToClipboard text={content?.content_json?.title} />}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          <input
                            min={0}
                            type="number"
                            className="w-16 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            defaultValue={content?.content_sort}
                            onBlur={(e) => {
                              const newSort = parseInt(e.target.value);
                              if (newSort !== content?.content_sort) {
                                updateContentSort(content, newSort);
                              }
                            }}
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            content?.status === 1 ? "bg-green-100 text-green-800 border border-green-200" : "bg-gray-100 text-gray-800 border border-gray-200"
                          }`}
                        >
                          <span className={`w-2 h-2 rounded-full mr-2 ${content?.status === 1 ? "bg-green-400" : "bg-gray-400"}`}></span>
                          {content.status === 1 ? "已发布" : "未发布"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <a
                            href={`/manage/page/${page}/${content?.content_base_id}`}
                            target="_blank"
                            className="inline-flex items-center px-3 py-1.5 border border-indigo-600 text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transition-colors duration-200"
                          >
                            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                            编辑内容
                          </a>
                          <button
                            onClick={() => changeContentStatus(content)}
                            className={`inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                              content?.status === 1 ? "bg-blue-50 text-blue-600 hover:bg-blue-100 border border-blue-600" : "bg-green-50 text-green-600 hover:bg-green-100 border border-green-600"
                            }`}
                          >
                            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                            </svg>
                            {content?.status === 1 ? "取消发布" : "设为发布"}
                          </button>
                          <button
                            onClick={() => {
                              setDeleteContent(content);
                              setIsDeleteModalOpen(true);
                            }}
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-red-50 text-red-600 hover:bg-red-100 border border-red-600"
                          >
                            删除
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 max-w-xs truncate">{new Date(content?.created_at).toLocaleString()}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 max-w-xs truncate">{new Date(content?.updated_at).toLocaleString()}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default PageComponent
