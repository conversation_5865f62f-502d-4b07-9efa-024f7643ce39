'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useCommonContext } from "~/context/common-context";
import HeadInfo from "~/components/common/HeadInfo";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";
import IframeCard from "~/components/landing/IframeCard";
import Cta from "~/components/landing/Cta";
import Faq from "~/components/landing/Faq";
import Feature from "~/components/landing/Feature";
import Introduction from "~/components/landing/Introduction";
import List from "~/components/landing/List";
import HowToUse from "~/components/landing/HowToUse";
import ListSlider from "~/components/landing/ListSlider";
import { pagePage } from "~/configs/globalConfig";

const componentMap = {
  IframeCard,
  Cta,
  Faq,
  Feature,
  Introduction,
  List,
  HowToUse,
  ListSlider,
};

const PageComponent = ({ locale, page, pageData }) => {
  const router = useRouter();
  const [pagePath] = useState(`manage/${pagePage}/${page}`);

  const { setShowLoadingModal, commonText } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => { };
  }, []);

  const hasAnyKey = (obj) => {
    return Object.keys(obj).length > 0;
  };

  return (
    <>
      <meta name="robots" content="noindex" />
      <div className={`fixed top-0 left-0 w-full ${pageData.status === 0 ? "bg-yellow-500" : "bg-blue-600"} text-white text-center py-2 z-50`}>
        这是「{page}」的的预览页面 : {pageData.status === 0 ? "未发布" : "已发布"}
        {pageData.status === 1 && (
          <a href={`/${pagePage}/${page}`} className="ml-4 underline hover:text-blue-200" target="_blank">
            查看正式页面
          </a>
        )}
      </div>
      <HeadInfo locale={locale} page={pagePath} title={pageData.title} description={pageData.description} />
      <Header locale={locale} page={pagePath} />
      {/* 动态组件 */}
      <div className="pt-14">
        {pageData.components?.map?.((component, index) => {
          const Component = componentMap[component.type];
          return <Component key={index} landingInfo={component.props} styles={component.styles} />;
        })}
      </div>
      <Footer locale={locale} page={pagePath} />
    </>
  );
};

export default PageComponent
