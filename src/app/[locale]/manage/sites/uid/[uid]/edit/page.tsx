import { Metadata } from "next";
import { setRequestLocale } from 'next-intl/server';
import SiteFormComponent from '~/components/manage/sites/SiteFormComponent';

export const metadata: Metadata = {
  title: "Edit Site",
  description: "Edit site details",
};

export default async function SiteEditPage(props) {
  const params = await props.params;
  const locale = params?.locale || '';
  const uid = params?.uid || '';
  
  setRequestLocale(locale);

  return (
    <SiteFormComponent 
      locale={locale} 
      siteUid={uid} 
    />
  );
} 