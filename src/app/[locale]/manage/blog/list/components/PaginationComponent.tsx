"use client";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import { useCommonContext } from "~/context/common-context";
import { useState } from "react";

const PaginationComponent = ({ locale, page, pageData, pageName }) => {
  const { setShowLoadingModal } = useCommonContext();

  const checkShowLoading = (toPage) => {
    if (page != toPage) {
      setShowLoadingModal(true);
    }
  };

  return (
    <>
      <div className={"flex justify-center items-center"}>
        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
          {pageData?.pagination?.length > 0 ? (
            page == 1 ? (
              <span
                key={2 + "Left"}
                className="no-underline relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 bg-gray-200 cursor-default focus:z-20 focus:outline-offset-0"
                onClick={(e) => e.preventDefault()}
              >
                <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
              </span>
            ) : (
              <a
                key={24 + "Left"}
                href={page == 2 ? `/${locale}/${pageName}` : `/${locale}/${pageName}/${Number(page) - 1}`}
                className="no-underline relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 bg-gray-100 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                onClick={() => checkShowLoading(Number(page) - 1)}
              >
                <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
              </a>
            )
          ) : null}
          {pageData?.pagination?.map((pa, index) => {
            let href;
            if (pa == 1) {
              href = `/${locale}/${pageName}`;
            } else if (pa == "...") {
              href = `#`;
            } else {
              href = `/${locale}/${pageName}/${pa}`;
            }
            if (pa == page) {
              return (
                <a
                  key={pa + "page"}
                  href={href}
                  aria-current="page"
                  className="no-underline relative z-10 inline-flex items-center bg-[#de5c2d] px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  onClick={() => checkShowLoading(pa)}
                >
                  {pa}
                </a>
              );
            } else {
              return (
                <a
                  key={pa + "page"}
                  href={href}
                  className="no-underline relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-gray-100 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                  onClick={() => checkShowLoading(pa)}
                >
                  {pa}
                </a>
              );
            }
          })}
          {pageData?.pagination?.length > 0 ? (
            page == pageData?.totalPage ? (
              <span
                key={page + "Right"}
                className="no-underline relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 bg-gray-200 cursor-default focus:z-20 focus:outline-offset-0"
                onClick={(e) => e.preventDefault()}
              >
                <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
              </span>
            ) : (
              <a
                key={2 + "Right"}
                href={`/${locale}/${pageName}/${Number(page) + 1}`}
                className="no-underline relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 bg-gray-100 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
                onClick={() => checkShowLoading(Number(page) + 1)}
              >
                <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
              </a>
            )
          ) : null}
        </nav>
      </div>
    </>
  );
};

export default PaginationComponent;
