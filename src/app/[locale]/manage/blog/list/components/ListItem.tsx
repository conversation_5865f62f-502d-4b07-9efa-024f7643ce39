"use client";
import moment from "moment";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import ConfirmModal from "~/components/common/ConfirmModal";
import { manageBlogWriterPage } from "~/configs/globalConfig";
import { blogPage } from "~/configs/globalConfig";

const ListItem = ({ item, getBlogList, status }) => {
  const [isHideModalOpen, setIsHideModalOpen] = useState(false);
  const router = useRouter();

  const handleEdit = () => {
    router.push(`/${manageBlogWriterPage}?uid=${item.uid}`);
  };

  const handleHideClick = () => {
    setIsHideModalOpen(true);
  };

  const handleHideConfirm = async () => {
    try {
      const body = {
        uid: item.uid,
        is_public: false,
      };
      const response = await fetch(`/api/blog/hidden`, {
        method: "POST",
        body: JSON.stringify(body),
      });
      const result = await response.json();
      if (result.code === 200) {
        toast.success("隐藏成功");
      } else {
        toast.error("隐藏失败：" + result.message);
      }
    } catch (error) {
      console.error("隐藏失败", error);
      toast.error("隐藏失败，请稍后重试");
    } finally {
      setIsHideModalOpen(false);
      getBlogList();
    }
  };

  return (
    <div className="flex w-full justify-between px-4 md:px-6 sm:gap-x-6 gap-y-4 py-5 items-start rounded-lg border border-gray-200 bg-white mb-3 shadow-sm transition duration-300">
      <ConfirmModal message="确定要隐藏这篇博客吗？" onConfirm={handleHideConfirm} onClose={() => setIsHideModalOpen(false)} isOpen={isHideModalOpen} />
      <a
        href={item.generator == "blog" ? `/${blogPage}/${item.blog_url}` : `/${item.generator}/${blogPage}/${item.blog_url}`}
        className="flex-grow cursor-pointer hover:bg-gray-50"
        target="_blank"
        rel="noopener noreferrer"
      >
        <div className="w-full grid">
          <div className="gap-x-2 text-sm leading-6 text-gray-600">
            <div className="flex justify-between text-sm font-semibold leading-6 text-gray-800 hover:text-indigo-600">
              <p className="hover:underline line-clamp-2 max-w-60 sm:max-w-[90%] break-words">{item.content_title}</p>
            </div>
          </div>

          <div className="mt-1 flex items-center gap-x-2 text-xs leading-5 text-gray-500">
            <p>
              <time dateTime={item.updated_at} suppressHydrationWarning>
                {moment(item.updated_at).format("YYYY-MM-DD HH:mm:ss")}
              </time>
            </p>
            <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-700">{item.generator || "未分类"}</span>
            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${item.is_public ? "bg-green-100 text-green-700" : "bg-yellow-100 text-yellow-700"}`}>
              {item.is_public ? "已发布" : "未发布"}
            </span>
            <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold text-blue-600`}>{item.name}</span>
          </div>
        </div>
      </a>
      {status == "my" && (
        <div className="flex items-center space-x-2">
          <button onClick={handleEdit} className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 focus:outline-none">
            编辑
          </button>
          {/* {item.is_public && (
            <button onClick={handleHideClick} className="px-3 py-1 text-sm text-yellow-600 hover:text-yellow-800 focus:outline-none">
              隐藏
            </button>
          )} */}
        </div>
      )}
    </div>
  );
};

export default ListItem;
