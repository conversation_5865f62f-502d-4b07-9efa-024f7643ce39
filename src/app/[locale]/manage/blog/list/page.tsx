import PageComponent from "./PageComponent";
import { setRequestLocale } from "next-intl/server";
import { getTotalPage } from "~/servers/manage/manageBlog";

export default async function IndexPage(props) {
  const params = await props.params;
  const { locale = "" } = params;
  // Enable static rendering
  setRequestLocale(locale);
  const json = {
    generator: "",
    pageSize: 20,
  };
  const pageData = await getTotalPage(json);

  return <PageComponent locale={locale} pageData={pageData} />;
}
