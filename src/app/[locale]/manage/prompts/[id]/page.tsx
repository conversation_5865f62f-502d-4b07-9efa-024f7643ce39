import { setRequestLocale } from 'next-intl/server';
import PromptFormComponent from '~/components/manage/prompts/PromptFormComponent';


export default async function EditPromptPage(props) {
  const params = await props.params;
  // Set the locale for the request
  await setRequestLocale(params.locale);

  // If id is "new", we're creating a new prompt
  const isNew = params.id === 'new';
  const promptId = isNew ? undefined : params.id;

  return <PromptFormComponent locale={params.locale} promptId={promptId} />;
} 