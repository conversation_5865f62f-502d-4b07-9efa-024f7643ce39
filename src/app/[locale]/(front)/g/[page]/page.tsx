import { getPageData } from "~/servers/manage/page/page";
import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';
import { notFound } from "next/navigation";
import { transformPageData } from "~/utils/handleData";

export const revalidate = 300;
export const dynamic = "force-static";

export default async function IndexPage(props) {
  const params = await props.params;

  const {
    locale = "",
    page = ""
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  // 查询已发布的数据，正式页面
  const detailPageData = await getPageData(page);

  if (detailPageData.status === 404) {
    notFound();
  }
  const pageData = transformPageData(detailPageData);

  return <PageComponent locale={locale} page={page} pageData={pageData} />;
}
