'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useCommonContext } from "~/context/common-context";
import HeadInfo from "~/components/common/HeadInfo";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";
import IframeCard from "~/components/landing/IframeCard";
import Cta from "~/components/landing/Cta";
import Faq from "~/components/landing/Faq";
import Feature from "~/components/landing/Feature";
import Introduction from "~/components/landing/Introduction";
import List from "~/components/landing/List";
import HowToUse from "~/components/landing/HowToUse";
import ListSlider from "~/components/landing/ListSlider";
import { pagePage } from "~/configs/globalConfig";
import GoogleAdsense from "~/components/GoogleAdsense";

const componentMap = {
  IframeCard,
  Cta,
  Faq,
  Feature,
  Introduction,
  List,
  HowToUse,
  ListSlider,
};

const PageComponent = ({ locale, page, pageData }) => {
  const router = useRouter();
  const [pagePath] = useState(`${pagePage}/${page}`);

  const { setShowLoadingModal, commonText } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => { };
  }, []);

  const hasAnyKey = (obj) => {
    return Object.keys(obj).length > 0;
  };

  return (
    <>
      <HeadInfo locale={locale} page={pagePath} title={pageData.title} description={pageData.description} />
      <Header locale={locale} page={pagePath} />
      {/* 动态组件 */}
      <div className="pt-14">
        {pageData.components?.map?.((component, index) => {
          const Component = componentMap[component.type];
          return <Component key={index} landingInfo={component.props} styles={component.styles} />;
        })}
      </div>
      <Footer locale={locale} page={pagePath} />
      <GoogleAdsense />
    </>
  );
};

export default PageComponent
