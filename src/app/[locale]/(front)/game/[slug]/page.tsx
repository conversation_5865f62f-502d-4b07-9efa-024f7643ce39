import { getGamePageData } from "~/servers/manage/game/page";
import { setRequestLocale } from 'next-intl/server';
import { notFound } from "next/navigation";
import { domainNameLowercase } from "~/configs/globalConfig";
import GamePageComponent from "./GamePageComponent";
import { SiteData } from "~/types/site";

export const revalidate = 300;
export const dynamic = "force-static";

interface PageParams {
  locale: string;
  slug: string;
}
interface PageProps {
  params?: PageParams | Promise<PageParams>;
}

export default async function GamePage({ params }: PageProps) {
  let resolvedParams: PageParams = { locale: '', slug: '' };
  if (params) {
    if (typeof (params as any).then === 'function') {
      resolvedParams = await params as PageParams;
    } else {
      resolvedParams = params as PageParams;
    }
  }
  const { locale, slug } = resolvedParams;

  // Enable static rendering
  setRequestLocale(locale);

  // Get page data using the new function
  const pageData = await getGamePageData(slug, domainNameLowercase) as SiteData;

  if (!pageData) {
    notFound();
  }

  return (
    <GamePageComponent 
      locale={locale}
      pageData={pageData}
    />
  );
} 