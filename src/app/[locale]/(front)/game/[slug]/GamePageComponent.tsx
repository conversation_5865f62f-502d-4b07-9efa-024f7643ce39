'use client'

import { templateComponents } from '~/configs/templatesConfig';
import { SiteData } from '~/types/site';

interface GamePageComponentProps {
  locale: string;
  pageData: SiteData;
}

const GamePageComponent = ({ locale, pageData }: GamePageComponentProps) => {

  const templateKey = pageData.page.template_key;

  // 直接同步选择模板
  const TemplateComponent = templateComponents[templateKey] || templateComponents['default'];
  const pagePath = `game/${pageData.page.slug}`;

  return <TemplateComponent locale={locale} pageData={pageData} pagePath={pagePath} />;
};

export default GamePageComponent; 