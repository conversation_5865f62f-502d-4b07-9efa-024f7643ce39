import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';
import { getCongratulationsText } from "~/i18n/languageText";

export default async function CongratulationPage(props) {
  const params = await props.params;

  const {
    locale = ''
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  const congratulationText = await getCongratulationsText();

  return (
    <PageComponent
      locale={locale}
      congratulationText={congratulationText}
    />
  )
}
