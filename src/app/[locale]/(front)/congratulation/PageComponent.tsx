'use client'
import React, { useEffect, useRef, useState } from "react";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";
import { useCommonContext } from "~/context/common-context";
import confetti from 'canvas-confetti';
import { useInterval } from "ahooks";
import HeadInfo from "~/components/common/HeadInfo";
import { googleAwId, googleAwOpen } from "~/configs/globalConfig";

const PageComponent = ({
  locale,
  congratulationText
}) => {
  const [pagePath] = useState("congratulation");

  const {
    setShowLoadingModal,
    userData
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    showConfetti();
    return () => {
    }
  }, []);

  const [intervalRedirect, setIntervalRedirect] = useState(3000);
  useInterval(() => {
    redirectToOriginalPage();
  }, intervalRedirect);
  const redirectToOriginalPage = () => {
    const successUrl = localStorage.getItem("successUrl");
    if (successUrl) {
      localStorage.removeItem("successUrl");
      window.location.href = successUrl;
    }
  }

  const showConfetti = () => {
    confetti({ particleCount: 1000, spread: 200, origin: { y: 0.6 } });
  }


  return (
    <>
      <meta name="robots" content="noindex" />
      {
        googleAwOpen ?
          <head>
            <script async
              src={`https://www.googletagmanager.com/gtag/js?id=${googleAwId}`}></script>
            <script
              dangerouslySetInnerHTML={{
                __html: `
                        window.dataLayer = window.dataLayer || [];
                        function gtag(){dataLayer.push(arguments);}
                        gtag('js', new Date());
                        gtag('config', ${googleAwId});
                        `,
              }}
            />
          </head>
          : null
      }
      <HeadInfo
        locale={locale}
        page={pagePath}
        title={congratulationText.title}
        description={congratulationText.description}
      />
      <Header
        locale={locale}
        page={pagePath}
      />
      <div className="pt-4 my-auto min-h-[80vh]">
        <div className="block overflow-hidden text-white">
          <div className="w-[95%] md:w-[65%] mx-auto px-5 mb-5">
            <div
              className="mx-auto flex max-w-4xl flex-col items-center text-center py-36">
              <h1 className="text-4xl font-bold md:text-6xl">{congratulationText.h1Text}</h1>
              <div className="mb-5 max-w-[628px] my-4">
                <h2 className="text-[#7c8aaa] text-xl">{congratulationText.h2Text}</h2>
              </div>
            </div>

          </div>
        </div>

      </div>
      <Footer
        locale={locale}
        page={pagePath}
      />
    </>
  )
}

export default PageComponent
