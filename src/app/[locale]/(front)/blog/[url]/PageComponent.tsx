"use client";

import Footer from "~/components/common/Footer";
import { useEffect, useRef, useState, useCallback } from "react";
import { useCommonContext } from "~/context/common-context";
import HeadInfo from "~/components/common/HeadInfo";
import HighLightMarkdown from "~/components/common/HighLightMarkdown";
import Header from "~/components/common/Header";
import { handleBlogUrl } from "~/utils/buildLink";
import BreadcrumbCommunity from "~/components/common/BreadCrumb";
import debounce from "lodash/debounce";

import moment from "moment";
import GoogleAdsense from "~/components/GoogleAdsense";

const PageComponent = ({ locale = "", blogDetail, indexText, commonText }) => {
  const [pagePath] = useState(`${process.env.NEXT_PUBLIC_BLOG}/${blogDetail.blog_url}`);
  const [tableOfContents, setTableOfContents] = useState([]);
  const [activeItem, setActiveItem] = useState("");
  const tableOfContentsRef = useRef([]);
  // 使用手动滚动
  const [isManualScroll, setIsManualScroll] = useState(false);

  const { setShowLoadingModal } = useCommonContext();

  const handleScroll = useCallback(
    debounce(() => {
      if (isManualScroll) return;

      const scrollPosition = window.scrollY;
      const headings = tableOfContentsRef.current.map((item) => document.getElementById(item.id)).filter(Boolean);

      let activeId = "";
      for (let i = 0; i < headings.length; i++) {
        if (headings[i].offsetTop > scrollPosition + 200) {
          activeId = i > 0 ? headings[i - 1].id : headings[0].id;
          break;
        }
      }
      if (!activeId && headings.length > 0) {
        activeId = headings[headings.length - 1].id;
      }
      setActiveItem(activeId);
    }, 100),
    [isManualScroll]
  );

  useEffect(() => {
    setShowLoadingModal(false);
    generateTableOfContents();
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      handleScroll.cancel();
    };
  }, [handleScroll]);

  useEffect(() => {
    tableOfContentsRef.current = tableOfContents;
  }, [tableOfContents]);

  const menuList =
    blogDetail.generator == "blog"
      ? [{ pageName: blogDetail.content_title, pagePath: pagePath }]
      : [
        { pageName: blogDetail.generator, pagePath: `${process.env.NEXT_PUBLIC_GENERATOR}/${blogDetail.generator}` },
        { pageName: blogDetail.content_title, pagePath: pagePath },
      ];

  const generateTableOfContents = () => {
    const headings = blogDetail.content_markdown.match(/^#{1,6}\s+.+$/gm);
    if (headings) {
      const toc = headings.map((heading) => {
        const level = heading.match(/^#+/)[0].length;
        const title = heading
          .replace(/^#+\s+/, "") // 移除开头的 #
          .replace(/\*\*(.*?)\*\*/g, "$1") // 移除加粗 **
          .replace(/__(.*?)__/g, "$1") // 移除加粗 __
          .replace(/\*(.*?)\*/g, "$1") // 移除斜体 *
          .replace(/_(.*?)_/g, "$1"); // 移除斜体 _
        const id = handleBlogUrl(title);
        return { level, title, id };
      });
      setTableOfContents(toc);
    }
  };

  const scrollToAnchor = (id) => {
    const element = document.getElementById(id);
    if (element) {
      setIsManualScroll(true);
      setActiveItem(id);
      element.scrollIntoView({ behavior: "smooth" });
      setTimeout(() => {
        setIsManualScroll(false);
      }, 1000);
    }
  };

  return (
    <>
      <HeadInfo locale={locale} page={pagePath} title={blogDetail.content_title} description={blogDetail.description} />
      <Header locale={locale} page={pagePath} />

      <div className="bg-white min-h-screen pb-20 mt-24 shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <BreadcrumbCommunity locale={locale} menuList={menuList} />
          <div className="flex lg:gap-8">
            {/* Sidebar */}
            <div className="md:w-[30%] md:sticky md:top-14 self-start h-fit max-h-[calc(100vh-5rem)] overflow-y-auto mt-10">
              <div className="max-h-[calc(100vh-2rem)] border-l border-gray-200">
                <div className="text-lg font-semibold mb-4 text-gray-900 pl-3">{blogDetail.content_title}</div>
                <nav className="space-y-1">
                  {tableOfContents.map((item, index) => (
                    <a
                      key={index}
                      onClick={(e) => {
                        scrollToAnchor(item.id);
                      }}
                      className={`block py-2 px-3 text-sm transition-colors duration-200 cursor-pointer ${activeItem === item.id ? "border-l-2 border-blue-500 bg-blue-50 text-blue-700" : "text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:border-l-2 hover:border-gray-300"
                        }`}
                      style={{ paddingLeft: `${(item.level - 1) * 0.75 + 0.75}rem` }}
                    >
                      {item.title}
                    </a>
                  ))}
                </nav>
              </div>
            </div>

            {/* Main content */}
            <div className="mt-8 bg-white lg:mt-0 lg:flex-grow">
              <article className="">
                <div className="px-6 py-8">
                  <h1 className="text-5xl font-bold text-gray-900 mb-6">{blogDetail.content_title}</h1>
                  <div className="flex items-center gap-x-2 text-md  text-gray-600 mb-6 ">
                    <time dateTime={blogDetail.updated_at} suppressHydrationWarning>
                      Updated at <span className="font-semibold text-blue-600">{moment(blogDetail.updated_at).format("MMMM D, YYYY")}</span>
                    </time>{" "}
                    <span className="font-semibold text-gray-600">|</span>
                    <time dateTime={blogDetail.created_at} suppressHydrationWarning>
                      Written at <span className="font-semibold text-blue-600">{moment(blogDetail.created_at).format("MMMM D, YYYY")}</span>
                    </time>
                  </div>
                  <img src={blogDetail.content_banner} alt={blogDetail.content_title} className="w-full h-auto mb-6" />
                  <h2 className="text-gray-600 text-lg mb-6">{blogDetail.content_description}</h2>
                  <div className="my-16">
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">{blogDetail.form_title}</h2>
                    <h3 className="text-gray-600 text-lg mb-6">{blogDetail.form_description}</h3>
                  </div>
                  <div className="prose prose-lg max-w-none">
                    <HighLightMarkdown markdown={blogDetail.content_markdown} />
                  </div>
                </div>
              </article>

              {/* Related blogs */}
              {blogDetail.relatedBlogs && blogDetail.relatedBlogs.length > 0 && (
                <div className="mt-12">
                  <div className="relative mb-6">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-gray-200"></div>
                    </div>
                    <div className="relative flex ">
                      <h2 className="px-3 bg-white text-3xl font-bold text-gray-900">More Blogs</h2>
                    </div>
                  </div>
                  <div className="grid">
                    {blogDetail.relatedBlogs.map((blog, index) => (
                      <a
                        key={index}
                        href={blog.generator == "blog" ? `/${process.env.NEXT_PUBLIC_BLOG}/${blog.blog_url}` : `/${process.env.NEXT_PUBLIC_GENERATOR}/${blog.generator}/${blog.blog_url}`}
                        className="block group mb-4"
                      >
                        <div className="bg-white shadow-sm rounded-md overflow-hidden transition-all duration-300 group-hover:shadow-md flex gap-6 ">
                          <div className="w-1/3">
                            <img src={blog.content_banner || "/placeholder.jpg"} alt={blog.content_title} className="object-cover w-full h-36" />
                          </div>
                          <div className="w-2/3 p-4">
                            <h3 className="text-2xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">{blog.content_title}</h3>
                            <p className="mt-2 text-sm text-gray-600 line-clamp-3">{blog.description}</p>
                          </div>
                        </div>
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Footer locale={locale} page={pagePath} />
      <GoogleAdsense />
    </>
  );
};

export default PageComponent;
