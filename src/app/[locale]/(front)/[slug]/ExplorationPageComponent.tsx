'use client';

import { templateComponents } from '~/configs/templatesConfig';
import { Page, ExplorationGamesResult } from "~/lib/api/pages";
import { Site } from "~/types/site";

interface ExplorationPageComponentProps {
  locale: string;
  page: Page;
  site: Site;
  gamesResult: ExplorationGamesResult;
}

const ExplorationPageComponent = ({
  locale,
  page,
  site,
  gamesResult
}: ExplorationPageComponentProps) => {

  // 构造 SiteData 格式的数据
  const pageData = {
    page,
    site,
    recommendedGames: [],
    recommendationZones: [],
  };

  // 获取模板组件
  const templateKey = page.template_key;
  const TemplateComponent = templateComponents[templateKey];
  const pagePath = page.slug;

  return (
    <TemplateComponent
      locale={locale}
      pageData={pageData}
      pagePath={pagePath}
      gamesResult={gamesResult}
    />
  );
};

export default ExplorationPageComponent;
