import { getPageBySlug, getExplorationGames } from "~/lib/api/pages";
import { setRequestLocale } from 'next-intl/server';
import { notFound } from "next/navigation";
import { domainNameLowercase } from "~/configs/globalConfig";
import ExplorationPageComponent from "./ExplorationPageComponent";
import { getSiteByDomain } from "~/servers/game/siteService";

export const revalidate = 300;
// 改为动态渲染以支持分页参数
export const dynamic = "force-dynamic";

interface PageParams {
  locale: string;
  slug: string;
}

interface PageProps {
  params?: PageParams | Promise<PageParams>;
  searchParams?: { [key: string]: string | string[] | undefined } | Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function ExplorationPage({ params, searchParams }: PageProps) {
  let resolvedParams: PageParams = { locale: '', slug: '' };
  if (params) {
    if (typeof (params as any).then === 'function') {
      resolvedParams = await params as PageParams;
    } else {
      resolvedParams = params as PageParams;
    }
  }

  let resolvedSearchParams: { [key: string]: string | string[] | undefined } = {};
  if (searchParams) {
    if (typeof (searchParams as any).then === 'function') {
      resolvedSearchParams = await searchParams as { [key: string]: string | string[] | undefined };
    } else {
      resolvedSearchParams = searchParams as { [key: string]: string | string[] | undefined };
    }
  }

  const { locale, slug } = resolvedParams;

  // Enable static rendering
  setRequestLocale(locale);

  // 首先尝试获取探索页面配置
  const explorationPage = await getPageBySlug(slug, domainNameLowercase);
  
  if (!explorationPage || !explorationPage.is_exploration_page) {
    notFound();
  }

  // 获取站点信息
  const site = await getSiteByDomain(domainNameLowercase);
  if (!site) {
    notFound();
  }

  // 获取分页参数
  const currentPage = parseInt(
    Array.isArray(resolvedSearchParams.page) 
      ? resolvedSearchParams.page[0] || '1' 
      : resolvedSearchParams.page || '1'
  );
  
  const pageSize = 12; // 默认每页12个游戏，与 getExplorationGames 函数默认值保持一致

  // 获取探索页面的游戏列表
  const explorationGamesResult = await getExplorationGames(
    explorationPage.site_uid,
    explorationPage.exploration_sort_by,
    currentPage,
    pageSize
  );

  return (
    <ExplorationPageComponent 
      locale={locale}
      page={explorationPage}
      site={site}
      gamesResult={explorationGamesResult}
    />
  );
}
