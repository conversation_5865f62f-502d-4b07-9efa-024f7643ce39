import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import { notFound } from "next/navigation";
import { setRequestLocale } from "next-intl/server";
import { ReactNode } from "react";
import { locales } from "~/i18n/config";
import { CommonProvider } from "~/context/common-context";
import { NextAuthProvider } from "~/context/next-auth-context";
import { getFooterLink } from "~/servers/common/shareData";
import { getAuthText, getCommonText, getMenuText, getPricingText } from "~/i18n/languageText";
import { checkLogin } from "~/configs/globalConfig";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const rubik = Rubik({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700", "800", "900"],
});

type Props = {
  children: ReactNode;
  params: Promise<{ locale: string }>;
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout(props: Props) {
  const params = await props.params;

  const {
    locale
  } = params;

  const {
    children
  } = props;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  // Enable static rendering
  setRequestLocale(locale);

  const commonText = await getCommonText();
  const authText = await getAuthText();
  const menuText = await getMenuText();
  const pricingText = await getPricingText();
  const footerLink = await getFooterLink();

  // 获取网站配置的背景色和文字颜色,如果没有配置则使用默认值
  const backgroundColor = "#ffffff";
  const textColor = "#000000";
  const backgroundImage = "";

  return (
    <html lang={locale} dir={locale == "ar" ? "rtl" : "ltr"}>
      <head>
        {
          checkLogin && (
            <script src="https://accounts.google.com/gsi/client" async defer></script>
          )
        }
      </head>
      <body
        suppressHydrationWarning={true}
        className={`${inter.variable} ${rubik.className}`}
        style={{
          color: textColor,
          backgroundColor: backgroundColor,
          backgroundImage: `url(${backgroundImage})`,
        }}
      >
        <NextAuthProvider>
          <CommonProvider commonText={commonText} authText={authText} menuText={menuText} pricingText={pricingText} footerLink={footerLink}>
            <div className="relative flex flex-col min-h-screen overflow-hidden supports-[overflow:clip]:overflow-clip">{children}</div>
          </CommonProvider>
        </NextAuthProvider>
      </body>
    </html>
  );
}
