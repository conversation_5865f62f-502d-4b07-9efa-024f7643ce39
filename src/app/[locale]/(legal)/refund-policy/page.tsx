import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';

import {
  getRefundPolicyText
} from "~/i18n/languageText";

export default async function RefundPolicyPage(props) {
  const params = await props.params;

  const {
    locale = ''
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  const refundPolicyText = await getRefundPolicyText();

  return (
    <PageComponent
      locale={locale}
      refundPolicyText={refundPolicyText}
    />
  )
}
