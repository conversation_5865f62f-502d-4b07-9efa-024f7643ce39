'use client'
import HeadInfo from "~/components/common/HeadInfo";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";
import Markdown from "react-markdown";
import { useEffect, useRef, useState } from "react";
import { useCommonContext } from "~/context/common-context";
import GoogleAdsense from "~/components/GoogleAdsense";

const PageComponent = ({
  locale,
  privacyPolicyText
}) => {

  const [pagePath] = useState("privacy-policy");
  const { setShowLoadingModal } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => {
    }
  }, []);

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeadInfo locale={locale} page={pagePath} title={privacyPolicyText.title} description={privacyPolicyText.description} />
      <Header locale={locale} page={pagePath} />

      <div className="pt-24 pb-12 md:pt-36 md:pb-20 my-auto min-h-[90vh]">
        <main className="w-[95%] md:w-[65%] lg:w-[55%] 2xl:w-[45%] mx-auto h-full my-8">
          <div className="p-6 mx-auto prose">
            <Markdown>
              {
                privacyPolicyText.detailText
              }
            </Markdown>
          </div>
        </main>
      </div>

      <Footer locale={locale} page={pagePath} />
      <GoogleAdsense />
    </>
  );
}

export default PageComponent
