import PageComponent from "./PageComponent";
import { getUserByServerSession } from "~/servers/common/user";

export const revalidate = 0;

export default async function LoginSuccessPage(props) {
  const params = await props.params;

  const {
    locale = ''
  } = params;

  const user_info = await getUserByServerSession();
  console.log(user_info);

  return (
    <PageComponent
      locale={locale}
      user_info={user_info}
    />
  )
}
