'use client'

import { useEffect, useState } from 'react';
import { templateComponents } from '~/configs/templatesConfig';
import { SiteData } from '~/types/site';

interface HomepageComponentProps {
  locale: string;
  homepageData: SiteData;
}

const HomepageComponent = ({ locale, homepageData }: HomepageComponentProps) => {
  const templateKey = homepageData.page.template_key;

  // 直接同步选择模板
  const TemplateComponent = templateComponents[templateKey] || templateComponents['default'];

  return <TemplateComponent locale={locale} pageData={homepageData} pagePath={""} />;
};

export default HomepageComponent; 