import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getHomepageData } from '~/servers/manage/homepage';
import HomepageComponent from './HomepageComponent';
import { domainNameLowercase } from '~/configs/globalConfig';

export const revalidate = 600;

export default async function Page(props) {
  const params = await props.params;

  const homepageData = await getHomepageData(domainNameLowercase, params.locale);

  if (!homepageData) return notFound();

  return (
    <HomepageComponent
      locale={params.locale}
      homepageData={homepageData}
    />
  );
}
