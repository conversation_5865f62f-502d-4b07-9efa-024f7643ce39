import { deleteBlog } from "~/servers/manage/manageBlog";
import { getUserByServerSession } from "~/servers/common/user";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 100;

export const revalidate = 0;

export async function POST(req: Request) {
  const { uid } = await req.json();
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      }),
      {
        headers: { "Content-Type": "application/json" },
        status: 403,
      }
    );
  }
  const result = await deleteBlog(uid);

  return Response.json(result);
}
