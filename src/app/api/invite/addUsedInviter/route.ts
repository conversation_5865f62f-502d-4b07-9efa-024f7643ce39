import { getUserByServerSession } from "~/servers/common/user";
import { checkInvitedAndAddTimesForInviter } from "~/servers/invite/inviteRecord";

export async function POST(req: Request) {
  const user = await getUserByServerSession();
  const userId = user?.user_id;
  const result = await checkInvitedAndAddTimesForInviter(userId);

  return Response.json({
    code: 200,
    message: "success",
  });
}
