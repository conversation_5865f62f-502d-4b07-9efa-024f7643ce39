import { getDb } from "~/libs/db/db";
import { getUserByServerSession } from "~/servers/common/user";

export const revalidate = 0;

const db = getDb();

export const GET = async (req: Request) => {
  const result = {
    user_id: "",
    available_times: 0,
    download_times: 0,
    subscribeStatus: "",
  };

  const user_info = await getUserByServerSession();
  if (!user_info.email) {
    return Response.json(result);
  }

  const user_id = user_info.user_id;
  if (user_id) {
    // 检查 stripe 订阅
    const resultsSubscribe = await db.query(
      `
      SELECT * FROM stripe_subscriptions where user_id=$1`,
      [user_id]
    );
    const originSubscribe = resultsSubscribe.rows;
    if (originSubscribe.length > 0) {
      if (originSubscribe[0].status == "active" || originSubscribe[0].status == "trialing") {
        result.subscribeStatus = "active";
      }
    }

    // 检查 paddle 订阅
    const { rows: paddleResult } = await db.query(
      `
      select * from paddle_user_payment_data where user_id=$1 order by created_at desc;
    `,
      [user_id]
    );
    if (paddleResult.length > 0) {
      const subscription_status = paddleResult[0].subscription_status;
      if (subscription_status == "active" || subscription_status == "trialing") {
        result.subscribeStatus = "active";
      }
    }

    const results = await db.query(
      `
      SELECT * FROM user_available where user_id=$1 order by created_at desc
      `,
      [user_id]
    );
    const origin = results.rows;

    if (origin.length !== 0) {
      result.available_times = origin[0].available_times;
      result.download_times = origin[0].download_times;
      if (result.subscribeStatus == "" && (origin[0].stripe_customer_id || origin[0].paddle_customer_id)) {
        result.subscribeStatus = "active";
      }
    }
  }
  return Response.json(result);
};
