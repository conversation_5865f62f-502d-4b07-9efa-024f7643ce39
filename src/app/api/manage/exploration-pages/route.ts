import { NextRequest } from 'next/server';
import { getUserByServerSession } from '~/servers/common/user';
import { checkAdminUser } from "~/utils/checkWhiteUser";
import {
  getExplorationPagesBySiteUid,
  createExplorationPage,
  updateExplorationPage,
  deleteExplorationPage
} from '~/servers/manage/explorationPages';

export async function GET(request: NextRequest) {
  const user = await getUserByServerSession();
  if (!checkAdminUser(user)) {
    return Response.json({ code: 403, message: 'Unauthorized' });
  }

  const { searchParams } = new URL(request.url);
  const siteUid = searchParams.get('site_uid');

  if (!siteUid) {
    return Response.json({ code: 400, message: 'Site UID is required' });
  }

  try {
    const explorationPages = await getExplorationPagesBySiteUid(siteUid);
    return Response.json({ code: 200, data: explorationPages });
  } catch (error) {
    console.error('Error fetching exploration pages:', error);
    return Response.json({ code: 500, message: 'Internal server error' });
  }
}

export async function POST(request: NextRequest) {
  const user = await getUserByServerSession();
  if (!checkAdminUser(user)) {
    return Response.json({ code: 403, message: 'Unauthorized' });
  }

  try {
    const body = await request.json();
    const { action, ...data } = body;

    switch (action) {
      case 'create':
        const newPage = await createExplorationPage(data);
        return Response.json({ code: 200, message: 'Exploration page created successfully', data: newPage });

      case 'update':
        await updateExplorationPage(data.uid, data);
        return Response.json({ code: 200, message: 'Exploration page updated successfully' });

      case 'delete':
        await deleteExplorationPage(data.uid);
        return Response.json({ code: 200, message: 'Exploration page deleted successfully' });

      default:
        return Response.json({ code: 400, message: 'Invalid action' });
    }
  } catch (error) {
    console.error('Error processing exploration page request:', error);
    return Response.json({ code: 500, message: 'Internal server error' });
  }
}