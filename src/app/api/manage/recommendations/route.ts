import { NextResponse } from 'next/server';
import { listRecommendations, createRecommendation, updateRecommendation, getRecommendation } from '~/servers/manage/recommendations'; // Import the service function and new service functions
import { NextRequest } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const siteId = searchParams.get('siteId');
  const zone = searchParams.get('zone');
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

  // *** Remove mock data and filtering ***
  // const allItems = Array.from({ length: 25 }).map((_, i) => ({...}));
  // const filtered = zone && zone !== 'all' ? allItems.filter(...) : allItems;
  // const start = (page - 1) * pageSize; ...

  // *** Call the service function to get real data ***
  if (!siteId) {
    return NextResponse.json({ success: false, error: 'siteId is required' }, { status: 400 });
  }

  try {
    const { items, total } = await listRecommendations(siteId, zone === 'all' ? undefined : zone, page, pageSize);

    return NextResponse.json({
      success: true,
      data: {
        items,
        total,
      },
    });
  } catch (error: any) {
    console.error('API Error fetching recommendations:', error);
    return NextResponse.json({ success: false, error: error.message || 'An error occurred' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, siteUid, zone, id, title, description, jump_url, sort_order, cover_img_url } = body;

    if (!action) {
      return NextResponse.json({ status: 400, message: 'Action is required' }, { status: 400 });
    }

    switch (action) {
      case 'create':
        if (!siteUid || !zone || !title || !jump_url || !cover_img_url) {
          return NextResponse.json({ status: 400, message: 'Missing required fields for create' }, { status: 400 });
        }
        
        const newRecommendation = await createRecommendation({
          siteUid,
          zone,
          title,
          description,
          jump_url,
          sort_order,
          cover_img_url
        });
        return NextResponse.json({ status: 200, message: 'Recommendation created successfully', data: newRecommendation });

      case 'update':
        if (!id || !siteUid) {
          return NextResponse.json({ status: 400, message: 'id and siteUid are required for update' }, { status: 400 });
        }
        // Collect update data, exclude undefined values
        const updateData: any = {};
        if (zone !== undefined) updateData.zone = zone;
        if (title !== undefined) updateData.title = title;
        if (description !== undefined) updateData.description = description;
        if (jump_url !== undefined) updateData.jump_url = jump_url;
        if (sort_order !== undefined) updateData.sort_order = sort_order;
        if (cover_img_url !== undefined) updateData.cover_img_url = cover_img_url;

        if (Object.keys(updateData).length === 0) {
          return NextResponse.json({ status: 400, message: 'No update fields provided' }, { status: 400 });
        }

        const updatedRecommendation = await updateRecommendation(id, updateData);
        if (!updatedRecommendation) {
          return NextResponse.json({ status: 404, message: `Recommendation with id ${id} not found` }, { status: 404 });
        }
        return NextResponse.json({ status: 200, message: 'Recommendation updated successfully', data: updatedRecommendation });

      case 'get': // Add case for get action
        if (!id) {
          return NextResponse.json({ status: 400, message: 'ID is required for get action' }, { status: 400 });
        }
        const item = await getRecommendation(id); // Call the service function
        if (!item) {
          return NextResponse.json({ status: 404, message: 'Recommendation not found' }, { status: 404 });
        }
        return NextResponse.json({ status: 200, data: item });

      case 'delete': // Assuming delete action might be added later here
          return NextResponse.json({ status: 501, message: 'Delete action not yet implemented' });

      case 'sort': // Already handled in zones/route.ts, keep separate or consolidate?
          // Keeping zones sort in zones/route.ts for now.
           return NextResponse.json({ status: 400, message: 'Invalid action for this endpoint' }, { status: 400 });

      default:
        return NextResponse.json({ status: 400, message: 'Invalid action' }, { status: 400 });
    }
  } catch (error: any) {
    console.error('API Error processing recommendation POST:', error);
    return NextResponse.json({ status: 500, message: error.message || 'An error occurred' }, { status: 500 });
  }
} 