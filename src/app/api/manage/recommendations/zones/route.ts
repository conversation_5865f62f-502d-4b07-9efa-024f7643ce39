import { NextRequest, NextResponse } from "next/server";
import { getRecommendationZonesBySiteUid, updateRecommendationZonesBySiteUid } from "~/servers/manage/recommendations";
// Added a comment to trigger re-evaluation

export async function POST(req: Request) {
  try {
    const { action, siteUid, zones } = await req.json();
    switch (action) {
      case 'get':
        const data = await getRecommendationZonesBySiteUid(siteUid);
        return NextResponse.json({ status: 200, data });
      case 'update':
        const updated = await updateRecommendationZonesBySiteUid(siteUid, zones);
        return NextResponse.json({ status: 200, data: updated });
      case 'sort':
        if (!zones || !Array.isArray(zones)) {
          return NextResponse.json({ status: 400, message: 'zones array is required for sort' });
        }
        await updateRecommendationZonesBySiteUid(siteUid, zones);
        return NextResponse.json({ status: 200, message: 'Zones sorted successfully' });
      default:
        return NextResponse.json({ status: 400, message: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({ status: 500, message: "Internal server error" }, { status: 500 });
  }
} 