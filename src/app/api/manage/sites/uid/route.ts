import { NextRequest, NextResponse } from "next/server";
import { saveSiteByUid, getSiteByUid, getSites, deleteSiteByUid } from "~/servers/manage/manageSites";
import { createSite } from "~/servers/game/siteService";
import { getUserByServerSession } from "~/servers/common/user";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 100;
export const revalidate = 0;

export async function GET(request: NextRequest) {
  try {
    // 解析URL参数
    const searchParams = request.nextUrl.searchParams;
    const uid = searchParams.get('uid');
    
    // 如果提供了uid，则获取特定站点
    if (uid) {
      const result = await getSiteByUid(uid);
      return NextResponse.json({
        code: result.code,
        data: result.data,
        message: result.message,
        success: result.code === 200
      });
    }
    
    // 否则返回站点列表（带分页）
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const skipSize = parseInt(searchParams.get('skipSize') || '0');
    
    const result = await getSites({ pageSize, skipSize });
    
    return NextResponse.json({
      code: result.code,
      data: result.data,
      message: result.message,
      success: result.code === 200
    });
  } catch (error) {
    console.error("Error in sites API route:", error);
    return NextResponse.json({
      code: 500,
      message: "Internal server error",
      success: false
    }, { status: 500 });
  }
}

/**
 * POST - Create a new site
 */
export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const { action, data } = await req.json();
    
    switch (action) {
      case 'list':
        const { page = 1, pageSize = 10 } = data || {};
        const skipSize = (page - 1) * pageSize;
        const result = await getSites({ pageSize, skipSize });
        return Response.json({
          status: 200,
          message: "Success",
          data: result,
        });
        
      default:
        return Response.json({
          status: 400,
          message: "Invalid action",
        });
    }
  } catch (error) {
    console.error(`Error in sites API route:`, error);
    return Response.json({
      status: 500,
      message: "An error occurred while processing the request",
      error: error.message
    });
  }
}

/**
 * DELETE - Delete a site by UID
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get UID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const uid = searchParams.get('uid');
    
    if (!uid) {
      return NextResponse.json(
        { message: "UID is required", code: 400 },
        { status: 400 }
      );
    }

    // Delete the site by UID
    const result = await deleteSiteByUid(uid);

    return NextResponse.json({
      code: result.code,
      message: result.message,
      success: result.code === 200
    }, { status: result.code });
  } catch (error) {
    console.error("Error deleting site:", error);
    return NextResponse.json(
      { message: "Internal server error", code: 500, success: false },
      { status: 500 }
    );
  }
} 