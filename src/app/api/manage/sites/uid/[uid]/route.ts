import { getUserByServerSession } from "~/servers/common/user";
import { getSiteByUid, updateSiteByUid, deleteSiteByUid } from "~/servers/game/siteService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 100;
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const { uid, action, data } = await req.json();
    
    switch (action) {
      case 'get':
        const site = await getSiteByUid(uid);
        if (!site) {
          return Response.json({
            status: 404,
            message: "Site not found",
          });
        }
        return Response.json({
          status: 200,
          message: "Success",
          data: site,
        });
        
      case 'update':
        const updatedSite = await updateSiteByUid(uid, data);
        if (!updatedSite) {
          return Response.json({
            status: 404,
            message: "Site not found",
          });
        }
        return Response.json({
          status: 200,
          message: "Site updated successfully",
          data: updatedSite,
        });
        
      case 'delete':
        const success = await deleteSiteByUid(uid);
        if (!success) {
          return Response.json({
            status: 404,
            message: "Site not found or could not be deleted",
          });
        }
        return Response.json({
          status: 200,
          message: "Site deleted successfully",
        });
        
      default:
        return Response.json({
          status: 400,
          message: "Invalid action",
        });
    }
  } catch (error) {
    console.error(`Error in sites API route:`, error);
    return Response.json({
      status: 500,
      message: "An error occurred while processing the request",
      error: error.message
    });
  }
} 