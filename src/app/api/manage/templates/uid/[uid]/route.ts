import { getUserByServerSession } from "~/servers/common/user";
import { 
  getTemplateByUid, 
  updateTemplateByUid,
  deleteTemplate 
} from "~/servers/game/templateService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 100;
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const { uid, action, data } = await req.json();
    
    switch (action) {
      case 'get':
        const template = await getTemplateByUid(uid);
        if (!template) {
          return Response.json({
            status: 200,
            message: "Template not found",
            data: null
          });
        }
        return Response.json({
          status: 200,
          message: "Success",
          data: template,
        });
        
      case 'update':
        if (!data.name || !data.content) {
          return Response.json({
            status: 400,
            message: "Name and content are required",
          });
        }
        
        const updatedTemplate = await updateTemplateByUid(uid, {
          name: data.name,
          content: data.content,
          preview_url: data.preview_url || null,
        });
        
        if (!updatedTemplate) {
          return Response.json({
            status: 200,
            message: "Template not found",
            data: null
          });
        }
        
        return Response.json({
          status: 200,
          message: "Template updated successfully",
          data: updatedTemplate,
        });
        
      case 'delete':
        const success = await deleteTemplate(uid);
        
        if (!success) {
          return Response.json({
            status: 200,
            message: "Template not found or could not be deleted",
            data: null
          });
        }
        
        return Response.json({
          status: 200,
          message: "Template deleted successfully",
        });
        
      default:
        return Response.json({
          status: 400,
          message: "Invalid action",
        });
    }
  } catch (error) {
    console.error(`Error in templates API route:`, error);
    return Response.json({
      status: 500,
      message: "An error occurred while processing the request",
      error: error.message
    });
  }
} 