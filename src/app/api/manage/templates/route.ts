import { NextRequest, NextResponse } from "next/server";
import { getAllTemplates, createTemplate } from "~/servers/game/templateService";

export async function GET(request: NextRequest) {
  try {
    const templates = await getAllTemplates();
    return NextResponse.json({
      code: 200,
      message: "Success",
      data: templates
    });
  } catch (error) {
    console.error("Error in GET /api/manage/templates:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.name || !body.content) {
      return NextResponse.json(
        { code: 400, message: "Name and content are required" },
        { status: 400 }
      );
    }
    
    const template = await createTemplate({
      name: body.name,
      content: body.content,
      preview_url: body.preview_url
    });
    
    return NextResponse.json({
      code: 200,
      message: "Template created successfully",
      data: template
    });
  } catch (error) {
    console.error("Error in POST /api/manage/templates:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
} 