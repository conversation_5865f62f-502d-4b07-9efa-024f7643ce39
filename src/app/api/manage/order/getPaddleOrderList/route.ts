import { getUserByServerSession } from "~/servers/common/user";
import { getPaddleOrderList } from "~/servers/manage/manageOrder";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { NextResponse } from "next/server";

export const maxDuration = 300;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return NextResponse.json({ resultList: [], totalPage: 0, countTotal: 0 }, { status: 403 });
  }

  const result = await getPaddleOrderList(json);

  return NextResponse.json(result, { status: 200 });
}
