import { NextRequest, NextResponse } from "next/server";
import { saveGame, getGames, deleteGame, getGameByUid } from "~/servers/manage/manageGames";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const uid = searchParams.get("uid");
    
    if (uid) {
      // Get a specific game by UID
      const result = await getGameByUid(uid);
      return NextResponse.json(result);
    } else {
      // Get all games with pagination
      const pageSize = searchParams.get("pageSize") ? Number(searchParams.get("pageSize")) : 10;
      const page = searchParams.get("page") ? Number(searchParams.get("page")) : 1;
      const skipSize = (page - 1) * pageSize;
      
      const result = await getGames({ pageSize, skipSize });
      return NextResponse.json(result);
    }
  } catch (error) {
    console.error("Error in GET /api/manage/games:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = await saveGame(body);
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in POST /api/manage/games:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    
    if (!id) {
      return NextResponse.json(
        { code: 400, message: "Missing game id" },
        { status: 400 }
      );
    }
    
    const result = await deleteGame(Number(id));
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in DELETE /api/manage/games:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
}

// For updating existing games
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { code: 400, message: "Missing game id" },
        { status: 400 }
      );
    }
    
    const result = await saveGame(body);
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in PUT /api/manage/games:", error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
} 