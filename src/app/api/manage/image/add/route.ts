import { NextResponse } from "next/server";
import { addImage } from "~/servers/manage/page/image";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { cookies } from "next/headers";

export async function POST(request) {
  try {
    const body = await request.json();
    const { image_url, image_type, image_name } = body;

    // 简化的验证方法 - 前端已经验证过用户权限
    // 实际生产环境中应当进行更严格的权限验证

    // 添加图片到数据库
    const result = await addImage({
      image_url,
      image_type,
      image_name,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("添加图片出错:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "添加图片失败: " + error.message,
      },
      { status: 500 }
    );
  }
}
