import { NextResponse } from "next/server";
import { updateImage } from "~/servers/manage/page/image";

export async function POST(request) {
  try {
    const body = await request.json();
    const { id, image_name, image_type } = body;

    // 更新图片信息
    const result = await updateImage({
      id,
      image_name,
      image_type,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("更新图片出错:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "更新图片失败: " + error.message,
      },
      { status: 500 }
    );
  }
}
