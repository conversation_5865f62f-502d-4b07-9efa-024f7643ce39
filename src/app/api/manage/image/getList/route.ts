import { NextResponse } from "next/server";
import { getImageList } from "~/servers/manage/page/image";

export async function POST(request) {
  try {
    const body = await request.json();
    const { image_type, page, pageSize, searchTerm } = body;

    // 获取图片列表
    const result = await getImageList({
      image_type,
      page,
      pageSize,
      searchTerm,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("获取图片列表出错:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "获取图片列表失败: " + error.message,
      },
      { status: 500 }
    );
  }
}
