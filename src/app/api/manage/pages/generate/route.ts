import { getUserByServerSession } from "~/servers/common/user";
import { getGameByUid } from "~/servers/game/gameService";
import { getDb } from "~/libs/db/db";
import { generateNewUID } from "~/utils/uidUtil";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { getModelParams } from "~/configs/llmModelsConfig";
import { getPromptWithKeyword } from "~/configs/promptsConfig";
import { apiKey, baseUrl } from "~/configs/openaiConfig";
import { revalidatePath } from "next/cache";

export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      //console.warn('API: Permission denied for user.'); // Log permission denied
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const json = await req.json();
    const { gameUid, sites, promptKey, llmModel, gameInfo } = json;

    // Validate required parameters
    if (!gameUid) {
      //console.warn('API: Validation failed - Game UID is required.'); // Log validation failure
      return Response.json({
        status: 400,
        message: "Game UID is required",
      });
    }

    if (!sites || !Array.isArray(sites) || sites.length === 0) {
      //console.warn('API: Validation failed - At least one site is required.'); // Log validation failure
      return Response.json({
        status: 400,
        message: "At least one site is required",
      });
    }

    // Get game information
    const game = await getGameByUid(gameUid);
    if (!game) {
      return Response.json({
        status: 404,
        message: "Game not found",
      });
    }
    if (!game.slug) {
      return Response.json({
        status: 400,
        message: "Game slug is required for page generation",
      });
    }

    // Get sites information
    const db = getDb();
    const siteUids = sites.map(site => site.siteUid);
    const { rows: siteRows } = await db.query(
      `SELECT * FROM sites WHERE uid = ANY($1)`,
      [siteUids]
    );

    if (siteRows.length === 0) {
      //console.warn('API: No valid sites found in DB for provided UIDs.'); // Log no valid sites
      return Response.json({
        status: 404,
        message: "No valid sites found",
      });
    }

    // Get model parameters
    const modelParams = getModelParams(llmModel || "gpt-4");
    if (!modelParams) {
      //console.warn(`API: Invalid LLM model specified: ${llmModel}`); // Log invalid model
      return Response.json({
        status: 400,
        message: "Invalid LLM model specified",
      });
    }

    // Get prompt template
    const promptTemplate = getPromptWithKeyword(promptKey || "seo_landing_page_v1", game.keyword);
    if (!promptTemplate) {
      //console.warn(`API: Invalid prompt key specified: ${promptKey}`); // Log invalid prompt
      return Response.json({
        status: 400,
        message: "Invalid prompt key specified",
      });
    }

    // For each site, generate a page
    const generationResults = await Promise.allSettled(
      sites.map(async (siteInfo) => {
        const site = siteRows.find(s => s.uid === siteInfo.siteUid);
        try {
          if (!site) {
            //console.error(`API: Site not found within siteRows for UID: ${siteInfo.siteUid}`); // Log site not found in loop
            throw new Error(`Site not found: ${siteInfo.siteUid}`);
          }

          // 直接用 game.slug 作为页面 slug
          const slug = game.slug;

          // --- START: Add duplicate page check ---
          const existingPage = await db.query(
            `SELECT uid FROM pages WHERE site_uid = $1 AND slug = $2`,
            [site.uid, slug]
          );

          if (existingPage.rows.length > 0) {
            //console.warn(`API: Duplicate page found for site ${site.name} (UID: ${site.uid}) with slug '${slug}'. Skipping generation.`); // Log duplicate found
            // If an existing page with the same site_uid and slug is found (regardless of status), return as failed
            return {
              siteUid: site.uid,
              siteName: site.name,
              status: "error",
              error: `该站点已存在相同slug的页面: ${slug}` // Updated error message
            };
          }
          // --- END: Add duplicate page check ---

          // Generate content using LLM
          const modelToUse = llmModel || site.default_llm_model || "gpt-4";
          const promptToUse = promptKey || site.default_llm_prompt_key || "seo_landing_page_v1";
          
          // Get prompt with game keyword
          const finalPrompt = getPromptWithKeyword(promptToUse, game.keyword);
          
          // Reference data from game if available
          const referencePrompt = game.reference_data ? 
            `\n\nReference information about the game:\n${game.reference_data}` : '';
          
          // Combine prompt with reference data
          const fullPrompt = finalPrompt + referencePrompt;
          
          // Call OpenAI API directly
          const openaiResponse = await fetch(`${baseUrl}/v1/chat/completions`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${apiKey}`
            },
            body: JSON.stringify({
              model: modelToUse,
              messages: [
                {
                  role: "system",
                  content: "You are a professional content creator for gaming websites. You create SEO-optimized content in JSON format."
                },
                {
                  role: "user",
                  content: fullPrompt
                }
              ],
              temperature: 0.7,
              response_format: { type: "json_object" }
            })
          });
          
          if (!openaiResponse.ok) {
            const errorData = await openaiResponse.json();
            //console.error(`API: OpenAI API error for site ${site.name}:`, errorData.error?.message || 'Unknown error'); // Log OpenAI error
            throw new Error(`OpenAI API error: ${errorData.error?.message || 'Unknown error'}`);
          }
          
          const openaiData = await openaiResponse.json();
          const generatedContent = openaiData.choices[0]?.message?.content || '';

          // Parse the generated content
          let parsedContent;
          try {
            // First try to parse as JSON
            parsedContent = JSON.parse(generatedContent);
          } catch (error) {
            // If not JSON, use as raw text
            parsedContent = {
              title: `${game.keyword} - ${site.name}`,
              content: generatedContent
            };
            //console.warn('API: Generated content is not valid JSON, using as raw text.'); // Log JSON parse failure
          }

          // Create a new page record
          const pageUid = generateNewUID();
          
          // Generate public URL - remove trailing slash from domain
          const cleanDomain = siteInfo.domain.replace(/\/+$/, ''); // Remove trailing slashes
          const publicUrl = `${cleanDomain}/game/${slug}`;
          
          const { rows } = await db.query(
            `INSERT INTO pages (
              uid, 
              game_uid, 
              site_uid,
              domain,
              template_key,
              title,
              slug,
              status,
              json_content,
              json_schema_version,
              llm_model_used,
              llm_prompt_key_used,
              iframe_url,
              public_url,
              created_at,
              updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
            RETURNING *`,
            [
              pageUid,
              gameUid,
              site.uid,
              siteInfo.domain,
              site.default_template_key || 'default',
              parsedContent.title || `${game.keyword} - ${site.name}`,
              slug,
              0, // Draft status
              JSON.stringify(parsedContent),
              "1.0", // JSON schema version
              modelToUse,
              promptToUse,
              gameInfo?.iframe_url || null,
              publicUrl
            ]
          );

          return {
            siteUid: site.uid,
            siteName: site.name,
            pageUid: pageUid,
            status: "success",
            page: rows[0]
          };
        } catch (error) {
          //console.error(`API: Error generating page for site ${site?.name || siteInfo.siteUid}:`, error); // Log site-specific error
          return {
            siteUid: siteInfo.siteUid,
            siteName: siteRows.find(s => s.uid === siteInfo.siteUid)?.name || 'Unknown',
            status: "error",
            error: error.message || "Unknown error occurred"
          };
        }
      })
    );

    // Process results
    const successResults = [];
    const failedResults = [];

    generationResults.forEach((result) => {
      if (result.status === "fulfilled") {
        if (result.value.status === "success") {
          successResults.push(result.value);
        } else {
          failedResults.push(result.value);
        }
      } else {
        failedResults.push({
          status: "error",
          error: result.reason?.message || "Unknown error"
        });
      }
    });

    // === 新增: revalidatePath 逻辑 ===
    for (const item of successResults) {
      // 兼容slug和public_url
      const slug = item.page?.slug;
      if (slug) {
        revalidatePath("/en");
        revalidatePath(`/en/game/${slug}`);
      }
    }

    return Response.json({
      status: 200,
      message: `Generated ${successResults.length} pages successfully, ${failedResults.length} failed`,
      data: {
        success: successResults,
        failed: failedResults,
        game: {
          uid: game.uid,
          keyword: game.keyword
        }
      }
    });
  } catch (error) {
    //console.error("API: Top-level error generating pages:", error); // Log top-level error
    return Response.json({
      status: 500,
      message: "An error occurred while generating pages",
      error: error.message
    });
  }
} 