import { NextRequest } from "next/server";
import { getUserByServerSession } from "~/servers/common/user";
import { getDb } from "~/libs/db/db";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

// GET: Count pages with optional filtering
export async function GET(req: NextRequest) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const gameUid = searchParams.get("gameUid") || undefined;
    const siteUid = searchParams.get("siteUid") || undefined;
    const status = searchParams.get("status") ? parseInt(searchParams.get("status")!) : undefined;
    
    // Build query
    const db = getDb();
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;
    
    // Add filters
    if (gameUid) {
      whereConditions.push(`game_uid = $${paramIndex++}`);
      queryParams.push(gameUid);
    }
    
    if (siteUid) {
      whereConditions.push(`site_uid = $${paramIndex++}`);
      queryParams.push(siteUid);
    }
    
    if (status !== undefined) {
      whereConditions.push(`status = $${paramIndex++}`);
      queryParams.push(status);
    }
    
    // Construct WHERE clause
    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}` 
      : '';
    
    // Execute count query
    const query = `
      SELECT COUNT(*) as count, 
             COUNT(CASE WHEN status = 0 THEN 1 END) as draft_count,
             COUNT(CASE WHEN status = 1 THEN 1 END) as published_count
      FROM pages
      ${whereClause}
    `;
    
    const { rows } = await db.query(query, queryParams);
    
    return Response.json({
      status: 200,
      message: "Success",
      data: {
        total: parseInt(rows[0].count),
        draft: parseInt(rows[0].draft_count),
        published: parseInt(rows[0].published_count)
      }
    });
  } catch (error) {
    console.error("Error counting pages:", error);
    return Response.json({
      status: 500,
      message: "An error occurred while counting pages"
    });
  }
}

// POST: Count pages with filter data in request body
export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const json = await req.json();
    const gameUid = json.gameUid;
    const siteUid = json.siteUid;
    const status = json.status;
    
    // Build query
    const db = getDb();
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;
    
    // Add filters
    if (gameUid) {
      whereConditions.push(`game_uid = $${paramIndex++}`);
      queryParams.push(gameUid);
    }
    
    if (siteUid) {
      whereConditions.push(`site_uid = $${paramIndex++}`);
      queryParams.push(siteUid);
    }
    
    if (status !== undefined) {
      whereConditions.push(`status = $${paramIndex++}`);
      queryParams.push(status);
    }
    
    // Construct WHERE clause
    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}` 
      : '';
    
    // Execute count query
    const query = `
      SELECT COUNT(*) as count, 
             COUNT(CASE WHEN status = 0 THEN 1 END) as draft_count,
             COUNT(CASE WHEN status = 1 THEN 1 END) as published_count
      FROM pages
      ${whereClause}
    `;
    
    const { rows } = await db.query(query, queryParams);
    
    return Response.json({
      status: 200,
      message: "Success",
      data: {
        total: parseInt(rows[0].count),
        draft: parseInt(rows[0].draft_count),
        published: parseInt(rows[0].published_count)
      }
    });
  } catch (error) {
    console.error("Error counting pages:", error);
    return Response.json({
      status: 500,
      message: "An error occurred while counting pages"
    });
  }
} 