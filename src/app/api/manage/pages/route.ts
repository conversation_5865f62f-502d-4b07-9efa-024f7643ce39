import { NextRequest } from "next/server";
import { getUserByServerSession } from "~/servers/common/user";
import { getPages } from "~/servers/game/pageService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

// GET: List all pages with optional filtering
export async function GET(req: NextRequest) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    // Parse query parameters
    const searchParams = req.nextUrl.searchParams;
    const pageSize = searchParams.get("pageSize") ? parseInt(searchParams.get("pageSize")!) : 10;
    const page = searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1;
    const skipSize = (page - 1) * pageSize;
    const gameUid = searchParams.get("gameUid") || undefined;
    const siteUid = searchParams.get("siteUid") || undefined;
    const status = searchParams.get("status") ? parseInt(searchParams.get("status")!) : undefined;
    const keyword = searchParams.get("keyword") || undefined;

    const result = await getPages({
      pageSize,
      skipSize,
      gameUid,
      siteUid,
      status,
      keyword
    });

    return Response.json({
      status: 200,
      message: "Success",
      ...result
    });
  } catch (error) {
    console.error("Error fetching pages:", error);
    return Response.json({
      status: 500,
      message: "An error occurred while fetching pages"
    });
  }
}

// POST: List pages with filter data in request body
export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const json = await req.json();
    const pageSize = json.pageSize || 10;
    const page = json.page || 1;
    const skipSize = (page - 1) * pageSize;

    const result = await getPages({
      pageSize,
      skipSize,
      gameUid: json.gameUid,
      siteUid: json.siteUid,
      status: json.status,
      keyword: json.keyword
    });

    return Response.json({
      status: 200,
      message: "Success",
      ...result
    });
  } catch (error) {
    console.error("Error fetching pages:", error);
    return Response.json({
      status: 500,
      message: "An error occurred while fetching pages"
    });
  }
} 