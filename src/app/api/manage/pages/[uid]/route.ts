import { getUserByServerSession } from "~/servers/common/user";
import { getPageByUid, updatePage, deletePage } from "~/servers/game/pageService";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { updatePageByUid } from "~/servers/manage/managePages";
import { revalidatePath } from "next/cache";

export const maxDuration = 100;
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const { uid, action, data } = await req.json();

    switch (action) {
      case "get":
        const page = await getPageByUid(uid);
        if (!page) {
          return Response.json({
            status: 404,
            message: "Page not found",
          });
        }
        return Response.json({
          status: 200,
          message: "Success",
          data: page,
        });

      case "update":
        const updatedPage = await updatePage(uid, {
          title: data.title,
          slug: data.slug,
          json_content: data.json_content,
          status: data.status,
          iframe_url: data.iframe_url,
          public_url: data.public_url,
        });

        if (!updatedPage) {
          return Response.json({
            status: 404,
            message: "Page not found",
          });
        }

        revalidatePath("/en");
        revalidatePath(`/en/game/${data.slug}`);

        return Response.json({
          status: 200,
          message: "Page updated successfully",
          data: updatedPage,
        });

      case "patch":
        const updateData: any = {};

        if (data.hasOwnProperty("title")) updateData.title = data.title;
        if (data.hasOwnProperty("slug")) updateData.slug = data.slug;
        if (data.hasOwnProperty("json_content")) updateData.json_content = data.json_content;
        if (data.hasOwnProperty("status")) updateData.status = data.status;
        if (data.hasOwnProperty("iframe_url")) updateData.iframe_url = data.iframe_url;
        if (data.hasOwnProperty("public_url")) updateData.public_url = data.public_url;
        if (data.hasOwnProperty("is_homepage")) updateData.is_homepage = data.is_homepage;
        if (data.hasOwnProperty("template_key")) updateData.template_key = data.template_key;

        const result = await updatePageByUid(uid, updateData);

        return Response.json({
          status: result.code,
          message: result.message,
          data: result.data,
        });

      case "delete":
        const success = await deletePage(uid);

        if (!success) {
          return Response.json({
            status: 404,
            message: "Page not found or could not be deleted",
          });
        }

        return Response.json({
          status: 200,
          message: "Page deleted successfully",
        });

      default:
        return Response.json({
          status: 400,
          message: "Invalid action",
        });
    }
  } catch (error) {
    console.error(`Error in pages API route:`, error);
    return Response.json({
      status: 500,
      message: "An error occurred while processing the request",
      error: error.message,
    });
  }
}
