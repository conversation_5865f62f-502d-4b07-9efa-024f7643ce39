import { getUserByServerSession } from "~/servers/common/user";
import { getUserList } from "~/servers/manage/manageUser";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 300;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      }),
      {
        headers: { "Content-Type": "application/json" },
        status: 403,
      }
    );
  }

  const result = await getUserList(json);

  return new Response(JSON.stringify(result), {
    headers: { "Content-Type": "application/json" },
    status: 200,
  });
}
