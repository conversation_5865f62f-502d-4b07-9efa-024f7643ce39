import { getUserByServerSession } from "~/servers/common/user";
import { deleteUserByUserId } from "~/servers/manage/manageUser";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 300;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(JSON.stringify({ msg: "" }), {
      headers: { "Content-Type": "application/json" },
      status: 403,
    });
  }

  const result = await deleteUserByUserId(json);

  return new Response(JSON.stringify(result), {
    headers: { "Content-Type": "application/json" },
    status: 200,
  });
}
