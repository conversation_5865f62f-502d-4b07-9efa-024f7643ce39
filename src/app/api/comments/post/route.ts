import { NextRequest } from "next/server";
import { getUserByServerSession } from "~/servers/common/user";
import { validateCommentData } from "~/servers/comments/commentValidation";
import { createComment } from "~/servers/comments/commentService";

export const revalidate = 0;

/**
 * 发表评论API
 * POST /api/comments/post
 */
export async function POST(request: NextRequest) {
  try {

    // 1. 用户认证检查
    let user_info = await getUserByServerSession();

    // 用户未登录
    if (!user_info.user_id) {
      return Response.json({
        success: false,
        error: "Please login to post a comment"
      }, { status: 401 });
    }

    // 2. 获取请求数据
    const requestData = await request.json();
    const { page_uid, parent_comment_uid, content } = requestData;

    // 3. 构建验证数据
    const validationData = {
      content,
      user_id: user_info.user_id,
      page_uid,
      parent_comment_uid
    };

    // 4. 综合验证（包含内容验证、频率限制等）
    const validation = await validateCommentData(validationData);
    
    if (!validation.isValid) {
      return Response.json({
        success: false,
        errors: validation.errors
      }, { status: 400 });
    }

    // 5. 创建评论
    const result = await createComment({
      page_uid,
      parent_comment_uid: parent_comment_uid || undefined,
      user_id: user_info.user_id,
      content: validation.sanitizedContent!, // 使用清理后的内容
      user_name: user_info.name || '',
      user_image: user_info.image || ''
    });

    if (!result.success) {
      return Response.json({
        success: false,
        error: result.error
      }, { status: 500 });
    }

    // 6. 返回成功结果
    return Response.json({
      success: true,
      data: {
        comment: result.data,
        message: "Comment posted successfully"
      }
    });

  } catch (error) {
    console.error("Post comment API error:", error);
    return Response.json({
      success: false,
      error: "Internal server error"
    }, { status: 500 });
  }
}
