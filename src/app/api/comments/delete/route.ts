import { NextRequest } from "next/server";
import { getUserByServerSession } from "~/servers/common/user";
import { deleteComment } from "~/servers/comments/commentService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

/**
 * 删除评论API
 * POST /api/comments/delete
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 用户认证检查
    let user_info = await getUserByServerSession();

    // 用户未登录
    if (!user_info.user_id) {
      return Response.json({
        success: false,
        error: "Please login first"
      }, { status: 401 });
    }

    // 2. 获取请求数据
    const requestData = await request.json();
    const { comment_uid } = requestData;

    // 3. 验证必需参数
    if (!comment_uid || typeof comment_uid !== 'string') {
      return Response.json({
        success: false,
        error: "Comment ID cannot be empty"
      }, { status: 400 });
    }

    // 4. 检查是否为管理员
    const is_admin = checkAdminUser(user_info);

    // 5. 调用服务层删除评论
    const result = await deleteComment(comment_uid, user_info.user_id, is_admin);

    if (!result.success) {
      // 根据错误类型返回不同状态码
      let statusCode = 500;
      if (result.error === 'Comment not found') {
        statusCode = 404;
      } else if (result.error === 'No permission to delete this comment') {
        statusCode = 403;
      }

      return Response.json({
        success: false,
        error: result.error
      }, { status: statusCode });
    }

    // 6. 返回成功结果
    return Response.json({
      success: true,
      message: result.message
    });

  } catch (error) {
    console.error("Delete comment API error:", error);
    return Response.json({
      success: false,
      error: "Internal server error"
    }, { status: 500 });
  }
}
