import { NextRequest } from "next/server";
import { getComments } from "~/servers/comments/commentService";

export const revalidate = 0;

/**
 * 获取评论列表API
 * GET /api/comments/get?page_uid=xxx&page=1&limit=10&parent_comment_uid=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 获取查询参数
    const page_uid = searchParams.get("page_uid");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const parent_comment_uid = searchParams.get("parent_comment_uid");

    // 验证必需参数
    if (!page_uid) {
      return Response.json({
        success: false,
        error: "Page ID cannot be empty"
      }, { status: 400 });
    }

    // 验证分页参数
    if (page < 1) {
      return Response.json({
        success: false,
        error: "Page number must be greater than 0"
      }, { status: 400 });
    }

    if (limit < 1 || limit > 100) {
      return Response.json({
        success: false,
        error: "Limit must be between 1 and 100"
      }, { status: 400 });
    }

    // 调用服务层获取评论
    const result = await getComments({
      page_uid,
      page,
      limit,
      parent_comment_uid: parent_comment_uid || null
    });

    if (!result.success) {
      return Response.json({
        success: false,
        error: result.error
      }, { status: 500 });
    }

    // 返回成功结果
    return Response.json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error("Get comments API error:", error);
    return Response.json({
      success: false,
      error: "Internal server error"
    }, { status: 500 });
  }
}
