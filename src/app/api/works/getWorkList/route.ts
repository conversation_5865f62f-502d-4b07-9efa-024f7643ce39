import { getWorkListByUserId } from "~/servers/works";
import { getUserByServerSession } from "~/servers/common/user";

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();

  if (!user_info.user_id) {
    return new Response(JSON.stringify({ msg: "Login to continue.", status: 601 }), {
      headers: { "Content-Type": "application/json" },
      status: 200,
    });
  }

  const user_id = user_info.user_id;
  const current_page = json.current_page;

  const works = await getWorkListByUserId(user_id, current_page);

  return new Response(JSON.stringify(works), {
    headers: { "Content-Type": "application/json" },
    status: 200,
  });
}
