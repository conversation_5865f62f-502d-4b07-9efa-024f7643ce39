import { getPriceByPayProPriceId } from "~/configs/pay/paypro";
import { getDb } from "~/libs/db/db";
import { payproOneTimeComplete } from "~/servers/pay/manageUserTimes";

const db = getDb();
export const POST = async (req: Request) => {
  const bodyText = await req.text();
  // 将 bodyText 按 & 分割成键值对数组
  const pairs = bodyText.split("&");

  // 创建一个空对象来存储结果
  const jsonData = {} as any;

  // 遍历键值对数组并解析到对象中
  pairs.forEach((pair) => {
    const [key, value] = pair.split("=");
    // 解码 URL 编码的值
    jsonData[key] = decodeURIComponent(value || "");
  });
  // console.log("Parsed JSON data:", jsonData);
  const ORDER_CUSTOM_FIELDS = (jsonData as any)?.ORDER_CUSTOM_FIELDS;
  // console.log("ORDER_CUSTOM_FIELDS-=-=->", ORDER_CUSTOM_FIELDS);
  let customFields = {} as any;
  try {
    // 将 ORDER_CUSTOM_FIELDS 按 , 分割成键值对数组
    const customFieldsPairs = ORDER_CUSTOM_FIELDS.split(",");
    customFields = {
      domain: "",
      userid: "",
    };
    // 遍历键值对数组并解析到对象中
    customFieldsPairs.forEach((pair) => {
      const [key, value] = pair.split("=");
      // key 移除前边的 x-
      const newKey = key.replace("x-", "");
      customFields[newKey] = decodeURIComponent(value || "");
    });
  } catch (error) {
    console.error("解析 ORDER_CUSTOM_FIELDS 失败:", error);
  }
  // console.log("customFields-=-=->", customFields);
  // 获取 domain
  const domain = customFields?.domain;
  const current_domain_name = process.env.NEXT_PUBLIC_DOMAIN_NAME;
  if (domain?.toLowerCase() !== current_domain_name?.toLowerCase()) {
    // 不是当前域名的 webhook 数据，不处理
    return Response.json(
      {
        message: "success",
      },
      {
        status: 200,
      }
    );
  }
  const user_id = customFields?.userid;
  const product_id = jsonData?.PRODUCT_ID;
  const order_id = jsonData?.ORDER_ID;
  const order_status = jsonData?.ORDER_STATUS;
  const customer_email = jsonData?.CUSTOMER_EMAIL;
  const customer_id = jsonData?.CUSTOMER_ID;
  const customer_country_code = jsonData?.CUSTOMER_COUNTRY_CODE;
  const customer_country_code_by_ip = jsonData?.CUSTOMER_COUNTRY_CODE_BY_IP;
  const product_price = getPriceByPayProPriceId(product_id).unit_amount;
  // 查询 event_id
  const { rows: eventRows } = await db.query("SELECT * FROM paypro_order_record WHERE order_id=$1", [order_id]);
  if (eventRows.length > 0) {
    // event_id 已存在，不处理
    console.log("event_id already exists");
    return Response.json({ message: "event_id already exists" }, { status: 200 });
  }

  // 存储事件数据
  await db.query(
    `
    INSERT INTO paypro_order_record (order_id, order_status, customer_id, user_id, customer_email, product_id, data_all, customer_country_code, customer_country_code_by_ip, product_price)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
  `,
    [order_id, order_status, customer_id, user_id, customer_email, product_id, jsonData, customer_country_code, customer_country_code_by_ip, product_price]
  );

  const completeData = {
    user_id,
    product_id,
    customer_id,
  };

  switch (order_status) {
    case "Processed":
      await payproOneTimeComplete(completeData);
      break;
  }

  return Response.json({ message: "success" });
};
