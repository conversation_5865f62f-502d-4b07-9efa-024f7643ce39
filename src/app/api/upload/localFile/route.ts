import { R2 } from "~/libs/r2/R2";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { generateNewLongUID } from "~/utils/uidUtil";
import { r2Bucket, storageUrl } from "~/configs/globalConfig";

export async function POST(req: Request) {
  const formData = await req.formData();
  const file = formData.get("file");

  if (!file) {
    return Response.json({ status: 603, msg: "No file uploaded." });
  }
  if (typeof file == "string") {
    return Response.json({ status: 603, msg: "Invalid file." });
  }
  const buffer = await file.arrayBuffer().then(Buffer.from);

  const fileExtension = getFileExtension(file.name);
  const strUUID = generateNewLongUID();
  const currImageKey = `image/${strUUID}.${fileExtension}`;
  if (fileExtension == "svg") {
    await R2.send(
      new PutObjectCommand({
        Bucket: r2Bucket,
        Key: currImageKey,
        Body: buffer,
        ContentType: "image/svg+xml",
      })
    );
  } else {
    // 判断 fileExtension 设置对应的 ContentType
    let contentType = "image/png";
    if (fileExtension == "jpg" || fileExtension == "jpeg") {
      contentType = "image/jpeg";
    } else if (fileExtension == "gif") {
      contentType = "image/gif";
    } else if (fileExtension == "webp") {
      contentType = "image/webp";
    }
    await R2.send(
      new PutObjectCommand({
        Bucket: r2Bucket,
        Key: currImageKey,
        Body: buffer,
        ContentType: contentType,
      })
    );
  }

  const storageImageUrl = `${storageUrl}/${currImageKey}`;
  console.log("storageImageUrl=-=-=-=-=-=-=->");
  console.log(storageImageUrl);
  return Response.json({ message: storageImageUrl, status: 1 });
}

function getFileExtension(filename) {
  return filename.split(".").pop();
}
