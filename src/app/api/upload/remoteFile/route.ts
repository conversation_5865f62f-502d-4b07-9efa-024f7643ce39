import { storageUrl } from "~/configs/globalConfig";
import { uploadFileToR2 } from "~/servers/upload/saveFileToR2";
import { getContentTypeByFileExtension } from "~/utils/fileType";
import { getFileExtension } from "~/utils/strUtil";
import { generateNewLongUID } from "~/utils/uidUtil";

// 调用上传远端的文件
export async function POST(req: Request) {
  const json = await req.json();
  const { url } = json;

  // 根据url获取文件类型
  const fileExtension = getFileExtension(url);
  const fileContentType = getContentTypeByFileExtension(fileExtension);

  const uid = generateNewLongUID();

  const fileKey = `file/${uid}.${fileExtension}`;

  const fileObj = {
    fileKey: fileKey,
    fileUrl: url,
    fileContentType: fileContentType,
  };

  // 文件存储的路径
  const storagePath = `${storageUrl}/${fileKey}`;

  // 上传文件到 R2
  await uploadFileToR2(uid, fileObj, true);

  return Response.json({
    status: 200,
    msg: "success",
    data: {
      uid,
      storagePath,
    },
  });
}
