import { getUserByServerSession } from "~/servers/common/user";
import { getDb } from "~/libs/db/db";
import { paddleApiKey, paddleSandbox } from "~/configs/globalConfig";

const db = getDb();
export async function GET(req: Request) {
  const user = await getUserByServerSession();
  if (!user.user_id) {
    return Response.json({ status: 1 });
  }
  const user_id = user.user_id;

  const { rows: paddleResult } = await db.query(`select * from paddle_user_payment_data where user_id=$1 order by created_at desc;`, [user_id]);
  if (paddleResult.length > 0) {
    const subscription_id = paddleResult[0].subscription_id;
    const url = paddleSandbox ? `https://sandbox-api.paddle.com/subscriptions/${subscription_id}` : `https://api.paddle.com/subscriptions/${subscription_id}`;
    const response = await fetch(`${url}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${paddleApiKey}`,
      },
    });
    const result = await response.json();
    const cancel_url = result?.data?.management_urls?.cancel;
    return Response.json({ status: 0, cancel_url: cancel_url });
  }

  return Response.json({ status: 1 });
}
