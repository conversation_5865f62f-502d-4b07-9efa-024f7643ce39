import { stripe } from "~/libs/stripe/stripe";
import { createOrRetrieveCustomer } from "~/libs/stripe/handle-stripe";
import Stripe from "stripe";
import { getDb } from "~/libs/db/db";
import { domainNameLowercase } from "~/configs/globalConfig";

const db = getDb();
export async function POST(req: Request) {
  if (req.method === "POST") {
    // 1. Destructure the price and quantity from the POST body
    const { price, quantity = 1, metadata = {}, cancelRedirectUrl, successRedirectUrl, user_id } = await req.json();

    try {
      // 2. select user
      const userInfoRes = await db.query(
        `
        select * from user_info where user_id = $1
        `,
        [user_id]
      );
      const userInfoRow = userInfoRes.rows;
      if (userInfoRow.length <= 0) {
        return new Response(JSON.stringify({ message: "No such user" }), {
          status: 200,
        });
      }
      const userInfo = userInfoRow[0];
      const userEmail = userInfo.email;

      // 3. Retrieve or create the customer in Stripe
      const customer = await createOrRetrieveCustomer({
        user_id: user_id,
        email: userEmail,
      });

      // 4. Create a checkout session in Stripe
      let session: Stripe.Response<Stripe.Checkout.Session>;
      if (price.type === "recurring") {
        // 检查是否已订阅
        const { rows: subscriptionRes } = await db.query(
          `
          select * from stripe_subscriptions where user_id = $1
          `,
          [user_id]
        );
        if (subscriptionRes.length > 0) {
          const subscription = subscriptionRes[0];
          const status = subscription.status;
          const price_id = subscription.price_id;
          if (status == "active" || status == "trialing") {
            return new Response(JSON.stringify({ status: 604, msg: "Already Subscribed!" }), {
              status: 200,
            });
          }
        }

        // @ts-ignore
        session = await stripe.checkout.sessions.create({
          payment_method_types: ["card"],
          billing_address_collection: "auto",
          customer,
          customer_update: {
            address: "auto",
          },
          line_items: [
            {
              price: price.id,
              quantity,
            },
          ],
          mode: "subscription",
          allow_promotion_codes: false,
          subscription_data: {
            trial_from_plan: true,
            metadata: {
              domain: domainNameLowercase,
            },
          },
          success_url: successRedirectUrl,
          cancel_url: cancelRedirectUrl,
          metadata: {
            domain: domainNameLowercase,
          },
        });
      } else if (price.type === "one_time") {
        session = await stripe.checkout.sessions.create({
          payment_method_types: ["card"],
          billing_address_collection: "auto",
          customer,
          customer_update: {
            address: "auto",
          },
          line_items: [
            {
              price: price.id,
              quantity,
            },
          ],
          mode: "payment",
          allow_promotion_codes: false,
          success_url: successRedirectUrl,
          cancel_url: cancelRedirectUrl,
          metadata: {
            domain: domainNameLowercase,
          },
        });
      }

      if (session) {
        return new Response(JSON.stringify({ sessionId: session.id }), {
          status: 200,
        });
      } else {
        return new Response(
          JSON.stringify({
            error: { statusCode: 500, message: "Session is not defined" },
          }),
          { status: 200 }
        );
      }
    } catch (err: any) {
      console.log(err);
      return new Response(JSON.stringify(err), { status: 500 });
    }
  } else {
    return new Response(JSON.stringify({ message: "Method Not Allowed" }), {
      status: 200,
    });
  }
}
