import {stripe} from '~/libs/stripe/stripe';
import {getURL} from "~/utils/helpers";

export async function POST(req: Request) {
  let json = await req.json();
  let customer_id = json.customer_id as any;

  const {url} = await stripe.billingPortal.sessions.create({
    customer: customer_id,
    return_url: `${getURL()}`
  });
  return new Response(JSON.stringify({url: url}), {
    status: 200
  });

}
