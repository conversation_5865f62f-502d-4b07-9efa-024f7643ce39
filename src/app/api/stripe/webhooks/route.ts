import Stripe from "stripe";
import { stripe } from "~/libs/stripe/stripe";
import { manageSubscriptionStatusChange } from "~/libs/stripe/handle-stripe";
import { userPayComplete } from "~/servers/pay/manageUserTimes";
import { getDb } from "~/libs/db/db";
import { domainNameLowercase, stripeWebhookSecret } from "~/configs/globalConfig";

const db = getDb();

const relevantEvents = new Set(["checkout.session.completed", "customer.subscription.created", "customer.subscription.updated", "customer.subscription.deleted", "invoice.marked_uncollectible"]);

export async function POST(req: Request) {
  const body = await req.text();
  const sig = req.headers.get("stripe-signature") as string;
  const webhookSecret = stripeWebhookSecret;
  let event: Stripe.Event;

  try {
    if (!sig || !webhookSecret) return;
    event = stripe.webhooks.constructEvent(body, sig, webhookSecret);
  } catch (err: any) {
    console.log(`❌ Error message: ${err.message}`);
    return new Response(`Webhook Error: ${err.message}`, { status: 200 });
  }

  if (!event) {
    return new Response(`Webhook Error: event undefined`, { status: 200 });
  }

  if (!event.type) {
    return new Response(`Webhook Error: event.type undefined`, { status: 200 });
  }

  if (relevantEvents.has(event.type)) {
    // 判断 metadata 数据
    console.log("event.data.object===>", event.data.object);
    const jsonStr = JSON.stringify(event.data.object);
    const metadata = JSON.parse(jsonStr)?.metadata;
    console.log("metadata===>", metadata);
    if (metadata?.domain?.toLowerCase() != domainNameLowercase?.toLowerCase()) {
      return new Response(`Webhook handler`, { status: 200 });
    }
    try {
      switch (event.type) {
        case "invoice.marked_uncollectible":
          const invoiceData = event.data.object as Stripe.Invoice;
          const invoiceId = invoiceData.id;
          const invoiceCustomer = invoiceData.customer;
          const invoiceStatus = invoiceData.status;
          const invoiceText = JSON.stringify(invoiceData);
          await db.query(
            `
            insert into stripe_invoice_record(stripe_customer_id, invoice_data, status) values($1, $2, $3)
            `,
            [invoiceCustomer, invoiceText, invoiceStatus]
          );
          // 废弃账单
          await stripe.invoices.voidInvoice(invoiceId);
          break;
        case "customer.subscription.created":
        case "customer.subscription.updated":
        case "customer.subscription.deleted":
          const subscription = event.data.object as Stripe.Subscription;
          // console.log(`subscription.id==${subscription.id}=>${event.type}`);
          // console.log(`subscription==${subscription.id}=>${JSON.stringify(subscription)}`);
          // console.log(`subscription.status==${subscription.id}=>${subscription.status}`);

          //-=-=-=-=-=-=-=-=-
          // 事件记录开始
          const subscriptionStatus = subscription.status;
          const subscriptionCustomer = subscription.customer;
          const subscriptionText = JSON.stringify(subscription);
          await db.query(
            `
            insert into stripe_order_record(stripe_customer_id, checkout_session, status) values($1, $2, $3)
            `,
            [subscriptionCustomer, subscriptionText, subscriptionStatus]
          );
          // 事件记录结束
          //-=-=-=-=-=-=-=-=-

          await manageSubscriptionStatusChange(subscription.id, subscription.customer as string);
          break;
        case "checkout.session.completed":
          const checkoutSession = event.data.object as Stripe.Checkout.Session;
          // console.log(`checkoutSession.id==${checkoutSession.id}=>${event.type}`);
          // console.log(`checkoutSession==${checkoutSession.id}=>${JSON.stringify(checkoutSession)}`);
          // console.log(`checkoutSession.status==${checkoutSession.id}=>${checkoutSession.status}`);

          //-=-=-=-=-=-=-=-=-
          // 事件记录开始
          const checkoutStatus = checkoutSession?.status;
          const checkoutCustomer = checkoutSession?.customer;
          const checkoutCountry = checkoutSession?.customer_details?.address?.country;
          const checkoutText = JSON.stringify(checkoutSession);
          await db.query(
            `
            insert into stripe_order_record(stripe_customer_id, checkout_session, status, card_address_country) values($1, $2, $3, $4)
            `,
            [checkoutCustomer, checkoutText, checkoutStatus, checkoutCountry]
          );
          // 事件记录结束
          //-=-=-=-=-=-=-=-=-

          if (checkoutSession.mode === "subscription") {
            const subscriptionId = checkoutSession.subscription;
            await manageSubscriptionStatusChange(subscriptionId as string, checkoutSession.customer as string);
          } else {
            // 一次性付费，充值次数，这里调用处理
            await userPayComplete(checkoutSession);
          }
          break;
        default:
          throw new Error("Unhandled relevant event!");
      }
    } catch (error) {
      console.log(error);
      return new Response("Webhook handler failed. View your nextjs function logs.", {
        status: 200,
      });
    }
  }
  return new Response(JSON.stringify({ received: true }));
}
