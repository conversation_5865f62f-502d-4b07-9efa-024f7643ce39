import type { MetadataRoute } from "next";
import { siteNameConfig } from "~/configs/globalConfig";

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: siteNameConfig,
    short_name: siteNameConfig,
    description: "Play free online games on TinyFun. No downloads needed, just click and play for endless fun anytime, anywhere.",
    start_url: "/",
    display: "standalone",
    background_color: "#ffffff",
    theme_color: "#000000",
    icons: [
      {
        src: "/icon/web-app-manifest-192x192.png",
        sizes: "192x192",
        type: "image/png",
      },
      {
        src: "/icon/web-app-manifest-512x512.png",
        sizes: "512x512",
        type: "image/png",
      },
      {
        src: "/icon/apple-touch-icon.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
  };
}
