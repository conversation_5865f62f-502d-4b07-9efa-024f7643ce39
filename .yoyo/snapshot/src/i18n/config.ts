import { isMultiLanguage } from "~/configs/globalConfig";

export const localesAll = ["en", "zh"] as const;
export const localesEn = ["en"] as const;

export const languagesAll = [
  {
    code: "en-US",
    lang: "en",
    language: "English",
    languageInChineseSimple: "英语",
  },
  {
    code: "zh-CN",
    lang: "zh",
    language: "简体中文",
    languageInChineseSimple: "简体中文",
  },
];
export const languagesEn = [
  {
    code: "en-US",
    lang: "en",
    language: "English",
    languageInChineseSimple: "英语",
  },
];

export const languages = isMultiLanguage ? languagesAll : languagesEn;

export const locales = isMultiLanguage ? localesAll : localesEn;

export const getLanguageByLang = (lang) => {
  for (let i = 0; i < languages.length; i++) {
    if (lang == languages[i].lang) {
      return languages[i];
    }
  }
};

// 根据语言缩写获取 语言中文名-语言名
export const getLanguageResult = (lang) => {
  const languageCurrent = getLanguageByLang(lang);
  return languageCurrent.languageInChineseSimple + "-" + languageCurrent.language;
};

// 根据语言缩写列表获取语言列表
export const getLanguageListByLangList = (langList) => {
  if (langList.length <= 0) {
    return languages;
  }
  const resultList = [];
  for (let i = 0; i < languages.length; i++) {
    const language = languages[i];
    if (langList.includes(language.lang)) {
      resultList.push(language);
    }
  }
  return resultList;
};
