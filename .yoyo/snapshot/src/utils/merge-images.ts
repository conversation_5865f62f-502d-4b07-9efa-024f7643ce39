type ImageInput = File | null | string;

export async function mergeImagesClient(leftImage: ImageInput, rightImage: ImageInput): Promise<Blob> {
  const canvas: HTMLCanvasElement = document.createElement("canvas");
  const ctx: CanvasRenderingContext2D | null = canvas.getContext("2d");

  if (!ctx) {
    throw new Error("failed to get canvas context");
  }

  const width = 1280;
  const height = 768;
  const singleWidth: number = width / 2;
  const featherWidth = 200;

  canvas.width = width;
  canvas.height = height;

  const loadImage = async (input: ImageInput): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;

      if (typeof input === "string") {
        img.src = input;
      } else if (input instanceof File) {
        const reader = new FileReader();
        reader.onload = (e) => {
          img.src = e.target?.result as string;
        };
        reader.onerror = reject;
        reader.readAsDataURL(input);
      } else {
        reject(new Error("unsupported image input type"));
      }
    });
  };

  const drawImageProp = (img: HTMLImageElement, x: number, y: number, w: number, h: number) => {
    const imgRatio = img.width / img.height;
    const canvasRatio = w / h;
    let sx, sy, sWidth, sHeight;

    if (imgRatio > canvasRatio) {
      sHeight = img.height;
      sWidth = sHeight * canvasRatio;
      sx = (img.width - sWidth) / 2;
      sy = 0;
    } else {
      sWidth = img.width;
      sHeight = sWidth / canvasRatio;
      sx = 0;
      sy = (img.height - sHeight) / 2;
    }

    ctx.drawImage(img, sx, sy, sWidth, sHeight, x, y, w, h);
  };

  try {
    const [leftImg, rightImg] = await Promise.all([loadImage(leftImage), loadImage(rightImage)]);

    // 绘制左图
    drawImageProp(leftImg, 0, 0, singleWidth, height);

    // 绘制右图
    drawImageProp(rightImg, singleWidth, 0, singleWidth, height);

    // 获取左右图像的边缘颜色
    const leftEdgeColor = ctx.getImageData(singleWidth - 1, height / 2, 1, 1).data;
    const rightEdgeColor = ctx.getImageData(singleWidth, height / 2, 1, 1).data;

    // 创建左图右边缘的渐变
    const leftGradient = ctx.createLinearGradient(singleWidth - featherWidth / 3, 0, singleWidth, 0);
    leftGradient.addColorStop(0, `rgba(${leftEdgeColor[0]},${leftEdgeColor[1]},${leftEdgeColor[2]},0)`);
    leftGradient.addColorStop(0.5, `rgba(${leftEdgeColor[0]},${leftEdgeColor[1]},${leftEdgeColor[2]},0.5)`);
    leftGradient.addColorStop(1, `rgba(${leftEdgeColor[0]},${leftEdgeColor[1]},${leftEdgeColor[2]},1)`);

    // 创建右图左边缘的渐变
    const rightGradient = ctx.createLinearGradient(singleWidth, 0, singleWidth + featherWidth / 3, 0);
    rightGradient.addColorStop(0, `rgba(${rightEdgeColor[0]},${rightEdgeColor[1]},${rightEdgeColor[2]},1)`);
    rightGradient.addColorStop(0.5, `rgba(${rightEdgeColor[0]},${rightEdgeColor[1]},${rightEdgeColor[2]},0.5)`);
    rightGradient.addColorStop(1, `rgba(${rightEdgeColor[0]},${rightEdgeColor[1]},${rightEdgeColor[2]},0)`);

    // 应用左图渐变
    ctx.globalCompositeOperation = "destination-out";
    ctx.fillStyle = leftGradient;
    ctx.fillRect(singleWidth - featherWidth, 0, featherWidth, height);

    // 应用右图渐变
    ctx.fillStyle = rightGradient;
    ctx.fillRect(singleWidth, 0, featherWidth, height);

    // 创建中间的融合渐变
    const centerGradient = ctx.createLinearGradient(singleWidth - featherWidth, 0, singleWidth + featherWidth, 0);
    centerGradient.addColorStop(0, `rgba(${leftEdgeColor[0]},${leftEdgeColor[1]},${leftEdgeColor[2]},0.1)`);
    centerGradient.addColorStop(0.5, `rgba(${(leftEdgeColor[0] + rightEdgeColor[0]) / 2},${(leftEdgeColor[1] + rightEdgeColor[1]) / 2},${(leftEdgeColor[2] + rightEdgeColor[2]) / 2},0.05)`);
    centerGradient.addColorStop(1, `rgba(${rightEdgeColor[0]},${rightEdgeColor[1]},${rightEdgeColor[2]},0.1)`);

    // 应用中间融合渐变
    ctx.globalCompositeOperation = "source-over";
    ctx.fillStyle = centerGradient;
    ctx.fillRect(singleWidth - featherWidth, 0, featherWidth * 2, height);

    // 将 canvas 转换为 Blob
    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("failed to create Blob"));
          }
        },
        "image/jpeg",
        0.95
      ); // 增加质量参数
    });
  } catch (error) {
    throw new Error(`image merge error: ${error as string}`);
  }
}

export default mergeImagesClient;
