import CryptoJS from "crypto-js";

// 使用公钥加密
export const encryptWithKey = (message) => {
  const encryptKey = process.env.ENCRYPT_KEY;
  const encrypted = CryptoJS.AES.encrypt(message, encryptKey).toString();
  return encrypted;
};

// 使用私钥解密
export const decryptWithKey = (encryptedMessage) => {
  const encryptKey = process.env.ENCRYPT_KEY;
  const decrypted = CryptoJS.AES.decrypt(encryptedMessage, encryptKey).toString(CryptoJS.enc.Utf8);
  return decrypted;
};
