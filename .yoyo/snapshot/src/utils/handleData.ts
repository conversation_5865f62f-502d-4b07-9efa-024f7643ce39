export const getArrayUrlResult = (origin) => {
  if (origin) {
    const jsonResult = JSON.parse(origin);
    if (jsonResult.length > 0) {
      return jsonResult;
    }
  }
  return [];
};

export const getDeviceType = (userAgent) => {
  if (/Mobile|Android|iPhone|iPad|iPod|BlackBerry|Opera Mini|IEMobile/i.test(userAgent)) {
    return "移动设备";
  } else {
    return "电脑";
  }
};

export const formatDateToUTC8 = (dateString, isStartOfDay) => {
  const date = new Date(dateString);
  date.setHours(date.getHours() + 8);
  if (isStartOfDay) {
    date.setDate(date.getDate() - 1); // 加一天
  }
  if (isStartOfDay) {
    date.setUTCHours(16, 0, 0, 0); // UTC+8 一天开始
  } else {
    date.setUTCHours(15, 59, 59, 999); // UTC+8 一天结束
  }

  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0");
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, "0");
  const microseconds = "000"; // JavaScript Date 对象不支持微秒，需要手动补零

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}${microseconds} +00:00`;
};

export const formatDateToUTC = (dateString, isStartOfDay) => {
  const date = new Date(dateString);
  if (isStartOfDay) {
    date.setDate(date.getDate() - 1); // 加一天
  }
  if (isStartOfDay) {
    date.setUTCHours(24, 0, 0, 0); // UTC 一天开始
  } else {
    date.setUTCHours(23, 59, 59, 999); // UTC 一天结束
  }

  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, "0");
  const day = String(date.getUTCDate()).padStart(2, "0");
  const hours = String(date.getUTCHours()).padStart(2, "0");
  const minutes = String(date.getUTCMinutes()).padStart(2, "0");
  const seconds = String(date.getUTCSeconds()).padStart(2, "0");
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, "0");
  const microseconds = "000"; // JavaScript Date 对象不支持微秒，需要手动补零

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}${microseconds} +00:00`;
};

export const createPagination = (totalPages, currentPage, maxPagesToShow) => {
  const pages = [];
  let startPage, endPage;

  if (totalPages <= maxPagesToShow) {
    // 总页数少于或等于最大显示页数，显示所有页码
    startPage = 1;
    endPage = totalPages;
  } else {
    // 确定页码的开始和结束位置
    const maxPagesBeforeCurrentPage = Math.floor(maxPagesToShow / 2);
    const maxPagesAfterCurrentPage = Math.ceil(maxPagesToShow / 2) - 1;
    if (currentPage <= maxPagesBeforeCurrentPage) {
      // 当前页码靠近开始
      startPage = 1;
      endPage = maxPagesToShow;
    } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
      // 当前页码靠近结束
      startPage = totalPages - maxPagesToShow + 1;
      endPage = totalPages;
    } else {
      // 当前页码在中间
      startPage = currentPage - maxPagesBeforeCurrentPage;
      endPage = currentPage + maxPagesAfterCurrentPage;
    }
  }

  // 生成页码
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  return pages;
};

// 转换页面数据格式
export const transformPageData = (pageData) => {
  if (!pageData) return null;

  return {
    status: pageData.status || 0,
    title: pageData.page_title || "",
    description: pageData.page_description || "",
    components:
      pageData.contentList?.map((content) => ({
        type: content.section_name || "",
        props: content.content_json || {},
        styles: content.content_styles || {},
        id: content.content_base_id || "",
      })) || [],
  };
};
export const isBlockCountry = (country) => {
  const blockCountry = ["IN", "IQ", "PK", "ID", "BD", "PH", "VN", "EG", "MY", "LK", "IR"];
  return blockCountry.includes(country);
};
