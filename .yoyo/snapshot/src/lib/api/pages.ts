import { getDb } from "~/libs/db/db";

// 引入推荐游戏和区域配置的类型定义，假设它们在 recommendations.ts 中已定义并导出
import { RecommendedGame, RecommendationZoneConfig } from "./recommendations"; // 根据实际路径修改

const db = getDb();

export interface PageContent {
  title: string;
  description?: string;
  name?: string;
  iframe_url?: string;
  components?: any[];
  introduction?: any;
  features?: any;
  how?: any;
  tips?: any;
  faq?: any;
  cta?: any;
}

export interface Page {
  uid: string;
  game_uid: string;
  site_uid: string;
  template_key: string;
  title: string;
  slug: string;
  json_content: any;
  status: number;
  iframe_url?: string;
  public_url: string;
  locale?: string;
  domain: string;
}

// Helper function to safely get nested value from an object
function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.');
  let current = obj;
  for (const key of keys) {
    if (current === null || typeof current !== 'object' || !(key in current)) {
      return undefined; // Path does not exist
    }
    current = current[key];
  }
  return current;
}

// Placeholder for generating share buttons (replace with actual implementation)
function generateShareButtons(url: string, title: string): string {
  // TODO: Implement actual social media share buttons
  return '';
}

// Placeholder for generating comments section (replace with actual implementation)
function generateCommentsSection(pageUid: string): string {
  // TODO: Implement actual comments section
  return '';
}

// 根据 slug 和 domain 获取页面
export async function getPageBySlug(slug: string, domain: string, isHomepage: boolean = false): Promise<Page | null> {
  try {
    let query = `SELECT * FROM pages WHERE domain = $1 AND status = 1`;
    let params: any[] = [domain];
    if (isHomepage) {
      query += ` AND is_homepage = true`;
    } else {
      query += ` AND slug = $2`;
      params.push(slug);
    }
    query += ` LIMIT 1`;

    const { rows } = await db.query(query, params);

    if (rows.length === 0) {
      return null;
    }

    const page = rows[0] as Page;
    return page;

  } catch (error) {
    console.error('[getPageBySlug] Error fetching page:', error); // LOG: Error details
    return null;
  }
}

// 根据模板 UID 获取模板内容
export async function getTemplateByUid(templateUid: string): Promise<string | null> {
  try {
    const { rows } = await db.query(`
      SELECT content FROM templates 
      WHERE uid = $1
      LIMIT 1
    `, [templateUid]);

    if (rows.length === 0) {
      return null;
    }

    return rows[0].content;

  } catch (error) {
    console.error('[getTemplateByUid] Error fetching template:', error); // LOG: Error details
    return null;
  }
}

// Helper function to handle simple replacements
function handleSimpleReplacements(content: string, data: any, page: Page): string {
  let renderedContent = content;
  const replacements: { [key: string]: string } = {};

  const simpleReplacementKeys = ['title', 'description', 'h1', 'name'];
  simpleReplacementKeys.forEach(key => {
    replacements[key] = String(getNestedValue(data, key) || '');
  });

  // Handle locale separately
  replacements['locale'] = String(page.locale || 'en');

  // Perform replacements using a single pass where possible or iterate over known placeholders
  let pattern = new RegExp(`\\$\\{(${Object.keys(replacements).join('|')})\\}`, 'g');
  renderedContent = renderedContent.replace(pattern, (match, key) => replacements[key]);

  return renderedContent;
}

// Helper function to handle iframe
function handleIframe(content: string, iframeSrc: string | null): string {
  // Assumes the template has a placeholder like ${rendered_iframe}
  // The conditional logic and iframe tag generation are handled here.

  let renderedHtml = '';
  if (iframeSrc) {
    // Generate the full iframe HTML with a class for external CSS styling
    renderedHtml = `<iframe src="${String(iframeSrc)}" class="game-iframe-element" id="game-iframe" frameborder="0" allowfullscreen></iframe>`;
  }

  // Replace the placeholder in the content
  // Using a simple string literal regex for this specific known placeholder
  return content.replace(new RegExp('\\$\\{rendered_iframe\\}', 'g'), renderedHtml);
}

// Helper function to handle sections
function handleSection(content: string, sectionKey: string, sectionData: any): string {
  let renderedContent = content;

  if (sectionData && typeof sectionData === 'object') {
    // Handle h2 text placeholder
    const h2Regex = new RegExp(`\\$\\{${sectionKey}\\.h2_text\\}`, 'g');
    renderedContent = renderedContent.replace(h2Regex, String(getNestedValue(sectionData, 'h2') || ''));

    // Handle content - array or simple text
    const contentData = getNestedValue(sectionData, 'content');

    if (Array.isArray(contentData)) {
       // For array content, generate list item HTML with classes for external CSS
       const contentHtmlRegex = new RegExp(`\\$\\{${sectionKey}\\.content_html\\}`, 'g');
       let contentHtml = contentData.map(item => {
        if (item && typeof item === 'object' && item.h3 !== undefined && item.content !== undefined) {
          // Generating list item HTML with classes in pages.ts
          return `
            <div class="section-item section-${sectionKey}-item">
              <h3 class="section-item-h3">${String(item.h3 || '')}</h3>
              <p class="section-item-content">${String(item.content || '')}</p>
            </div>`;
        } else if (item && typeof item === 'string') {
           // Generating paragraph HTML with classes in pages.ts
          return `<div class="section-paragraph section-${sectionKey}-paragraph"><p class="section-paragraph-content">${String(item || '')}</p></div>`;
        }
        return '';
      }).join('');
       renderedContent = renderedContent.replace(contentHtmlRegex, contentHtml);

    } else if (contentData !== undefined && contentData !== null) {
       // For non-array content, replace content_text placeholder
       const contentTextRegex = new RegExp(`\\$\\{${sectionKey}\\.content_text\\}`, 'g');
       renderedContent = renderedContent.replace(contentTextRegex, String(contentData));
    }
  }

  return renderedContent;
}

// Helper function to handle CTA section
function handleCTA(content: string, ctaData: any): string {
  let renderedContent = content;

  if (ctaData && typeof ctaData === 'object') {
    // Replace h2 text placeholder
    const h2Regex = new RegExp(`\\$\\{cta\\.h2_text\\}`, 'g');
    renderedContent = renderedContent.replace(h2Regex, String(getNestedValue(ctaData, 'h2') || ''));

    // Replace content text placeholder
    const contentRegex = new RegExp(`\\$\\{cta\\.content_text\\}`, 'g');
    renderedContent = renderedContent.replace(contentRegex, String(getNestedValue(ctaData, 'content') || ''));

    // Replace button url and text placeholders
    const buttonUrlRegex = new RegExp(`\\$\\{cta\\.button_url\\}`, 'g');
    const buttonTextRegex = new RegExp(`\\$\\{cta\\.button_text\\}`, 'g');

    const buttonText = getNestedValue(ctaData, 'buttonText');
    const buttonUrl = getNestedValue(ctaData, 'buttonUrl');

    if (buttonUrl) {
      renderedContent = renderedContent.replace(buttonUrlRegex, String(buttonUrl));
    } else {
       renderedContent = renderedContent.replace(buttonUrlRegex, '');
    }

    if (buttonText) {
       renderedContent = renderedContent.replace(buttonTextRegex, String(buttonText));
    } else {
       renderedContent = renderedContent.replace(buttonTextRegex, '');
    }

  }

  return renderedContent;
}

// 新增辅助函数：渲染推荐游戏区域
function renderRecommendedGamesSection(
    recommendedGames: RecommendedGame[],
    recommendationZones: RecommendationZoneConfig[]
): string {
    // 修正数据检查条件，只检查数组长度
    if (recommendedGames.length === 0 || recommendationZones.length === 0) {
        return ''; // 没有数据则不渲染任何内容
    }

    let html = '<div class="recommended-games-section">'; // 整体容器

    // 按 recommendation_zone 对游戏进行分组
    const gamesByZone: { [key: string]: RecommendedGame[] } = {};
    recommendedGames.forEach(game => {
        if (!gamesByZone[game.recommendation_zone]) {
            gamesByZone[game.recommendation_zone] = [];
        }
        gamesByZone[game.recommendation_zone].push(game);
    });

    // 遍历推荐区域配置，按配置顺序渲染每个区域
    recommendationZones.forEach(zoneConfig => {
        const zoneId = zoneConfig.id;
        const zoneName = zoneConfig.name;
        const gamesInZone = gamesByZone[zoneId];

        if (gamesInZone && gamesInZone.length > 0) {
            html += `
                <div class="recommendation-zone">
                    <h2 class="recommendation-zone-title">${zoneName}</h2>
                    <div class="recommendation-game-list">
                        ${gamesInZone.map(game => `
                            <a href="${game.jump_url}" class="recommended-game-item" target="_blank">
                                <img src="${game.cover_img_url}" alt="${game.title}" class="recommended-game-cover">
                                <p class="recommended-game-title">${game.title}</p>
                            </a>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    });

    html += '</div>'; // 关闭整体容器

    return html;
}

// 修改 renderPage 函数签名，接受推荐游戏和区域配置
export async function renderPage(
    page: Page,
    recommendedGames: RecommendedGame[] = [], // 添加推荐游戏参数，默认为空数组
    recommendationZones: RecommendationZoneConfig[] = [] // 添加推荐区域参数，默认为空数组
): Promise<string | null> {

    try {
        const template = await getTemplateByUid(page.template_key);

        if (!template) {
            console.error('[renderPage] Template not found for rendering.');
            return null;
        }

        // Add canonical URL replacement using page.public_url
        let renderedContent = template;
        if (page.public_url) {
            const canonicalRegex = new RegExp('\{\{canonical\}\}', 'g'); // Regex to match {{canonical}}
            renderedContent = renderedContent.replace(canonicalRegex, String(page.public_url));
        } else {
            // Optionally remove the placeholder if public_url is missing, or leave it.
            const canonicalRegex = new RegExp('\{\{canonical\}\}', 'g');
            renderedContent = renderedContent.replace(canonicalRegex, '');
        }

        const { json_content } = page;

        let data = json_content || {}; // 如果 json_content 为空，使用空对象避免错误

        // 1. Handle simple replacements
        renderedContent = handleSimpleReplacements(renderedContent, data, page);

        // 2. Handle iframe
        renderedContent = handleIframe(renderedContent, page.iframe_url);

        // 3. Handle share buttons
        // Use explicit replace for known placeholders even if content is empty string
        renderedContent = renderedContent.replace(new RegExp('\\$\\{shareButtons\\}', 'g'), generateShareButtons(page.public_url || '', data.title || ''));

        // 4. Handle recommended games section (NEW)
        // Ensure placeholder replacement happens after iframe and share buttons, before other sections
        const recommendedGamesHtml = renderRecommendedGamesSection(recommendedGames, recommendationZones);
        renderedContent = renderedContent.replace(new RegExp('\\$\\{recommended_games_section\\}', 'g'), recommendedGamesHtml);

        // 5. Handle sections (introduction, how, tips, faq)
        // Ensure these section treatments are after recommended games
        const sectionKeys = ['introduction', 'how', 'tips', 'faq'];
        sectionKeys.forEach(sectionKey => {
            const sectionData = getNestedValue(data, sectionKey);
            renderedContent = handleSection(renderedContent, sectionKey, sectionData);
        });

        // 6. Handle features section
        const featuresData = getNestedValue(data, 'features');
        renderedContent = handleSection(renderedContent, 'features', featuresData);

        // 7. Handle CTA section
        const ctaData = getNestedValue(data, 'cta');
        renderedContent = handleCTA(renderedContent, ctaData);

        // 8. Handle comments section
        renderedContent = renderedContent.replace(new RegExp('\\$\\{commentsSection\\}', 'g'), generateCommentsSection(page.uid || ''));

        // Remove any remaining placeholders
        // This regex should catch any remaining ${...} if the specific replacements missed them.
        // Let's ensure the regex is correctly escaped.
        const anyPlaceholderRegex = new RegExp('\\$\\{.+?\\}', 'g');
        renderedContent = renderedContent.replace(anyPlaceholderRegex, '');

        return renderedContent;

    } catch (error) {
        console.error('[renderPage] Error rendering page:', error);
        return null;
    }
}