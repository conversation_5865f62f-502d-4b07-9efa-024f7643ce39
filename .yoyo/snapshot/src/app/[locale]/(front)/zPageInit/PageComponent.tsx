'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useCommonContext } from "~/context/common-context";
import HeadInfo from "~/components/common/HeadInfo";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";

const PageComponent = ({
  locale
}) => {
  const router = useRouter();
  const [pagePath] = useState(
    ``
  );

  const {
    setShowLoadingModal,
    commonText
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => {
    }
  }, []);

  const hasAnyKey = (obj) => {
    return Object.keys(obj).length > 0;
  }

  return (
    <>
      <HeadInfo
        locale={locale}
        page={pagePath}
        title={""}
        description={""}
      />
      <Header
        locale={locale}
        page={pagePath}
      />
      <div className="mt-4 my-auto min-h-[90vh]">



      </div>
      <Footer
        locale={locale}
        page={pagePath}
      />
    </>
  )
}

export default PageComponent
