'use client'
import HeadInfo from "~/components/common/HeadInfo";
import Header from "~/components/common/Header";
import Footer from "~/components/common/Footer";
import { useEffect, useRef, useState } from "react";
import { useCommonContext } from "~/context/common-context";
import { getLinkHrefWithoutStartSlash } from "~/utils/buildLink";
import PricingPayProComponent from "~/components/pay/PricingPayProComponent";
import GoogleAdsense from "~/components/GoogleAdsense";

const PageComponent = ({
  locale,
}) => {
  const [pagePath] = useState('pricing');

  const { setShowLoadingModal, pricingText } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);

    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => {
    }
  }, []);

  return (
    <>
      <HeadInfo locale={locale} page={pagePath} title={pricingText.title} description={pricingText.description} />
      <Header locale={locale} page={pagePath} />
      <div className={"pt-24 pb-12 md:pt-36 md:pb-20 my-auto min-h-[90vh]"}>
        <PricingPayProComponent redirectUrl={getLinkHrefWithoutStartSlash(locale, pagePath)} isPricing={true} />
      </div>
      <Footer locale={locale} page={pagePath} />
      <GoogleAdsense />
    </>
  );
}

export default PageComponent
