import PageComponent from "./PageComponent";
import { setRequestLocale } from "next-intl/server";

import { getIndexPageText, getCommonText } from "~/i18n/languageText";
import { getAllBlogDetailByUrl } from "~/servers/manage/manageBlog";
import { notFound } from "next/navigation";

export const revalidate = 300;
export const dynamic = "force-static";

export default async function BlogPage(props) {
  const params = await props.params;
  const { locale, url } = params;
  // Enable static rendering
  setRequestLocale(locale);

  const blogDetail = await getAllBlogDetailByUrl(url);
  const indexText = await getIndexPageText();
  const commonText = await getCommonText();

  if (blogDetail.code == 404) {
    notFound();
  }

  return <PageComponent locale={locale} blogDetail={blogDetail.data} indexText={indexText} commonText={commonText} />;
}
