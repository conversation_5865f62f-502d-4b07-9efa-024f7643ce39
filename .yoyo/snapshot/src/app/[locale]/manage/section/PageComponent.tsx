'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useInterval } from "ahooks";
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { manageSectionPage } from "~/configs/globalConfig";
import { toast } from "react-toastify";
import CopyToClipboard from "~/components/common/CopyToClipboard";

const PageComponent = ({
  locale,
}) => {
  const router = useRouter();
  const [pagePath] = useState(manageSectionPage);

  const {
    setShowLoadingModal,
    userData
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => {
    }
  }, []);

  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  const checkUser = async () => {
    if (!userData?.user_id) {

    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        getSectionList(1);
      }
    }
  }
  useInterval(() => {
    checkUser();
  }, intervalCheckUser);

  const [sectionData, setSectionData] = useState([]);
  const [totalPage, setTotalPage] = useState(0);
  const [countTotal, setCountTotal] = useState(0);

  const getSectionList = async (page) => {
    if (!checkAdminUser(userData)) {
      return;
    }
    setShowLoadingModal(true);
    const requestData = {
      positionPage: page
    }
    const response = await fetch(`/api/manage/section/getList`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    })
    const result = await response.json();
    if (result.code === 200) {
      setSectionData(result.resultList);
      setTotalPage(result.totalPage);
      setCountTotal(result.countTotal);
    } else {
      toast(result.message, {
        type: "error",
      });
    }
    setShowLoadingModal(false);
  }

  const [currentPage, setCurrentPage] = useState(1);
  const [newSectionName, setNewSectionName] = useState('');
  const [newSectionType, setNewSectionType] = useState('component');
  const [newSectionStatus, setNewSectionStatus] = useState(0);
  const [newSectionJson, setNewSectionJson] = useState(undefined);
  const [newSectionStyles, setNewSectionStyles] = useState(undefined);
  const [newSectionSort, setNewSectionSort] = useState(0);
  const [editSectionId, setEditSectionId] = useState();

  const addNewSection = async () => {
    if (!checkAdminUser(userData)) {
      return;
    }
    if (!newSectionName) {
      return;
    }
    const requestData = {
      section_name: newSectionName?.trim(),
      section_type: newSectionType,
      section_json: newSectionJson,
      styles: newSectionStyles,
      section_sort: newSectionSort,
      status: newSectionStatus,
      id: editSectionId,
    };
    setShowLoadingModal(true);
    if (editSectionId) {
      const response = await fetch(`/api/manage/section/update`, {
        method: "POST",
        body: JSON.stringify(requestData),
      });
      const result = await response.json();
      if (result.code === 200) {
        await getSectionList(currentPage);
        toast(result.message, {
          type: "success",
        });
        setEditSectionId(undefined);
      } else {
        toast(result.message, {
          type: "error",
        });
      }
    } else {
      const response = await fetch(`/api/manage/section/add`, {
        method: "POST",
        body: JSON.stringify(requestData),
      });
      const result = await response.json();
      if (result.code === 200) {
        await getSectionList(currentPage);
        toast(result.message, {
          type: "success",
        });
        setEditSectionId(undefined);
      } else {
        toast(result.message, {
          type: "error",
        });
      }
    }
    setShowLoadingModal(false);
  };

  const editSection = async (section) => {
    setNewSectionName(section.section_name);
    setNewSectionType(section.section_type);
    setNewSectionJson(JSON.stringify(section.section_json));
    setNewSectionStyles(JSON.stringify(section.styles));
    if (section.section_sort) {
      setNewSectionSort(section.section_sort);
    }
    setNewSectionStatus(section.status);
    setEditSectionId(section.id);
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const changeSectionStatus = async (section) => {
    if (!checkAdminUser(userData)) {
      return;
    }
    const requestData = {
      id: section.id,
      status: section.status === 1 ? 0 : 1,
    };
    setShowLoadingModal(true);
    const response = await fetch(`/api/manage/section/changeStatus`, {
      method: "POST",
      body: JSON.stringify(requestData),
    });
    const result = await response.json();
    setShowLoadingModal(false);
    if (result.code === 200) {
      await getSectionList(currentPage);
      toast(result.message, {
        type: "success",
      });
    } else {
      toast(result.message, {
        type: "error",
      });
    }
    setShowLoadingModal(false);
  };

  const prevPage = async () => {
    if (currentPage > 0 && currentPage <= totalPage) {
      const newCurrentPage = currentPage - 1;
      setCurrentPage(newCurrentPage);
      await getSectionList(newCurrentPage);
    }
  };

  const nextPage = async () => {
    if (currentPage < totalPage) {
      const newCurrentPage = currentPage + 1;
      setCurrentPage(newCurrentPage);
      await getSectionList(newCurrentPage);
    }
  };

  const [searchSectionName, setSearchSectionName] = useState("");

  const deleteSectionById = async (section) => {
    if (!checkAdminUser(userData)) {
      return;
    }
    const requestData = {
      id: section.id,
    };
    setShowLoadingModal(true);
    const response = await fetch(`/api/manage/section/delete`, {
      method: "POST",
      body: JSON.stringify(requestData),
    });
    const result = await response.json();
    setShowLoadingModal(false);
    if (result.code === 200) {
      await getSectionList(currentPage);
      toast(result.message, {
        type: "success",
      });
    }
  };

  if (!whiteUser) {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page={pagePath} />
      </>
    );
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <div className="flex flex-col items-center justify-center">
          <div className="text-2xl font-bold">section管理</div>
        </div>

        <div className="mt-8 w-full max-w-5xl mx-auto px-4">
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-6 py-6">
              <div className="grid grid-cols-1 gap-8">
                <div className="space-y-4">
                  <label htmlFor="section-name" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    section_name
                  </label>
                  <div>
                    <input
                      type="text"
                      name="section-name"
                      id="section-name"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入section_name"
                      value={newSectionName}
                      onChange={(e) => setNewSectionName(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label htmlFor="section-type" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    section_type
                  </label>
                  <div>
                    <select
                      id="section-type"
                      name="section-type"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      value={newSectionType}
                      onChange={(e) => setNewSectionType(e.target.value)}
                    >
                      <option value="component">component</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-4">
                  <label htmlFor="section-json" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    section_json
                  </label>
                  <div>
                    <textarea
                      name="section-json"
                      id="section-json"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入section_json"
                      value={newSectionJson}
                      onChange={(e) => setNewSectionJson(e.target.value)}
                      rows={8}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label htmlFor="section-styles" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    styles
                  </label>
                  <div>
                    <textarea
                      name="section-styles"
                      id="section-styles"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入styles"
                      value={newSectionStyles}
                      onChange={(e) => setNewSectionStyles(e.target.value)}
                      rows={8}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label htmlFor="section-sort" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    排序
                  </label>
                  <div>
                    <input
                      type="number"
                      name="section-sort"
                      id="section-sort"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入排序数字"
                      value={newSectionSort}
                      onChange={(e) => setNewSectionSort(Number(e.target.value))}
                    />
                  </div>
                </div>

                {editSectionId ? (
                  <div className="space-y-4">
                    <label htmlFor="section-status" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                      状态
                    </label>
                    <div>
                      <select
                        id="section-status"
                        name="section-status"
                        className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                        value={newSectionStatus}
                        onChange={(e) => setNewSectionStatus(Number(e.target.value))}
                      >
                        <option value="1">发布</option>
                        <option value="0">未发布</option>
                      </select>
                    </div>
                  </div>
                ) : null}

                <div className="flex justify-end pt-4">
                  <button
                    type="button"
                    onClick={addNewSection}
                    className="inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    保存
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 w-full max-w-[85%] mx-auto px-4">
          <div className="bg-white px-4 py-3">
            {/* 搜索区域 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 mr-2">section_name：</span>
                  <input
                    type="text"
                    className="w-64 px-3 py-2 border border-gray-300 rounded-md text-sm transition-shadow"
                    placeholder="请输入section_name"
                    value={searchSectionName}
                    onChange={(e) => setSearchSectionName(e.target.value)}
                  />
                </div>
                <button
                  onClick={() => getSectionList(currentPage)}
                  className="px-4 py-2 border border-gray-300 rounded-md bg-blue-500 text-sm font-medium text-white hover:bg-blue-600 transition-colors"
                >
                  查询
                </button>
              </div>
            </div>

            {/* 分页区域 */}
            <div className="flex items-center justify-between border-t border-gray-200 pt-4">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => prevPage()}
                  className={`mx-auto flex justify-center items-center rounded-md ${currentPage == 1 ? "bg-gray-400" : "bg-amber-500"} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                  disabled={currentPage == 1}
                >
                  上一页
                </button>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">第</span>
                  <input
                    type="number"
                    className="w-16 px-3 py-2 border border-gray-300 rounded-md text-sm transition-shadow"
                    value={currentPage}
                    onChange={(e) => setCurrentPage(Number(e.target.value))}
                  />
                  <span className="text-sm text-gray-700">页</span>
                </div>
                <button
                  onClick={() => nextPage()}
                  className={`mx-auto flex justify-center items-center rounded-md ${currentPage == totalPage ? "bg-gray-400" : "bg-amber-400"} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                  disabled={currentPage == totalPage}
                >
                  下一页
                </button>
              </div>
              <div className="text-sm text-gray-700">
                共 {totalPage} 页 ｜ {countTotal} 条
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      section_name
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      section_type
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      section_json
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      styles
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      排序
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      状态
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      操作
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      创建时间
                    </th>
                    <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      更新时间
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sectionData?.map((section) => (
                    <tr key={section.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {section.section_name}
                          <CopyToClipboard text={section.section_name} />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {section.section_type}
                          <CopyToClipboard text={section.section_type} />
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm font-medium text-gray-900 cursor-pointer break-words">
                          {JSON.stringify(section.section_json)}
                          <CopyToClipboard text={JSON.stringify(section.section_json)} />
                        </div>
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="text-sm font-medium text-gray-900 cursor-pointer break-words">
                          {section.styles ? JSON.stringify(section.styles) : "/"}
                          {section.styles && <CopyToClipboard text={JSON.stringify(section.styles)} />}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{section.section_sort}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            section.status === 1 ? "bg-green-100 text-green-800 border border-green-200" : "bg-gray-100 text-gray-800 border border-gray-200"
                          }`}
                        >
                          <span className={`w-2 h-2 rounded-full mr-2 ${section.status === 1 ? "bg-green-400" : "bg-gray-400"}`}></span>
                          {section.status === 1 ? "已发布" : "未发布"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <button
                            className="inline-flex items-center px-3 py-1.5 border border-indigo-600 text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transition-colors duration-200"
                            onClick={() => editSection(section)}
                          >
                            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                            编辑
                          </button>
                          <button
                            onClick={() => changeSectionStatus(section)}
                            className={`inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                              section.status === 1 ? "bg-blue-50 text-blue-600 hover:bg-blue-100 border border-blue-600" : "bg-green-50 text-green-600 hover:bg-green-100 border border-green-600"
                            }`}
                          >
                            <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                            </svg>
                            {section.status === 1 ? "取消发布" : "设为发布"}
                          </button>
                          <button
                            onClick={() => deleteSectionById(section)}
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-red-50 text-red-600 hover:bg-red-100 border border-red-600"
                          >
                            删除
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 max-w-xs truncate">{new Date(section.created_at).toLocaleString()}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 max-w-xs truncate">{new Date(section.updated_at).toLocaleString()}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default PageComponent
