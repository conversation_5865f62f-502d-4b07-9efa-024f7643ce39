import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';
import { getUserByServerSession } from "~/servers/common/user";
import { notFound } from "next/navigation";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { getPageData } from "~/servers/manage/page/page";

export default async function IndexPage(props) {
  const params = await props.params;

  const {
    locale = '',
    page = ''
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  const user_info = await getUserByServerSession();
  if (!user_info) {
    notFound();
  }
  if (!checkAdminUser(user_info)) {
    notFound();
  }

  const pageData = await getPageData(page);

  return (
    <PageComponent
      locale={locale}
      page={page}
      pageData={pageData}
    />
  )
}
