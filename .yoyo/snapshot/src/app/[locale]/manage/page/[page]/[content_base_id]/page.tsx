import { getContentSection } from "~/servers/manage/page/content";
import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';


export default async function ManagePage(props) {
  const params = await props.params;

  const {
    locale = '',
    page = '',
    content_base_id = ''
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  const contentSection = await getContentSection(content_base_id);

  return (
    <PageComponent
      locale={locale}
      page={page}
      content_base_id={content_base_id}
      contentSection={contentSection}
    />
  )
}
