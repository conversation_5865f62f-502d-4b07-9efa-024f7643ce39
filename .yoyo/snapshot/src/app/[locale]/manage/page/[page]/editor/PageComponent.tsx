'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import HeaderManage from '~/components/common/HeaderManage';
import { useCommonContext } from '~/context/common-context';
import { checkAdminUser } from '~/utils/checkWhiteUser';
import { useInterval } from 'ahooks';
import { toast } from 'react-toastify';
import { manageLandingPage, initPageJson } from "~/configs/globalConfig";
import ImageModal from "~/components/manage/ImageModal";
import JsonEditor from "~/components/common/JsonEditor";

// 导入预览所需组件
import Cta from "~/components/landing/Cta";
import Faq from "~/components/landing/Faq";
import Feature from "~/components/landing/Feature";
import HowToUse from "~/components/landing/HowToUse";
import IframeCard from "~/components/landing/IframeCard";
import Introduction from "~/components/landing/Introduction";
import List from "~/components/landing/List";
import ListSlider from "~/components/landing/ListSlider";
import { pagePage } from "~/configs/globalConfig";
import { HexColorPicker } from "react-colorful";
import ConfirmModal from "~/components/common/ConfirmModal";
import Modal from "~/components/common/Modal";

const componentMap = {
  IframeCard,
  Cta,
  Faq,
  Feature,
  Introduction,
  List,
  HowToUse,
  ListSlider,
};

const PageEditor = ({ locale, page, pageData, contentSection }) => {
  const router = useRouter();
  const [pagePath] = useState(`manage/page/${page}/editor`);
  const { setShowLoadingModal, userData, websiteBaseConfig } = useCommonContext();

  // 用户验证逻辑
  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  useEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => {};
  }, []);

  const checkUser = async () => {
    if (!userData?.user_id) {
    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        getPublishedPageList();
      }
    }
  };

  useInterval(() => {
    checkUser();
  }, intervalCheckUser);

  // 编辑器状态管理
  const [sections, setSections] = useState(() => {
    // 只过滤未删除的内容，并添加空值判断
    if (!contentSection || !Array.isArray(contentSection)) {
      return [];
    }
    return contentSection.filter((section) => section && !section.is_delete).sort((a, b) => (a?.content_sort || 0) - (b?.content_sort || 0));
  });

  const [selectedSection, setSelectedSection] = useState(null);

  const [expandedStyles, setExpandedStyles] = useState<Record<string, boolean>>({});
  const [openColorPicker, setOpenColorPicker] = useState<string | null>(null);
  const [contentJson, setContentJson] = useState(contentSection?.content_json || {});

  const [publishedPageList, setPublishedPageList] = useState([]);

  const [availableSections, setAvailableSections] = useState([]);
  const [isComponentModalOpen, setIsComponentModalOpen] = useState(false);

  // 修改删除确认状态
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [contentToDelete, setContentToDelete] = useState(null);

  // 添加编辑模式状态
  const [editMode, setEditMode] = useState<"form" | "json">("form");
  const [jsonContent, setJsonContent] = useState("");
  const [sectionList, setSectionList] = useState([]);
  // 初始化 JSON 内容
  useEffect(() => {
    console.log("sections》》》》", sections);
    if (sections) {
      const initialJson = sections.map((section) => ({
        type: section.section_name,
        props: section.content_json || sectionList?.find((item) => item.section_name === section.section_name)?.section_json || {},
      }));
      setJsonContent(JSON.stringify(initialJson, null, 2));
    }
  }, [sections]);

  const getSectionTypeList = async () => {
    const response = await fetch(`/api/manage/section/getList`, {
      method: "POST",
      body: JSON.stringify({
        positionPage: 1,
      }),
    });
    const result = await response.json();
    console.log(result);
    setSectionList(result.resultList);
  };

  // 处理 JSON 更改
  const handleJsonChange = (value: string | undefined) => {
    if (!value) return;

    try {
      const parsedJson = JSON.parse(value);
      setJsonContent(value);

      // 创建一个映射来跟踪现有的content_base_ids
      const existingContentBaseIds = new Set(sections.map((section) => section.content_base_id));

      // 创建新的sections数组
      const newSections = parsedJson.map((item: any, index: number) => {
        // 尝试找到匹配的现有section
        const existingSection = sections.find((section) => section.section_name === item.type && existingContentBaseIds.has(section.content_base_id));

        // 如果找到匹配的section，则更新它
        if (existingSection) {
          existingContentBaseIds.delete(existingSection.content_base_id);
          return {
            ...existingSection,
            section_name: item.type,
            content_json: item.props,
            section_json: sectionList?.find?.((section: any) => section.section_name === item.type)?.section_json,
            content_sort: index,
            is_delete: false,
          };
        }

        // 如果没有找到匹配的section，创建新的
        return {
          section_name: item.type,
          content_json: item.props,
          section_json: sectionList?.find?.((section: any) => section.section_name === item.type)?.section_json,
          content_base_id: `temp-${index}`,
          content_sort: index,
          is_delete: false,
        };
      });

      setSections(newSections);
    } catch (error) {
      console.error("Invalid JSON:", error);
    }
  };

  const getPublishedPageList = async () => {
    // 获取已发布的 page 列表
    const response = await fetch(`/api/manage/page/getPublishedPageList`);
    const result = await response.json();
    setPublishedPageList(result);
  };

  // 保存所有内容
  const saveAllContent = async () => {
    if (!sections || sections.length === 0) {
      toast.warning("没有可保存的内容", {
        containerId: "toast-container2",
      });
      return;
    }

    // 先按照 content_sort 排序
    const sortedSections = [...sections].sort((a, b) => (a?.content_sort || 0) - (b?.content_sort || 0));

    // 找出已保存过但在当前sections中不存在的组件
    const savedContentBaseIds = contentSection.filter((section) => section && !section.is_delete).map((section) => section.content_base_id);

    const currentContentBaseIds = sortedSections.map((section) => section.content_base_id).filter((id) => !id.startsWith("temp-")); // 排除临时ID

    const deletedContentBaseIds = savedContentBaseIds.filter((id) => !currentContentBaseIds.includes(id));

    // 如果存在已删除的组件，向用户确认
    if (deletedContentBaseIds.length > 0 && editMode === "json") {
      const confirmDelete = window.confirm(`您在JSON中删除了${deletedContentBaseIds.length}个已保存的组件。保存后这些组件将被永久删除，确定要继续吗？`);

      if (!confirmDelete) {
        toast.info("已取消保存操作");
        return;
      }
    }

    setShowLoadingModal(true);
    try {
      // 先删除不再存在的组件
      if (deletedContentBaseIds.length > 0) {
        const deletePromises = deletedContentBaseIds.map((content_base_id) =>
          fetch(`/api/manage/content/delete`, {
            method: "POST",
            body: JSON.stringify({ content_base_id }),
          })
        );

        const deleteResponses = await Promise.all(deletePromises);
        const deleteResults = await Promise.all(deleteResponses.map((res) => res.json()));

        const hasDeleteError = deleteResults.some((result) => result.code !== 200);
        if (hasDeleteError) {
          toast.error("部分组件删除失败", {
            containerId: "toast-container2",
          });
        }
      }

      // 获取所有当前section的ID列表
      const currentSectionIds = sortedSections.map((section) => section.content_base_id);

      // 先更新所有组件的排序
      const updateSortPromises = sortedSections.map((section, index) =>
        fetch(`/api/manage/content/updateSort`, {
          method: "POST",
          body: JSON.stringify({
            content_base_id: section.content_base_id,
            content_sort: index,
          }),
        })
      );

      await Promise.all(updateSortPromises);

      // 然后保存内容
      const promises = sortedSections.map((section, index) => {
        if (!section?.content_base_id) {
          return Promise.reject(new Error("Invalid section data"));
        }

        const requestData = {
          content_base_id: section.content_base_id,
          content_json: section.content_json || {},
          content_styles: section.content_styles || {},
          section_name: section.section_name || "",
          page_url: pageData.page_url,
          content_sort: index, // 使用循环索引作为排序值
          isFirstSection: index === 0,
          currentSectionIds: index === 0 ? currentSectionIds : undefined,
        };

        return fetch(`/api/manage/content/saveContentJson`, {
          method: "POST",
          body: JSON.stringify(requestData),
        });
      });

      const responses = await Promise.all(promises);
      const results = await Promise.all(responses.map((res) => res.json()));

      // 更新临时ID为新的永久ID
      const newSections = sortedSections.map((section, index) => {
        if (section.content_base_id.startsWith("temp-") && results[index].data?.content_base_id) {
          return {
            ...section,
            content_base_id: results[index].data.content_base_id,
          };
        }
        return section;
      });

      // 更新本地状态，保持排序
      setSections(newSections);

      const hasError = results.some((result) => result.code !== 200);
      if (!hasError) {
        toast.success("所有内容保存成功");
      } else {
        toast("部分内容保存失败", {
          type: "error",
          containerId: "toast-container2",
        });
      }
    } catch (error) {
      console.error("Save error:", error);
      toast("保存失败", {
        type: "error",
        containerId: "toast-container2",
      });
    } finally {
      setShowLoadingModal(false);
    }
  };

  // 处理排序更改
  const handleSortChange = async (sectionIndex, newSort) => {
    if (!sections[sectionIndex]) {
      toast("无效的内容索引", {
        type: "error",
        containerId: "toast-container2",
      });
      return;
    }

    const section = sections[sectionIndex];
    try {
      // 创建新的排序数组
      const newSections = [...sections];
      const oldSort = section.content_sort;
      const newSortValue = parseInt(newSort) || 0;

      // 更新目标section的排序值
      newSections[sectionIndex] = {
        ...section,
        content_sort: newSortValue,
      };

      // 重新排序整个数组
      newSections.sort((a, b) => (a?.content_sort || 0) - (b?.content_sort || 0));

      // 更新状态
      setSections(newSections);

      // 不立即发送请求到服务器，等待用户保存
      toast.success("排序已更新，请点击保存按钮以保存更改");
    } catch (error) {
      console.error("Sort error:", error);
      toast.error("排序更新失败");
      setSections(sections); // 发生错误时回滚状态
    }
  };

  const getSectionList = async () => {
    try {
      const response = await fetch("/api/manage/section/getPublishList");
      const data = await response.json();
      if (data.code === 200) {
        console.log("获取组件列表成功:", data.resultList);
        setAvailableSections(data.resultList);
      }
    } catch (error) {
      console.error("获取组件列表失败:", error);
      toast.error("获取组件列表失败");
    }
  };

  useEffect(() => {
    getSectionList();
    getSectionTypeList();
  }, []);

  const handleAddSection = async (sectionName) => {
    try {
      const response = await fetch("/api/manage/content/addPageContent", {
        method: "POST",
        body: JSON.stringify({
          page_url: pageData.page_url,
          section_name: sectionName,
          content_sort: sections.length,
        }),
      });
      const result = await response.json();
      if (result.code === 200) {
        setSections([...sections, result.data]);
        toast.success("添加组件成功");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("添加组件失败:", error);
      toast.error("添加组件失败");
    }
  };

  // 删除组件
  const handleDeleteContent = async (content) => {
    try {
      const response = await fetch(`/api/manage/content/delete`, {
        method: "POST",
        body: JSON.stringify({
          content_base_id: content.content_base_id,
        }),
      });

      const result = await response.json();
      if (result.code === 200) {
        const newSections = sections.filter((section) => section.content_base_id !== content.content_base_id);
        setSections(newSections);
        toast.success("删除组件成功");
      } else {
        toast.error("删除组件失败");
      }
    } catch (error) {
      console.error("删除组件失败:", error);
      toast.error("删除组件失败");
    }
  };
  // // 添加自动保存间隔配置
  // const AUTO_SAVE_INTERVAL = 5 * 60 * 1000;

  // // 设置自动保存
  // useInterval(() => {
  //   if (!sections || sections.length === 0) return;

  //   saveAllContent();
  //   const now = new Date().toLocaleTimeString();
  //   setLastSaveTime(now);
  // }, AUTO_SAVE_INTERVAL);

  // 添加页面关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      const message = "离开页面将导致未保存的内容丢失。确定要离开吗？";
      e.preventDefault();
      e.returnValue = message;
      return message;
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  // 处理删除确认
  const handleConfirmDelete = async () => {
    if (!contentToDelete) return;
    await handleDeleteContent(contentToDelete);
  };

  const [isPublishing, setIsPublishing] = useState(false);

  const handleSectionClick = (section, index, fromSidebar = false) => {
    // 设置选中的 section
    setSelectedSection(index);

    // 如果是从侧边栏点击，需要滚动编辑区域
    if (fromSidebar) {
      const editElement = document.getElementById(`edit-${section.content_base_id}`);
      if (editElement) {
        // 获取编辑区域的容器
        const editContainer = editElement.closest(".overflow-y-auto");
        if (editContainer) {
          editContainer.scrollTo({
            top: editElement.offsetTop - 32, // 32px 是顶部空间
            behavior: "smooth",
          });
        }
      }
    }

    // 滚动预览区域（原有逻辑）
    const previewElement = document.getElementById(`preview-${section.content_base_id}`);
    if (previewElement) {
      const headerOffset = 32;
      const elementPosition = previewElement.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.scrollY - headerOffset;

      const previewContainer = previewElement.closest(".overflow-y-auto");
      if (previewContainer) {
        previewContainer.scrollTo({
          top: previewElement.offsetTop - headerOffset,
          behavior: "smooth",
        });
      }
    }
  };

  const handlePublishAll = async () => {
    if (!sections || sections.length === 0) {
      toast.warning("没有可发布的内容");
      return;
    }

    // 在发布前也执行同样的检查
    const savedContentBaseIds = contentSection.filter((section) => section && !section.is_delete).map((section) => section.content_base_id);

    const currentContentBaseIds = sections.map((section) => section.content_base_id).filter((id) => !id.startsWith("temp-"));

    const deletedContentBaseIds = savedContentBaseIds.filter((id) => !currentContentBaseIds.includes(id));

    if (deletedContentBaseIds.length > 0 && editMode === "json") {
      const confirmDelete = window.confirm(`您在JSON中删除了${deletedContentBaseIds.length}个已保存的组件。发布后这些组件将被永久删除，确定要继续吗？`);

      if (!confirmDelete) {
        toast.info("已取消发布操作");
        return;
      }

      // 如果确认发布，先执行保存操作以确保删除被处理
      await saveAllContent();
    } else {
      await saveAllContent();
    }

    setIsPublishing(true);
    try {
      const promises = sections.map((section) => {
        if (!section?.content_base_id) {
          return Promise.reject(new Error("Invalid section data"));
        }

        return fetch(`/api/manage/content/publishContentByUid`, {
          method: "POST",
          body: JSON.stringify({
            content_base_id: section.content_base_id,
          }),
        });
      });

      const responses = await Promise.all(promises);
      const results = await Promise.all(responses.map((res) => res.json()));

      const hasError = results.some((result) => result.code !== 200);
      if (!hasError) {
        console.log("pageData", pageData);
        // 所有内容发布成功后,发布页面
        const pageResponse = await fetch(`/api/manage/page/changeStatus`, {
          method: "POST",
          body: JSON.stringify({
            id: pageData.id,
            status: 1,
          }),
        });
        const pageResult = await pageResponse.json();

        if (pageResult.code === 200) {
          toast.success("所有内容和页面发布成功");
        } else {
          toast.error("内容发布成功但页面发布失败");
        }
      } else {
        toast.error("部分内容发布失败");
      }
    } catch (error) {
      console.error("Publish error:", error);
      toast.error("发布失败");
    } finally {
      setIsPublishing(false);
    }
  };
  // 根据组件类型获取背景色
  const getBackgroundColor = (index: number) => {
    return index % 2 === 0 ? "#FBFBFF" : "#FFFFFF";
  };

  // 添加图片选择模态框相关状态
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [currentEditingSection, setCurrentEditingSection] = useState(null);
  const [currentEditingKey, setCurrentEditingKey] = useState("");
  const [currentImageType, setCurrentImageType] = useState<"icon" | "painting">("painting");
  const [currentArrayIndex, setCurrentArrayIndex] = useState<number | null>(null);
  const [currentItemKey, setCurrentItemKey] = useState<string | null>(null);

  // 打开图片选择模态框
  const openImageModal = (sectionIndex: number, key: string, isIcon: boolean = false, arrayIndex: number = null, itemKey: string = null) => {
    setCurrentEditingSection(sectionIndex);
    setCurrentEditingKey(key);
    setCurrentImageType(isIcon ? "icon" : "painting");
    setCurrentArrayIndex(arrayIndex);
    setCurrentItemKey(itemKey);
    setIsImageModalOpen(true);
  };

  // 选择图片后的处理
  const handleImageSelect = (imageUrl: string) => {
    if (currentEditingSection === null || !currentEditingKey) return;

    const newSections = [...sections];
    const section = newSections[currentEditingSection];

    if (!section) return;

    // 如果是数组中的项目
    if (currentArrayIndex !== null && currentItemKey) {
      // 确保数组存在
      const items = [...(section.content_json?.[currentEditingKey] || [])];

      if (items[currentArrayIndex]) {
        items[currentArrayIndex] = {
          ...items[currentArrayIndex],
          [currentItemKey]: imageUrl,
        };

        newSections[currentEditingSection].content_json = {
          ...(section.content_json || {}),
          [currentEditingKey]: items,
        };
      }
    } else {
      // 直接字段
      newSections[currentEditingSection].content_json = {
        ...(section.content_json || {}),
        [currentEditingKey]: imageUrl,
      };
    }

    setSections(newSections);
    setIsImageModalOpen(false);
  };

  if (!whiteUser) {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page={pagePath} />
      </>
    );
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />

      <div className="flex h-[calc(100vh-64px)]">
        {/* 修改后的左侧面板 */}
        <div className="w-64 border-r border-gray-200 bg-white h-full overflow-y-auto">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium text-gray-900">已选组件</h3>
            </div>

            {/* 已选组件列表 */}
            <div className="space-y-2">
              {sections
                .sort((a, b) => (a?.content_sort || 0) - (b?.content_sort || 0))
                .map((section, index) => (
                  <div
                    key={section?.content_base_id || index}
                    className={`p-3 border rounded hover:bg-gray-50 cursor-pointer ${selectedSection === index ? "ring-2 ring-indigo-500" : ""}`}
                    onClick={() => handleSectionClick(section, index, true)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-600">{section?.content_json?.title || section?.content_json?.heading || "未设置标题"}</span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setContentToDelete(section);
                          setDeleteModalOpen(true);
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </div>

                    <div className="mt-1 text-xs font-semibold text-gray-500 truncate">{section?.section_name || ""}</div>
                  </div>
                ))}
            </div>

            {/* 新增组件按钮 */}
            <button
              onClick={() => setIsComponentModalOpen(true)}
              className="mt-4 w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              新增组件
            </button>
          </div>
        </div>

        {/* 组件选择弹窗 */}
        <Modal isOpen={isComponentModalOpen} onClose={() => setIsComponentModalOpen(false)} title="选择组件">
          <div className="grid grid-cols-3 gap-4 p-4">
            {availableSections.map((section) => (
              <div
                key={section.id}
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => {
                  handleAddSection(section.section_name);
                  setIsComponentModalOpen(false);
                }}
              >
                <img src={`${process.env.NEXT_PUBLIC_STORAGE_URL}/images/${section.section_name.toLowerCase()}.png`} alt={section.section_name} className="w-full h-36 object-cover rounded-lg mb-3" />
                <div className="font-medium text-gray-700">{section.section_name}</div>
              </div>
            ))}
          </div>
        </Modal>

        {/* 原有的编辑和预览区域 */}
        <div className="flex flex-1">
          {/* 编辑区域 */}
          <div className="w-1/2 overflow-y-auto border-r border-gray-200 bg-white text-gray-900">
            <div className="p-4">
              <nav className="mb-4">
                <ol className="flex items-center space-x-2 text-sm text-gray-500">
                  <li>
                    <a href="/manage/page" className="hover:text-gray-700">
                      页面管理
                    </a>
                  </li>
                  <li>/</li>
                  <li>
                    <a href={`/manage/page/${page}`} className="hover:text-gray-700">
                      {pageData?.page_name || "未命名页面"}
                    </a>
                  </li>
                  <li>/</li>
                  <li className="text-indigo-600 font-medium">可视化编辑器</li>
                </ol>
              </nav>

              <div className="space-y-6">
                {/* 编辑模式切换 */}
                <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-2 flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <h2 className="text-lg font-medium text-gray-900">{editMode === "form" ? "表单编辑模式" : "JSON 编辑模式"}</h2>
                    {editMode === "json" && (
                      <button
                        onClick={() => {
                          setJsonContent(JSON.stringify(initPageJson, null, 2));
                          // 同时更新预览
                          const newSections = initPageJson.map((item: any, index: number) => ({
                            section_name: item.type,
                            content_json: item.props,
                            content_base_id: sections[index]?.content_base_id || `temp-${index}`,
                            content_sort: index,
                            section_json: sectionList?.find?.((section: any) => section.section_name === item.type)?.section_json,
                            is_delete: false,
                          }));
                          console.log("newSections>>>>", newSections);
                          setSections(newSections);
                        }}
                        className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                      >
                        添加初始化 JSON
                      </button>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm ${editMode === "form" ? "text-blue-600" : "text-gray-500"}`}>表单编辑</span>
                    <button
                      onClick={() => setEditMode(editMode === "form" ? "json" : "form")}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${editMode === "json" ? "bg-blue-600" : "bg-gray-200"}`}
                    >
                      <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${editMode === "json" ? "translate-x-6" : "translate-x-1"}`} />
                    </button>
                    <span className={`text-sm ${editMode === "json" ? "text-blue-600" : "text-gray-500"}`}>JSON编辑</span>
                  </div>
                </div>

                {/* 编辑区域内容 */}
                <div className="p-4">
                  {editMode === "json" ? (
                    <JsonEditor value={jsonContent} onChange={handleJsonChange} />
                  ) : (
                    // 原有的表单编辑界面保持不变
                    <div>
                      {sections.map((section, index) => (
                        <div
                          id={`edit-${section?.content_base_id}`}
                          key={section?.content_base_id || index}
                          className={`border rounded-lg p-6 relative group ${selectedSection === index ? "ring-2 ring-indigo-500" : ""}`}
                          onClick={() => handleSectionClick(section, index)}
                        >
                          {/* 删除按钮 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setContentToDelete(section || "");
                              setDeleteModalOpen(true);
                            }}
                            className="absolute top-0 right-2 text-red-500 hover:text-red-700 font-bold text-lg opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            x
                          </button>

                          <div className="flex items-center justify-between mb-4">
                            <h3 className="font-medium text-gray-900">
                              {section?.section_name || "未命名区块"}
                              <span className="ml-2 text-sm text-gray-500">({section?.section_type || "未知类型"})</span>
                            </h3>
                            <input type="number" value={section?.content_sort || 0} onChange={(e) => handleSortChange(index, e.target.value)} className="w-16 px-2 py-1 border rounded" />
                          </div>

                          {/* 根据section_json定义的结构生成编辑表单 */}
                          <div className="space-y-4">
                            {section?.section_json &&
                              Object.entries(section.section_json).map(([key, type]) => (
                                <div key={key} className="space-y-2">
                                  <label className="text-sm font-medium text-gray-700 flex items-center">
                                    <span className="bg-gray-100 px-2 py-1 rounded">{key}</span>
                                  </label>
                                  {key === "type" && (
                                    <select
                                      value={section?.content_json?.[key] || "all"}
                                      onChange={(e) => {
                                        const newSections = [...sections];
                                        if (newSections[index]) {
                                          newSections[index].content_json = {
                                            ...(section?.content_json || {}),
                                            [key]: e.target.value,
                                          };
                                          setSections(newSections);
                                        }
                                      }}
                                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-base"
                                    >
                                      {section?.section_name !== "IframeSection" && <option value="all">All</option>}
                                      <option value="quiz">Quiz</option>
                                      <option value="flashcard">Flashcard</option>
                                      <option value="notes">Notes</option>
                                    </select>
                                  )}

                                  {type === "string" && key !== "type" && (key === "imageUrl" || key === "author_image") ? (
                                    <div className="flex flex-col space-y-2">
                                      {section?.content_json?.[key] && (
                                        <div className="relative w-full h-32 bg-gray-100 rounded-md overflow-hidden">
                                          <img src={section?.content_json?.[key]} alt={key} className="w-full h-full object-contain" />
                                        </div>
                                      )}
                                      <button
                                        type="button"
                                        onClick={() => openImageModal(index, key, false)}
                                        className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                      >
                                        {section?.content_json?.[key] ? "更换图片" : "选择图片"}
                                      </button>
                                    </div>
                                  ) : type === "string" && key !== "type" && key === "iconUrl" ? (
                                    <div className="flex flex-col space-y-2">
                                      {section?.content_json?.[key] && (
                                        <div className="relative w-32 h-32 bg-gray-100 rounded-md overflow-hidden mx-auto">
                                          <img src={section?.content_json?.[key]} alt={key} className="w-full h-full object-contain" />
                                        </div>
                                      )}
                                      <button
                                        type="button"
                                        onClick={() => openImageModal(index, key, true)}
                                        className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                      >
                                        {section?.content_json?.[key] ? "更换图标" : "选择图标"}
                                      </button>
                                    </div>
                                  ) : type === "string" && key !== "type" ? (
                                    <textarea
                                      value={section?.content_json?.[key] || ""}
                                      onChange={(e) => {
                                        const newSections = [...sections];
                                        if (newSections[index]) {
                                          newSections[index].content_json = {
                                            ...(section?.content_json || {}),
                                            [key]: e.target.value.replace(/\\n/g, "\n"),
                                          };
                                          setSections(newSections);
                                        }
                                      }}
                                      ref={(textArea) => {
                                        if (textArea) {
                                          textArea.style.height = "auto";
                                          textArea.style.height = textArea.scrollHeight + "px";
                                        }
                                      }}
                                      className="py-3 px-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-base custom-textarea whitespace-pre-wrap"
                                    />
                                  ) : null}

                                  {Array.isArray(type) && (
                                    <div className="space-y-4">
                                      {section?.content_json?.[key]?.map((item, itemIndex) => (
                                        <div key={itemIndex} className="p-4 border rounded-lg">
                                          {Object.entries(type[0]).map(([itemKey, itemType]) => (
                                            <div key={itemKey} className="mt-2">
                                              <label className="block text-sm font-medium text-gray-700">{itemKey}</label>
                                              {itemKey === "imageUrl" || itemKey === "author_image" || itemKey === "backgroundImage" ? (
                                                <div className="space-y-2">
                                                  {item[itemKey] && (
                                                    <div className="relative w-full h-32 bg-gray-100 rounded-md overflow-hidden">
                                                      <img src={item[itemKey]} alt={itemKey} className="w-full h-full object-contain" />
                                                    </div>
                                                  )}
                                                  <button
                                                    type="button"
                                                    onClick={() => openImageModal(index, key, false, itemIndex, itemKey)}
                                                    className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                  >
                                                    {item[itemKey] ? "更换图片" : "选择图片"}
                                                  </button>
                                                </div>
                                              ) : itemKey === "iconUrl" || itemKey === "icon" ? (
                                                <div className="space-y-2">
                                                  {item[itemKey] && (
                                                    <div className="relative w-32 h-32 bg-gray-100 rounded-md overflow-hidden mx-auto">
                                                      <img src={item[itemKey]} alt={itemKey} className="w-full h-full object-contain" />
                                                    </div>
                                                  )}
                                                  <button
                                                    type="button"
                                                    onClick={() => openImageModal(index, key, true, itemIndex, itemKey)}
                                                    className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                  >
                                                    {item[itemKey] ? "更换图标" : "选择图标"}
                                                  </button>
                                                </div>
                                              ) : itemKey === "beforeImage" || itemKey === "afterVideo" ? (
                                                <div className="space-y-2">
                                                  <input
                                                    type="text"
                                                    value={item[itemKey] || ""}
                                                    onChange={(e) => {
                                                      const newSections = [...sections];
                                                      if (newSections[index]) {
                                                        const newItems = [...(section?.content_json?.[key] || [])];
                                                        newItems[itemIndex] = {
                                                          ...newItems[itemIndex],
                                                          [itemKey]: e.target.value,
                                                        };
                                                        newSections[index].content_json = {
                                                          ...(section?.content_json || {}),
                                                          [key]: newItems,
                                                        };
                                                        setSections(newSections);
                                                      }
                                                    }}
                                                    className="py-3 px-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-base"
                                                    placeholder={`输入${itemKey === "beforeImage" ? "图片" : "视频"}URL`}
                                                  />
                                                  {item[itemKey] && (
                                                    <div className="mt-2">
                                                      {itemKey === "beforeImage" ? (
                                                        <img src={item[itemKey]} alt="Preview" className="max-w-xs h-auto rounded" />
                                                      ) : (
                                                        <video src={item[itemKey]} controls className="max-w-xs h-auto rounded" />
                                                      )}
                                                    </div>
                                                  )}
                                                </div>
                                              ) : itemType === "selector" ? (
                                                <select
                                                  title="选择question的层级"
                                                  value={String(item[itemKey]) || "h4"}
                                                  onChange={(e) => {
                                                    const newSections = [...sections];
                                                    if (newSections[index]) {
                                                      const newItems = [...(section?.content_json?.[key] || [])];
                                                      newItems[itemIndex] = {
                                                        ...newItems[itemIndex],
                                                        [itemKey]: e.target.value,
                                                      };
                                                      newSections[index].content_json = {
                                                        ...(section?.content_json || {}),
                                                        [key]: newItems,
                                                      };
                                                      setSections(newSections);
                                                    }
                                                  }}
                                                  className="py-3 px-2 border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm rounded-lg block w-full"
                                                >
                                                  <option value="h2">h2</option>
                                                  <option value="h3">h3</option>
                                                  <option value="h4">h4</option>
                                                </select>
                                              ) : (
                                                <textarea
                                                  value={item[itemKey] || ""}
                                                  onChange={(e) => {
                                                    const newSections = [...sections];
                                                    if (newSections[index]) {
                                                      const newItems = [...(section?.content_json?.[key] || [])];
                                                      newItems[itemIndex] = {
                                                        ...newItems[itemIndex],
                                                        [itemKey]: e.target.value,
                                                      };
                                                      newSections[index].content_json = {
                                                        ...(section?.content_json || {}),
                                                        [key]: newItems,
                                                      };
                                                      setSections(newSections);
                                                    }
                                                  }}
                                                  ref={(textArea) => {
                                                    if (textArea) {
                                                      textArea.style.height = "auto";
                                                      textArea.style.height = textArea.scrollHeight + "px";
                                                    }
                                                  }}
                                                  className="py-3 px-2 mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-base"
                                                />
                                              )}
                                            </div>
                                          ))}
                                          <button
                                            onClick={() => {
                                              const newSections = [...sections];
                                              if (newSections[index]) {
                                                const newItems = section?.content_json?.[key]?.filter((_, i) => i !== itemIndex) || [];
                                                newSections[index].content_json = {
                                                  ...(section?.content_json || {}),
                                                  [key]: newItems,
                                                };
                                                setSections(newSections);
                                              }
                                            }}
                                            className="mt-2 text-red-600 hover:text-red-800"
                                          >
                                            删除
                                          </button>
                                        </div>
                                      ))}
                                      <button
                                        onClick={() => {
                                          const newSections = [...sections];
                                          if (newSections[index]) {
                                            const emptyItem = {};
                                            Object.keys(type[0]).forEach((k) => {
                                              emptyItem[k] = "";
                                            });
                                            newSections[index].content_json = {
                                              ...(section?.content_json || {}),
                                              [key]: [...(section?.content_json?.[key] || []), emptyItem],
                                            };
                                            setSections(newSections);
                                          }
                                        }}
                                        className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-full shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                      >
                                        添加新项
                                      </button>
                                    </div>
                                  )}
                                </div>
                              ))}
                            <div className="mt-4 space-y-4">
                              <div
                                className="flex items-center justify-between cursor-pointer"
                                onClick={() =>
                                  setExpandedStyles((prev) => ({
                                    ...prev,
                                    [section.content_base_id]: !prev[section.content_base_id],
                                  }))
                                }
                              >
                                <h4 className="text-sm font-medium text-gray-700">样式配置</h4>
                                <svg className={`w-5 h-5 transition-transform ${expandedStyles[section.content_base_id] ? "rotate-180" : ""}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                              </div>

                              {expandedStyles[section.content_base_id] &&
                                Object.entries(section?.styles || {})?.map?.(([styleKey, styleType]) => (
                                  <div key={styleKey} className="flex flex-col gap-2">
                                    <label className="text-sm font-medium text-gray-700 flex items-center">
                                      <span className="bg-gray-100 px-2 py-1 rounded">{styleKey}</span>
                                    </label>
                                    <div className="relative flex items-start ml-2">
                                      <div
                                        className="w-12 h-12 rounded cursor-pointer border border-gray-300"
                                        style={{
                                          backgroundColor: section?.content_styles?.[styleKey] || "#ffffff",
                                        }}
                                        onClick={() => setOpenColorPicker(styleKey)}
                                      />
                                      <div className="mt-2 flex items-center justify-between ml-4">
                                        <input
                                          type="text"
                                          value={section?.content_styles?.[styleKey] || ""}
                                          onChange={(e) => {
                                            const newSections = [...sections];
                                            newSections[index] = {
                                              ...section,
                                              content_styles: {
                                                ...(section?.content_styles || {}),
                                                [styleKey]: e.target.value,
                                              },
                                            };
                                            setSections(newSections);
                                          }}
                                          className="w-full text-base px-2 py-1 border rounded"
                                        />
                                      </div>
                                      {openColorPicker === styleKey && (
                                        <div className="absolute left-0 top-full mt-2 z-20">
                                          <div className="fixed inset-0" onClick={() => setOpenColorPicker(null)} />
                                          <div className="relative">
                                            <HexColorPicker
                                              className="w-64 h-64 z-50"
                                              color={section?.content_styles?.[styleKey] || "#ffffff"}
                                              onChange={(e) => {
                                                const newSections = [...sections];
                                                newSections[index] = {
                                                  ...section,
                                                  content_styles: {
                                                    ...(section?.content_styles || {}),
                                                    [styleKey]: e,
                                                  },
                                                };
                                                setSections(newSections);
                                              }}
                                            />
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                ))}
                            </div>
                            {(section.section_name === "ListSlider" || section.section_name === "List") && publishedPageList?.length > 0 && (
                              <div className="mt-8 border-t border-gray-200 pt-8">
                                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-4">选择要展示的已发布页面</h3>
                                <div className="space-y-4">
                                  {publishedPageList.map((item) => (
                                    <div key={item.id} className="flex items-center">
                                      <input
                                        type="checkbox"
                                        name="page"
                                        id={`page-${item.id}`}
                                        checked={section.content_json?.items?.some((i) => i.link === `/${pagePage}/${item.id}`)}
                                        onChange={(e) => {
                                          const newSections = [...sections];
                                          if (e.target.checked) {
                                            // 添加新项
                                            const newItem = {
                                              name: item.page_name,
                                              description: item.page_description || "",
                                              banner: item.page_banner || "",
                                              link: `/${pagePage}/${item.id}`,
                                            };
                                            newSections[index] = {
                                              ...section,
                                              content_json: {
                                                ...(section?.content_json || {}),
                                                items: [...(section?.content_json?.items || []), newItem],
                                              },
                                            };
                                          } else {
                                            // 移除项
                                            newSections[index] = {
                                              ...section,
                                              content_json: {
                                                ...(section?.content_json || {}),
                                                items: (section?.content_json?.items || []).filter((i) => i.link !== `/${pagePage}/${item.id}`),
                                              },
                                            };
                                          }
                                          setSections(newSections);
                                        }}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                      />
                                      <label htmlFor={`page-${item.id}`} className="ml-3 block text-sm font-medium">
                                        <span className="text-gray-700">
                                          {pagePage}/{item.page_url}
                                        </span>
                                        <span className="text-blue-600 ml-2">({item.page_name})</span>
                                        <span className={`ml-2 ${item.show_in_index_page ? "text-purple-600" : "text-gray-500"}`}>[{item.show_in_index_page ? "首页Game" : "非首页Game"}]</span>
                                      </label>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="mt-4 flex justify-center items-center gap-4">
                    <button
                      onClick={saveAllContent}
                      disabled={!sections || sections.length === 0}
                      className="px-8 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      保存所有内容
                    </button>
                    <button
                      onClick={handlePublishAll}
                      disabled={!sections || sections.length === 0 || isPublishing}
                      className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      {isPublishing ? "发布中..." : "一键发布"}
                    </button>
                    {/* {lastSaveTime && (
                    <span className="text-sm text-gray-500">上次保存时间: {lastSaveTime}</span>
                  )} */}
                  </div>
                </div>
              </div>
            </div>
          </div>
          ;{/* 预览区域 */}
          <div className="w-1/2 overflow-y-auto ">
            <div className="sticky top-0 bg-yellow-100 p-2 text-center text-sm">预览模式 - {pageData?.page_name || "未命名页面"}</div>
            <div
              className="preview-container  bg-indigo-100"
              style={{
                ...(websiteBaseConfig?.styles?.backgroundImage && {
                  backgroundImage: `url(${websiteBaseConfig?.styles?.backgroundImage})`,
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }),
              }}
            >
              {sections.map((section, index) => {
                if (!section?.section_name) return null;
                const Component = componentMap[section.section_name];
                if (section.section_name === "Navbar") {
                  return (
                    <Component
                      key={section.content_base_id}
                      locale={locale}
                      type={section.content_json?.type}
                      landingInfo={section.content_json}
                      styles={{ backgroundColor: getBackgroundColor(index) }}
                    />
                  );
                }
                return Component ? (
                  <div id={`preview-${section.content_base_id}`} key={section.content_base_id}>
                    <Component landingInfo={section.content_json || {}} styles={{ backgroundColor: getBackgroundColor(index) }} />
                  </div>
                ) : null;
              })}
            </div>
          </div>
          ;
        </div>
      </div>

      {/* 使用 ConfirmModal 替换原有的 Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setContentToDelete(null);
        }}
        onConfirm={handleConfirmDelete}
        message="确定要删除这个组件吗？此操作无法撤销。"
      />

      {/* 添加模态框组件在页面底部 */}
      <ImageModal isOpen={isImageModalOpen} onClose={() => setIsImageModalOpen(false)} onSelect={handleImageSelect} imageType={currentImageType} />
    </>
  );
};

export default PageEditor;
