'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useInterval } from "ahooks";
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { manageLandingPage, pagePage } from "~/configs/globalConfig";
import { toast } from "react-toastify";
import { getContentTypeByFileExtension } from "~/utils/fileType";
import ConfirmModal from "~/components/common/ConfirmModal";
import CopyToClipboard from "~/components/common/CopyToClipboard";

const PageComponent = ({
  locale,
}) => {
  const router = useRouter();
  const [pagePath] = useState(manageLandingPage);

  const {
    setShowLoadingModal,
    userData
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => {
    }
  }, []);

  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  const checkUser = async () => {
    if (!userData?.user_id) {

    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        getPageList(1);
      }
    }
  }
  useInterval(() => {
    checkUser();
  }, intervalCheckUser);

  const [pageData, setPageData] = useState([]);
  const [totalPage, setTotalPage] = useState(0);
  const [countTotal, setCountTotal] = useState(0);

  const getPageList = async (page) => {
    if (!checkAdminUser(userData)) {
      return;
    }
    setShowLoadingModal(true);
    const requestData = {
      positionPage: page,
      page_url: searchPageUrl
    }
    const response = await fetch(`/api/manage/page/getList`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    })
    const result = await response.json();
    if (result.code === 200) {
      setPageData(result.resultList);
      setTotalPage(result.totalPage);
      setCountTotal(result.countTotal);
    } else {
      toast(result.message, {
        type: "error",
      });
    }
    setShowLoadingModal(false);
  }

  const [currentPage, setCurrentPage] = useState(1);
  const [newPageUrl, setNewPageUrl] = useState('');
  const [newPageTitle, setNewPageTitle] = useState('');
  const [newPageDescription, setNewPageDescription] = useState('');
  const [newOutLinkUrl, setNewOutLinkUrl] = useState('');
  const [newPageName, setNewPageName] = useState('');
  const [newPageStatus, setNewPageStatus] = useState(0);
  const [editPageId, setEditPageId] = useState();

  const addNewPage = async () => {
    if (!checkAdminUser(userData)) {
      return;
    }
    if (!newPageUrl || !newPageName) {
      return;
    }
    const requestData = {
      page_url: newPageUrl?.trim(),
      page_title: newPageTitle?.trim(),
      page_description: newPageDescription?.trim(),
      page_name: newPageName?.trim(),
      out_link_url: newOutLinkUrl?.trim(),
      status: newPageStatus,
      page_banner: pageBanner,
      id: editPageId
    }
    setShowLoadingModal(true);
    if (editPageId) {
      const response = await fetch(`/api/manage/page/update`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })
      const result = await response.json();
      if (result.code === 200) {
        await getPageList(currentPage);
        toast(result.message, {
          type: "success",
        });
        clearEditPage();
      } else {
        toast(result.message, {
          type: "error",
        });
      }
    } else {
      const response = await fetch(`/api/manage/page/add`, {
        method: 'POST',
        body: JSON.stringify(requestData)
      })
      const result = await response.json();
      if (result.code === 200) {
        await getPageList(currentPage);
        toast(result.message, {
          type: "success",
        });
        clearEditPage();
      } else {
        toast(result.message, {
          type: "error",
        });
      }
    }
    setShowLoadingModal(false);
  }

  const clearEditPage = () => {
    setEditPageId(undefined);
    setNewPageUrl('');
    setNewPageTitle('');
    setNewPageDescription('');
    setNewOutLinkUrl('');
    setNewPageName('');
    setNewPageStatus(0);
    setPageBanner(null);
  }

  const editPage = async (page) => {
    setNewPageUrl(page.page_url);
    setNewPageTitle(page.page_title);
    setNewPageDescription(page.page_description);
    setNewOutLinkUrl(page.out_link_url);
    setNewPageName(page.page_name);
    setNewPageStatus(page.status);
    setPageBanner(page.page_banner);
    if (page.page_banner) {
      setPageBanner(page.page_banner);
    }
    setEditPageId(page.id);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  const [confirmModalConfig, setConfirmModalConfig] = useState({
    isOpen: false,
    message: "",
    onConfirm: () => {},
    title: "",
  });

  const changePageStatus = async (page) => {
    if (!checkAdminUser(userData)) return;

    const newStatus = page.status === 1 ? 0 : 1;
    const action = newStatus === 1 ? "发布" : "取消发布";

    setConfirmModalConfig({
      isOpen: true,
      title: `确认${action}`,
      message: `确定要${action}该页面吗?`,
      onConfirm: async () => {
        setShowLoadingModal(true);
        const response = await fetch(`/api/manage/page/changeStatus`, {
          method: "POST",
          body: JSON.stringify({
            id: page.id,
            status: newStatus,
          }),
        });
        const result = await response.json();
        setShowLoadingModal(false);

        if (result.code === 200) {
          await getPageList(currentPage);
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      },
    });
  };

  const prevPage = async () => {
    if (currentPage > 0 && currentPage <= totalPage) {
      const newCurrentPage = currentPage - 1;
      setCurrentPage(newCurrentPage);
      await getPageList(newCurrentPage);
    }
  };

  const nextPage = async () => {
    if (currentPage < totalPage) {
      const newCurrentPage = currentPage + 1;
      setCurrentPage(newCurrentPage);
      await getPageList(newCurrentPage);
    }
  };

  const [searchPageUrl, setSearchPageUrl] = useState("");

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deletePage, setDeletePage] = useState(null);

  const deletePageById = async (page) => {
    if (!checkAdminUser(userData)) {
      return;
    }
    if (!page) {
      return;
    }
    const requestData = {
      id: page.id,
    };
    setShowLoadingModal(true);
    const response = await fetch(`/api/manage/page/delete`, {
      method: "POST",
      body: JSON.stringify(requestData),
    });
    const result = await response.json();
    setShowLoadingModal(false);
    if (result.code === 200) {
      await getPageList(currentPage);
      toast(result.message, {
        type: "success",
      });
    }
  };

  const [pageBanner, setPageBanner] = useState(null);

  const getPresignedUrl = async (fileExtension) => {
    const response = await fetch(`/api/upload/generatePresignedUrl?fileExtension=${fileExtension}`);
    const result = await response.json();
    return result;
  };

  const uploadImageToR2 = async (file) => {
    setShowLoadingModal(true);
    const fileExtension = file.type.split("/")[1];
    const presignedUrlObject = await getPresignedUrl(fileExtension);
    const presignedUrl = presignedUrlObject.presignedUrl;
    const contentType = getContentTypeByFileExtension(fileExtension);
    const response = await fetch(presignedUrl, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": contentType,
      },
    });
    const status = await response.status;
    setShowLoadingModal(false);
    const objectUrl = presignedUrlObject.objectUrl;
    setPageBanner(objectUrl);
    if (status !== 200) {
      return false;
    } else {
      return true;
    }
  };

  const setToIndexPage = async (page) => {
    if (page.status !== 1) {
      toast.error("请先发布页面");
      return;
    }

    setConfirmModalConfig({
      isOpen: true,
      title: "设为首页",
      message: "确定要将该页面设为首页吗?",
      onConfirm: async () => {
        setShowLoadingModal(true);
        const response = await fetch(`/api/manage/page/setToIndexPage`, {
          method: "POST",
          body: JSON.stringify({ id: page.id }),
        });
        const result = await response.json();
        setShowLoadingModal(false);

        if (result.code === 200) {
          await getPageList(currentPage);
          toast.success(result.message);
        } else {
          toast.error(result.message);
        }
      },
    });
  };

  if (!whiteUser) {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page={pagePath} />
      </>
    );
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />
      <ConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={() => deletePageById(deletePage)}
        message="Are you sure you want to delete this item?"
      />
      <ConfirmModal
        isOpen={confirmModalConfig.isOpen}
        message={confirmModalConfig.message}
        onClose={() => setConfirmModalConfig(prev => ({ ...prev, isOpen: false }))}
        onConfirm={() => {
          confirmModalConfig.onConfirm();
          setConfirmModalConfig(prev => ({ ...prev, isOpen: false }));
        }}
      />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <div className="flex flex-col items-center justify-center">
          <div className="text-2xl font-bold">游戏管理</div>
        </div>
        <div className="mt-8 w-full max-w-3xl mx-auto px-4">
          <div className="bg-white shadow sm:rounded-lg">
            <div className="px-6 py-6">
              <div className="grid grid-cols-1 gap-8">
                <div className="space-y-4">
                  <label
                    htmlFor="page-url"
                    className="block text-sm font-medium text-gray-700 mb-2 px-1"
                  >
                    page_url（游戏展示路由）
                  </label>
                  <div>
                    <input
                      type="text"
                      name="page-url"
                      id="page-url"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入page_url"
                      value={newPageUrl}
                      onChange={e => setNewPageUrl(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label
                    htmlFor="page-title"
                    className="block text-sm font-medium text-gray-700 mb-2 px-1"
                  >
                    page_title
                  </label>
                  <div>
                    <input
                      type="text"
                      name="page-title"
                      id="page-title"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入page_title"
                      value={newPageTitle}
                      onChange={e => setNewPageTitle(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label
                    htmlFor="page-description"
                    className="block text-sm font-medium text-gray-700 mb-2 px-1"
                  >
                    page_description
                  </label>
                  <div>
                    <input
                      type="text"
                      name="page-description"
                      id="page-description"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入page_description"
                      value={newPageDescription}
                      onChange={e => setNewPageDescription(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <label
                    htmlFor="page-name"
                    className="block text-sm font-medium text-gray-700 mb-2 px-1"
                  >
                    游戏名称
                  </label>
                  <div>
                    <input
                      type="text"
                      name="page-name"
                      id="page-name"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入游戏名称"
                      value={newPageName}
                      onChange={e => setNewPageName(e.target.value)}
                    />
                  </div>
                </div>

                {/* <div className="space-y-4">
                  <label htmlFor="page-url" className="block text-sm font-medium text-gray-700 mb-2 px-1">
                    游戏URL（iframe使用）
                  </label>
                  <div>
                    <input
                      type="text"
                      name="page-url"
                      id="page-url"
                      className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                      placeholder="请输入游戏URL"
                      value={newOutLinkUrl}
                      onChange={(e) => setNewOutLinkUrl(e.target.value)}
                    />
                  </div>
                </div> */}
                {editPageId ? (
                  <div className="space-y-4">
                    <label
                      htmlFor="page-status"
                      className="block text-sm font-medium text-gray-700 mb-2 px-1"
                    >
                      游戏状态
                    </label>
                    <div>
                      <select
                        id="page-status"
                        name="page-status"
                        className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md px-4 py-2"
                        value={newPageStatus}
                        onChange={e => setNewPageStatus(Number(e.target.value))}
                      >
                        <option value="1">发布</option>
                        <option value="0">未发布</option>
                      </select>
                    </div>
                  </div>
                ) : null}

                <div className="space-y-4">
                  <label
                    htmlFor="page-image"
                    className="block text-sm font-medium text-gray-700 mb-2 px-1"
                  >
                    游戏封面
                  </label>
                  <div className="space-y-4">
                    {!pageBanner && (
                      <input
                        type="file"
                        name="page-image"
                        id="page-image"
                        accept="image/*"
                        onChange={async e => {
                          const file = e.target.files[0];
                          await uploadImageToR2(file);
                        }}
                        className="block w-full text-sm text-gray-500
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-md file:border-0
                          file:text-sm file:font-semibold
                          file:bg-indigo-50 file:text-indigo-700
                          hover:file:bg-indigo-100"
                      />
                    )}
                    {pageBanner && (
                      <div className="mt-2">
                        <label htmlFor="page-image" className="cursor-pointer">
                          <img
                            src={pageBanner}
                            alt="预览图片"
                            className="max-w-[200px] h-auto rounded-lg shadow-md hover:opacity-80 transition-opacity"
                          />
                        </label>
                        <input
                          type="file"
                          name="page-image"
                          id="page-image"
                          accept="image/*"
                          onChange={async e => {
                            const file = e.target.files[0];
                            await uploadImageToR2(file);
                          }}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-center w-full pt-4">
                  <button
                    type="button"
                    onClick={addNewPage}
                    className="inline-flex items-center px-24 py-2.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    保 存
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8 w-full max-w-[85%] mx-auto px-4">
          <div className="bg-white px-4 py-3">
            {/* 搜索区域 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <span className="text-sm text-gray-700 mr-2">page_url：</span>
                  <input
                    type="text"
                    className="w-64 px-3 py-2 border border-gray-300 rounded-md text-sm transition-shadow"
                    placeholder="请输入page_url"
                    value={searchPageUrl}
                    onChange={e => setSearchPageUrl(e.target.value)}
                  />
                </div>
                <button
                  onClick={() => getPageList(currentPage)}
                  className="px-4 py-2 border border-gray-300 rounded-md bg-blue-500 text-sm font-medium text-white hover:bg-blue-600 transition-colors"
                >
                  查询
                </button>
              </div>
            </div>

            {/* 分页区域 */}
            <div className="flex items-center justify-between border-t border-gray-200 pt-4">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => prevPage()}
                  className={`mx-auto flex justify-center items-center rounded-md ${
                    currentPage == 1 ? 'bg-gray-400' : 'bg-amber-500'
                  } px-3 py-2 text-md font-semibold text-white shadow-sm`}
                  disabled={currentPage == 1}
                >
                  上一页
                </button>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">第</span>
                  <input
                    type="number"
                    className="w-16 px-3 py-2 border border-gray-300 rounded-md text-sm transition-shadow"
                    value={currentPage}
                    onChange={e => setCurrentPage(Number(e.target.value))}
                  />
                  <span className="text-sm text-gray-700">页</span>
                </div>
                <button
                  onClick={() => nextPage()}
                  className={`mx-auto flex justify-center items-center rounded-md ${
                    currentPage == totalPage ? 'bg-gray-400' : 'bg-amber-400'
                  } px-3 py-2 text-md font-semibold text-white shadow-sm`}
                  disabled={currentPage == totalPage}
                >
                  下一页
                </button>
              </div>
              <div className="text-sm text-gray-700">
                共 {totalPage} 页 ｜ {countTotal} 条
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider"
                    >
                      page_url
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider"
                    >
                      page_title
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider"
                    >
                      page_description
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider"
                    >
                      游戏名称
                    </th>
                    {/* <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 tracking-wider">
                      游戏URL（iframe使用）
                    </th> */}
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                    >
                      状态
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                    >
                      首页Game
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                    >
                      操作
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                    >
                      创建时间
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider"
                    >
                      更新时间
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {pageData?.map(page => (
                    <tr key={page.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {page.page_url}
                          <CopyToClipboard text={page.page_url} />
                        </div>
                      </td>
                      <td className="px-6 py-4" title={page.page_title}>
                        <div className="text-sm font-medium text-gray-900 max-w-xs cursor-pointer max-h-20 overflow-y-auto break-words">
                          {page.page_title}
                          <CopyToClipboard text={page.page_title} />
                        </div>
                      </td>
                      <td className="px-6 py-4" title={page.page_description}>
                        <div className="text-sm font-medium text-gray-900 max-w-xs cursor-pointer max-h-20 overflow-y-auto break-words">
                          {page.page_description}
                          <CopyToClipboard text={page.page_description} />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {page.page_name}
                          <CopyToClipboard text={page.page_name} />
                        </div>
                      </td>
                      {/* <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600 max-w-xs truncate">
                          {page.out_link_url}
                          <CopyToClipboard text={page.out_link_url} />
                        </div>
                      </td> */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        {page.status === 1 ? (
                          <a href={`/${pagePage}/${page.page_url}`} target="_blank">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                              <span className="w-2 h-2 rounded-full mr-2 bg-green-400"></span>
                              已发布
                              <svg
                                className="w-4 h-4 mr-1.5 inline"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                />
                              </svg>
                            </span>
                          </a>
                        ) : (
                          <a href={`/manage/${pagePage}/${page.page_url}`} target="_blank">
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                              <span className="w-2 h-2 rounded-full mr-2 bg-gray-400"></span>
                              未发布
                              <svg
                                className="w-4 h-4 mr-1.5 inline"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                                />
                              </svg>
                            </span>
                          </a>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                            page?.show_in_index_page
                              ? 'bg-blue-100 text-blue-800 border border-blue-200'
                              : 'bg-gray-100 text-gray-800 border border-gray-200'
                          }`}
                        >
                          <span
                            className={`w-2 h-2 rounded-full mr-2 ${
                              page?.show_in_index_page ? 'bg-blue-400' : 'bg-gray-400'
                            }`}
                          ></span>
                          {page.show_in_index_page ? '是' : '否'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <a
                            href={`/manage/${pagePage}/${page.page_url}`}
                            target="_blank"
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-teal-50 text-teal-600 hover:bg-teal-100 border border-teal-600 shadow-sm hover:shadow-md"
                          >
                            <svg
                              className="w-4 h-4 mr-1.5 animate-pulse"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                            预览
                          </a>
                          <button
                            className="inline-flex items-center px-3 py-1.5 border border-indigo-600 text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 transition-colors duration-200"
                            onClick={() => editPage(page)}
                          >
                            <svg
                              className="w-4 h-4 mr-1.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                            编辑
                          </button>
                          <a
                            href={`/manage/page/${page.page_url}/editor`}
                            target="_blank"
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-purple-50 text-purple-600 hover:bg-purple-100 border border-purple-600"
                          >
                            <svg
                              className="w-4 h-4 mr-1.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                            编辑详情
                          </a>
                          {/* <a
                            href={`/manage/page/${page.page_url}`}
                            target="_blank"
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-purple-50 text-purple-600 hover:bg-purple-100 border border-purple-600"
                          >
                            <svg
                              className="w-4 h-4 mr-1.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                            编辑详情
                          </a> */}
                          <button
                            onClick={() => changePageStatus(page)}
                            className={`inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                              page.status === 1
                                ? 'bg-blue-50 text-blue-600 hover:bg-blue-100 border border-blue-600'
                                : 'bg-green-50 text-green-600 hover:bg-green-100 border border-green-600'
                            }`}
                          >
                            <svg
                              className="w-4 h-4 mr-1.5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                              />
                            </svg>
                            {page.status === 1 ? '取消发布' : '设为发布'}
                          </button>
                          {!page.show_in_index_page ? (
                            <button
                              onClick={() => setToIndexPage(page)}
                              className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 border border-blue-600"
                            >
                              设为首页Game
                            </button>
                          ) : null}

                          <button
                            onClick={() => {
                              setDeletePage(page);
                              setIsDeleteModalOpen(true);
                            }}
                            className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 bg-red-50 text-red-600 hover:bg-red-100 border border-red-600"
                          >
                            删除
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">
                          {new Date(page.created_at).toLocaleString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-600">
                          {new Date(page.updated_at).toLocaleString()}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default PageComponent
