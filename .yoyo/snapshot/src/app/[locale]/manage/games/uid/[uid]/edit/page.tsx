import { setRequestLocale } from 'next-intl/server';
import GameFormComponent from '~/components/manage/games/GameFormComponent';

export default async function EditGameByUidPage(props) {
  // Make sure to await the params before using them
  const params = await props.params;
  const locale = params?.locale || '';
  const uid = params?.uid || '';

  setRequestLocale(locale);

  return (
    <GameFormComponent locale={locale} gameUid={uid} />
  );
} 