import { getDistinctUserFrom } from "~/servers/manage/manageOrder";
import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';

export default async function PayproPaySuccessPage(props) {
  const params = await props.params;
  const {
    locale = ''
  } = params;
  // Enable static rendering
  setRequestLocale(locale);

  const listLoginFrom = await getDistinctUserFrom();

  return (
    <PageComponent
      locale={locale}
      listLoginFrom={listLoginFrom}
    />
  )
}
