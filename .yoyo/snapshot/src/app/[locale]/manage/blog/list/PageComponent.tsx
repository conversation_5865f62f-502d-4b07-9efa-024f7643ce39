"use client";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";
import ListItem from "./components/ListItem";
import PaginationComponent from "./components/PaginationComponent";

const PageComponent = ({ locale, pageData }) => {
  const [pagePath] = useState(process.env.NEXT_PUBLIC_MANAGE_BLOG_LIST);
  const [activeTab, setActiveTab] = useState("my"); // 'my' 或 'all'

  const { setShowLoadingModal } = useCommonContext();

  useEffect(() => {
    setShowLoadingModal(false);
    getBlogList();
    return () => {};
  }, [activeTab]); // 直接使用useEffect监听activeTab的变化

  const [blogList, setBlogList] = useState([]);

  const getBlogList = async () => {
    const body = {
      generator: "",
      pageSize: 20,
    };

    const endpoint = activeTab === "my" ? "/api/blog/getBlogByUser" : "/api/blog/getBlogByAdmin";

    const res = await fetch(endpoint, {
      method: "POST",
      body: JSON.stringify(body),
    });
    const result = await res.json();
    setBlogList(result.data);
  };

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />

      <div className="flex flex-col mt-3 md:mt-8 w-[80%] xl:w-[60%] mx-auto max-w-6xl min-h-[90vh]">
        <div className="flex border-b border-gray-200 mb-6 px-4 lg:px-8">
          <button
            onClick={() => setActiveTab("my")}
            className={`px-6 py-3 font-medium text-lg border-b-2 ${
              activeTab === "my" ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            我的博客
          </button>
          <button
            onClick={() => setActiveTab("all")}
            className={`px-6 py-3 font-medium text-lg border-b-2 ${
              activeTab === "all" ? "border-blue-500 text-blue-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            所有博客
          </button>
        </div>

        <div role="list" className="w-full px-4 lg:px-8">
          {blogList?.map((blog, index) => (
            <div key={blog.uid + index} className="w-full">
              <ListItem item={blog} getBlogList={getBlogList} status={activeTab} />
            </div>
          ))}
        </div>
        <div className="my-5 w-full">
          <PaginationComponent locale={locale} page={pagePath} pageData={pageData} pageName={process.env.NEXT_PUBLIC_MANAGE_BLOG_LIST} />
        </div>
      </div>
    </>
  );
};
export default PageComponent;
