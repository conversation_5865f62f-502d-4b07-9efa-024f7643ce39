"use client";
import MarkdownEditor from "~/components/common/Editor";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";

const PageComponent = ({ locale }) => {
  const [pagePath] = useState(process.env.NEXT_PUBLIC_MANAGE_BLOG_WRITER);

  const { setShowLoadingModal } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    return () => {};
  }, []);

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page={pagePath} />
      <style jsx>{`
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className="flex flex-col justify-center items-center my-8 md:mb-12 md:mt-12 2xl:mt-16 mx-auto  w-[100%] md:w-[80%] 2xl:w-[70%]  h-[100%] md:min-h-[90vh] gap-8">
        <div className="w-[100%]">
          <MarkdownEditor locale={locale} />
        </div>
      </div>
    </>
  );
};
export default PageComponent;
