'use client'
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";
import { useInterval } from "ahooks";
import ManageListItem from "~/components/common/ManageListItem";
import moment from "moment";
import { getCountryByCode } from "~/configs/pay/cardCountry";
import Datepicker from "react-tailwindcss-datepicker";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { manageUserPage, manageUserSize } from "~/configs/globalConfig";

const PageComponent = ({
  locale,
  listLoginFrom
}) => {
  const router = useRouter();
  const [pagePath] = useState(manageUserPage);

  const {
    setShowLoadingModal,
    userData
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    setIntervalCheckUser(500);
    return () => {
    }
  }, []);

  const [intervalCheckUser, setIntervalCheckUser] = useState(undefined);
  const [whiteUser, setWhiteUser] = useState(false);

  const checkUser = async () => {
    if (!userData?.user_id) {

    } else {
      if (!checkAdminUser(userData)) {
        setWhiteUser(false);
      } else {
        setIntervalCheckUser(undefined);
        setWhiteUser(true);
        await getUserList(currentPage);
      }
    }
  }
  useInterval(() => {
    checkUser();
  }, intervalCheckUser);

  const [userList, setUserList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPage, setTotalPage] = useState(0);
  const [countTotal, setCountTotal] = useState(0);
  const [email, setEmail] = useState('');

  const getUserList = async (positionPage) => {
    if (!userData?.user_id) {
      return;
    }
    setShowLoadingModal(true);
    const requestData = {
      positionPage: positionPage,
      email: email,
      date: timeValue
    }
    const response = await fetch(`/api/manage/user/getUserList`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
    const result = await response.json();
    setUserList(result.resultList);
    setTotalPage(result.totalPage);
    setCountTotal(result.countTotal);
    setShowLoadingModal(false);
  }

  const prevPage = async () => {
    if (currentPage > 0 && currentPage <= totalPage) {
      const newCurrentPage = currentPage - 1;
      setCurrentPage(newCurrentPage);
      await getUserList(newCurrentPage);
    }
  }

  const nextPage = async () => {
    if (currentPage < totalPage) {
      const newCurrentPage = currentPage + 1;
      setCurrentPage(newCurrentPage);
      await getUserList(newCurrentPage);
    }
  }

  const deleteUserByUserId = async (userId) => {
    if (!userData?.user_id) {
      return;
    }
    setShowLoadingModal(true);
    const requestData = {
      user_id: userId
    }
    const response = await fetch(`/api/manage/user/deleteUserByUserId`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
    const result = await response.json();
    if (result) {
      await getUserList(currentPage);
    }
    setShowLoadingModal(false);
  }

  const [timeValue, setTimeValue] = useState({
    startDate: null,
    endDate: null
  });

  const handleTimeValueChange = (newValue) => {
    console.log("newValue:", newValue);
    setTimeValue(newValue);
  }

  const [groupUserList, setGroupUserList] = useState([]);
  const [loginFrom, setLoginFrom] = useState('');

  const getGroupUserList = async () => {
    if (!userData?.user_id) {
      return;
    }
    setShowLoadingModal(true);
    const requestData = {
      login_from: valueRef.current,
      date: timeValue,
    }
    const response = await fetch(`/api/manage/user/getGroupUserList`, {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
    const result = await response.json();
    setGroupUserList(result.resultList);
    setShowLoadingModal(false);
  }

  const valueRef = useRef(loginFrom);
  useEffect(() => {
    valueRef.current = loginFrom;
  }, [loginFrom]);

  const countAll = () => {
    setLoginFrom('');
    setLoginFrom(prevState => {
      valueRef.current = prevState;
      getGroupUserList();
      return prevState
    }
    )
  }

  if (!whiteUser) {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage
          locale={locale}
          page={pagePath}
        />
      </>
    )
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage
        locale={locale}
        page={pagePath}
      />
      <div className="pt-4 my-auto min-h-[100vh] pb-12 bg-white text-black">
        <div className="block overflow-hidden">
          <div className={"w-full mx-auto mt-12"}>
            <div className={"flex justify-center items-center"}>
              <h2 className="text-2xl">User列表(<span
                className={"text-red-500"}>{manageUserSize}</span>/页)</h2>
            </div>
            <div className={"flex items-center w-[90%] md:max-w-md mx-auto mt-4"}>
              <input
                type={"text"}
                className={"border rounded-lg block w-full px-2 py-1 text-red-500 text-lg"}
                value={email}
                placeholder={"email"}
                onChange={(e) => {
                  setEmail(e.target.value)
                }}
              />
            </div>
            <div className={"flex justify-between items-center w-[95%] md:max-w-xl mx-auto mt-4"}>
              <button
                onClick={() => prevPage()}
                className={`mx-auto flex justify-center items-center rounded-md ${currentPage == 1 ? 'bg-gray-400' : 'bg-amber-500'} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                disabled={currentPage == 1}
              >
                上一页
              </button>
              <div>当前第&nbsp;</div>
              <input
                type={"number"}
                className={"border rounded-lg block w-16 pl-2 text-red-500 text-lg"}
                value={currentPage}
                onChange={(e) => {
                  setCurrentPage(Number(e.target.value))
                }}
              />
              <div>&nbsp;页&nbsp;
                <span
                  onClick={() => getUserList(currentPage)}
                  className={"cursor-pointer mx-auto rounded-md bg-blue-500 p-2 text-md font-semibold text-white shadow-sm"}>查询</span>
              </div>
              <div>&nbsp;共{totalPage}页｜{countTotal}条</div>
              <button
                onClick={() => nextPage()}
                className={`mx-auto flex justify-center items-center rounded-md ${currentPage == totalPage ? 'bg-gray-400' : 'bg-amber-400'} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                disabled={currentPage == totalPage}
              >
                下一页
              </button>
            </div>
            <div className={"flex items-center w-[90%] md:max-w-md mx-auto mt-4"}>
              <div className="w-[95%] md:max-w-xl mx-auto flex items-center justify-center space-x-2">
                <div className="whitespace-nowrap">注册时间:</div>
                <div className={"flex-grow"}>
                  <Datepicker
                    primaryColor={"blue"}
                    inputClassName={"border rounded-lg bg-white relative transition-all duration-300 py-2.5 pl-4 pr-14 w-full border-gray-300 rounded-lg tracking-wide font-light text-sm placeholder-gray-400 bg-white focus:ring disabled:opacity-40 disabled:cursor-not-allowed focus:border-blue-500 focus:ring-blue-500/20"}
                    useRange={true}
                    asSingle={false}
                    value={timeValue}
                    displayFormat={"YYYY-MM-DD"}
                    onChange={handleTimeValueChange}
                  />
                </div>
              </div>
            </div>
            <div className="w-[90%] mx-auto mb-10 mt-4">
              <div className="grid gap-x-6 gap-y-10">
                <div className="flex flex-col rounded-lg pt-6 sm:rounded-2xl sm:pt-8">
                  <div className="flex flex-col [border-top:1px_solid_rgb(213,_213,_234)] text-red-400">
                    <div className="grid grid-cols-8 [border-bottom:1px_solid_rgb(213,_213,_234)]">
                      <ManageListItem
                        isBold={true}
                        textStr={"email"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"设备"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"生成次数"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"剩余次数"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"注册时间"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"ip-country"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"ip-city"}
                      />
                      <ManageListItem
                        isBold={true}
                        textStr={"操作"}
                      />
                    </div>
                  </div>
                  <div className="flex flex-col [border-top:1px_solid_rgb(213,_213,_234)]">
                    {
                      userList.map((item, index) => {
                        return (
                          <div key={item.user_id + index}
                            className="grid grid-cols-8 [border-bottom:1px_solid_rgb(213,_213,_234)]">
                            <div
                              className="p-5 flex items-center justify-center [border-left:1px_solid_rgb(213,_213,_234)] [border-right:1px_solid_rgb(213,_213,_234)]">
                              {
                                item.stripe_customer_id ?
                                  <a
                                    href={`https://dashboard.stripe.com/customers/${item.stripe_customer_id}`}
                                    target={"_blank"}
                                  >
                                    <span
                                      className={"flex justify-center items-center text-blue-500 break-all"}>{item.email}</span>
                                  </a>
                                  :
                                  <span
                                    className={"flex justify-center items-center text-gray-500 break-all"}>{item.email}</span>
                              }
                            </div>
                            <ManageListItem
                              titleStr={item.user_agent}
                              textStr={item.device + (item.login_from ? '｜' + item.login_from : '')}
                            />
                            <ManageListItem
                              textStr={item.data_count}
                            />
                            <ManageListItem
                              textStr={item.available_times}
                            />
                            <ManageListItem
                              textStr={moment(item.created_at).format('YYYY-MM-DD HH:mm:ss')}
                            />
                            <ManageListItem
                              textStr={getCountryByCode(item.country).chinese}
                            />
                            <ManageListItem
                              textStr={item.city}
                            />
                            <div
                              className="[border-left:1px_solid_rgb(213,_213,_234)] [border-right:1px_solid_rgb(213,_213,_234)]">
                              <div className={"h-28 flex flex-col justify-center items-center"}>
                                <p
                                  onClick={() => deleteUserByUserId(item.user_id)}
                                  className="cursor-pointer px-3 py-2 text-xl rounded-md bg-gray-200 text-red-500">
                                  删除用户
                                </p>
                              </div>
                            </div>
                          </div>
                        )
                      })
                    }
                  </div>
                </div>
              </div>
            </div>
            <div className={"flex justify-between items-center w-[95%] md:max-w-xl mx-auto mt-4"}>
              <button
                onClick={() => prevPage()}
                className={`mx-auto flex justify-center items-center rounded-md ${currentPage == 1 ? 'bg-gray-400' : 'bg-amber-500'} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                disabled={currentPage == 1}
              >
                上一页
              </button>
              <div>当前第&nbsp;</div>
              <input
                type={"number"}
                className={"border rounded-lg block w-16 pl-2 text-red-500 text-lg"}
                value={currentPage}
                onChange={(e) => {
                  setCurrentPage(Number(e.target.value))
                }}
              />
              <div>&nbsp;页&nbsp;
                <span
                  onClick={() => getUserList(currentPage)}
                  className={"cursor-pointer mx-auto rounded-md bg-blue-500 p-2 text-md font-semibold text-white shadow-sm"}>查询</span>
              </div>
              <div>&nbsp;共{totalPage}页｜{countTotal}条</div>
              <button
                onClick={() => nextPage()}
                className={`mx-auto flex justify-center items-center rounded-md ${currentPage == totalPage ? 'bg-gray-400' : 'bg-amber-400'} px-3 py-2 text-md font-semibold text-white shadow-sm`}
                disabled={currentPage == totalPage}
              >
                下一页
              </button>
            </div>
            <div className={"flex items-center w-[90%] md:max-w-xs mx-auto mt-4"}>
              <Datepicker
                primaryColor={"blue"}
                inputClassName={"border rounded-lg bg-white relative transition-all duration-300 py-2.5 pl-4 pr-14 w-full border-gray-300 rounded-lg tracking-wide font-light text-sm placeholder-gray-400 bg-white focus:ring disabled:opacity-40 disabled:cursor-not-allowed focus:border-blue-500 focus:ring-blue-500/20"}
                useRange={true}
                asSingle={false}
                value={timeValue}
                displayFormat={"YYYY-MM-DD"}
                onChange={handleTimeValueChange}
              />
            </div>
            <div className={"mt-8"}></div>
            <div className="w-[90%] mx-auto mb-100 mt-4">
              <div className="w-[95%] md:max-w-xl mx-auto flex items-center justify-center space-x-2">
                <input
                  type="text"
                  className="border border-gray-300 rounded-md px-3 py-2 flex-grow focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={loginFrom}
                  placeholder={""}
                  onChange={(e) => {
                    setLoginFrom(e.target.value)
                  }}
                />
                <button
                  className="bg-amber-400 text-white px-4 py-2 rounded-md shadow-sm"
                  onClick={() => getGroupUserList()}
                >
                  查询
                </button>
              </div>
              <div className="mt-4 w-[95%] md:max-w-xl mx-auto flex items-center justify-center space-x-2">
                <button
                  className="bg-green-500 text-white px-4 py-2 rounded-md shadow-sm"
                  onClick={() => countAll()}
                >
                  全部
                </button>
                {listLoginFrom.map((item, index) => (
                  <button
                    key={item + index}
                    className={`bg-${['blue', 'green', 'yellow', 'purple', 'pink'][index % 5]}-500 text-white px-4 py-2 rounded-md shadow-sm`}
                    onClick={() => {
                      setLoginFrom(item);
                      setLoginFrom(prevState => {
                        valueRef.current = prevState;
                        getGroupUserList();
                        return prevState;
                      });
                    }}
                  >
                    {item}
                  </button>
                ))}
              </div>
            </div>
            <div className="flex flex-col [border-top:1px_solid_rgb(213,_213,_234)] text-red-400 mt-8">
              <div className="grid grid-cols-3 [border-bottom:1px_solid_rgb(213,_213,_234)]">
                <ManageListItem
                  isBold={true}
                  textStr={"国家代码"}
                />
                <ManageListItem
                  isBold={true}
                  textStr={"国家"}
                />
                <ManageListItem
                  isBold={true}
                  textStr={"数量"}
                />
              </div>
            </div>
            <div className="flex flex-col [border-top:1px_solid_rgb(213,_213,_234)]">
              {
                groupUserList.map((item, index) => {
                  return (
                    <div key={item.card_address_country + item.total_paid_amount + index}
                      className="grid grid-cols-3 [border-bottom:1px_solid_rgb(213,_213,_234)]">
                      <ManageListItem
                        textStr={item.country}
                      />
                      <ManageListItem
                        textStr={getCountryByCode(item.country).chinese}
                      />
                      <ManageListItem
                        textStr={item.count}
                      />
                    </div>
                  )
                })
              }
            </div>
            <div
              className="grid grid-cols-3 [border-bottom:1px_solid_rgb(213,_213,_234)]">
              <ManageListItem
                textStr={""}
              />
              <ManageListItem
                textStr={"汇总"}
              />
              <ManageListItem
                textStr={(groupUserList.reduce((sum1, item) => {
                  return sum1 + Number(item.count);
                }, 0))}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  )

}

export default PageComponent
