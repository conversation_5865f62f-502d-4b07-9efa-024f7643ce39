'use client'
import React, { useEffect, useRef, useState } from "react";
import Footer from "~/components/common/Footer";
import { useCommonContext } from "~/context/common-context";
import { useInterval } from "ahooks";
import HeadInfo from "~/components/common/HeadInfo";
import { googleAwId, googleAwOpen } from "~/configs/globalConfig";
import confetti from 'canvas-confetti';
import Script from "next/script";

const style = {
  loginGoogleBtn: 'inline-flex w-full justify-center items-center space-x-3 rounded-md px-3 py-3 text-sm font-semibold shadow-sm hover:bg-gray-50 border-2 border-gray-200 transition-all duration-200 max-w-md',
  container: 'flex min-h-[80vh] items-center justify-center py-12 px-4 sm:px-6 lg:px-8',
  formContainer: 'w-full max-w-md space-y-8 bg-white/5 backdrop-blur-lg p-8 rounded-xl border border-white/10 shadow-2xl',
  heading: 'text-center space-y-4',
  title: 'text-3xl font-bold tracking-tight md:text-4xl bg-gradient-to-r from-white to-gray-900 bg-clip-text text-transparent',
  description: 'text-lg text-gray-400 max-w-sm mx-auto'
}

const PageComponent = ({
  locale,
  user_info
}) => {
  const [pagePath] = useState("login-success");

  const {
    setShowLoadingModal,
  } = useCommonContext();

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === 'production' || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    showConfetti();
    if (!user_info.is_new_user) {
      redirectToOriginalPage();
    }
    return () => {
    }
  }, []);

  const showConfetti = () => {
    confetti({ particleCount: 1000, spread: 200, origin: { y: 0.6 } });
  }

  const [intervalRedirect, setIntervalRedirect] = useState(5000);
  useInterval(() => {
    redirectToOriginalPage();
  }, intervalRedirect);
  const redirectToOriginalPage = () => {
    const successUrl = localStorage.getItem("loginSuccessUrl");
    if (successUrl) {
      localStorage.removeItem("loginSuccessUrl");
      window.location.href = successUrl;
    }
  }


  return (
    <>
      <meta name="robots" content="noindex" />
      {googleAwOpen ? (
        <>
          <Script src={`https://www.googletagmanager.com/gtag/js?id=${googleAwId}`} id="google-ads-script1" strategy="afterInteractive" />
          <Script id="google-ads-script2" strategy="afterInteractive">
            {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', ${googleAwId});
              `}
          </Script>
          {user_info?.email ? (
            <Script id="google-ads-script3" strategy="afterInteractive">
              {`
                    gtag('set', 'user_data', {
                        'email': '${user_info?.email}',
                    });
                  `}
            </Script>
          ) : null}
          {user_info?.is_new_user ? (
            <Script id="google-ads-script4" strategy="afterInteractive">
              {`
                    gtag('event', 'conversion', {
                        'send_to': 'AW-16957597539/pLQHCPqOtrAaEOPOgZY_',
                    });
                    `}
            </Script>
          ) : null}
        </>
      ) : null}
      <HeadInfo locale={locale} page={pagePath} title="Login Successful" description="You have successfully logged in" />
      <div className="flex min-h-screen bg-white">
        <main className="flex-1 relative bg-[#FBFBFF] overflow-y-auto max-h-screen mt-14 md:mt-0  overflow-x-hidden">
          <div className="flex flex-col px-0 mt-10">
            <div className={style.container}>
              <div className={style.formContainer}>
                <div className={style.heading}>
                  <h1 className={style.title}>Login Successful</h1>
                  <p className={style.description}>You will be redirected to the previous page shortly...</p>
                </div>
              </div>
            </div>
            <Footer locale={locale} page={pagePath} />
          </div>
        </main>
      </div>
    </>
  );
}

export default PageComponent
