import PageComponent from "./PageComponent";
import { setRequestLocale } from 'next-intl/server';

import {
  getPrivacyPolicyText
} from "~/i18n/languageText";

export default async function PrivacyPolicyPage(props) {
  const params = await props.params;

  const {
    locale = ''
  } = params;

  // Enable static rendering
  setRequestLocale(locale);

  const privacyPolicyText = await getPrivacyPolicyText();

  return (
    <PageComponent
      locale={locale}
      privacyPolicyText={privacyPolicyText}
    />
  )
}
