@tailwind base;
@tailwind components;
@tailwind utilities;

.div-markdown-color {
  color: text-gray-700;
}

.div-markdown-color h1,
.div-markdown-color h2,
.div-markdown-color h3,
.div-markdown-color h4,
.div-markdown-color h5,
.div-markdown-color h6 {
  @apply text-gray-700;
}

.div-markdown-color a {
  @apply text-gray-700;
}

.div-markdown-color strong {
  @apply text-gray-700;
}
.div-markdown-color p {
  @apply text-gray-700;
}
.div-markdown-color li {
  @apply text-gray-700;
}

.box-white-bg {
  @apply rounded-lg relative bg-gradient-to-tr from-white/70 to-white/50 dark:bg-gradient-to-b dark:from-gray-700/50 dark:to-gray-700/40 shadow shadow-black/5 flex items-center justify-start;
}

.custom-textarea:focus {
  border: none !important;
  outline: none !important;
}

.background-div {
  background-repeat: no-repeat;
  background-size: cover;
  background-color: #e0e7ff;
}
.box-bg {
  background-color: #6366f126;
}
.text-main {
  color: #6a63f6;
}
.text-sub {
  color: #ed7470;
}
.text-strong {
  color: #2d6ae0;
}
.pricing-box {
  background-color: #0005ff59;
}
.form-input-bg {
  background-color: #ffffffb3;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 定义一个棋盘格背景的样式 */
.checkerboard {
  /* 创建两个线性渐变，45度角，模拟棋盘格效果 */
  background-image: linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0), linear-gradient(45deg, #f0f0f0 25%, #ffffff 25%, #ffffff 75%, #f0f0f0 75%, #f0f0f0);
  /* 定义每个方格的大小为20px */
  background-size: 20px 20px;
  /* 偏移其中一个渐变，以产生交错效果 */
  background-position: 0 0, 10px 10px;
}

.main-color-0 {
  color: #3662e3;
}

.main-color-1 {
  color: #3662e3;
}

.main-color-0-bg {
  background-color: #3662e3;
}

.main-color-1-bg {
  background-color: #3662e3;
}

.text-available {
  color: #3662e3;
}

.switch-button {
  background-color: #3662e3;
}

.download-audio-button {
  color: #3662e3;
}

.loading-color {
  color: #3662e3;
}

.button-text {
  color: white;
}

.button-bg {
  background-color: #3662e3;
}

.button-bg:hover {
  background-color: #3662e3;
}

.link-text {
  color: #3662e3;
}

.link-text:hover {
  color: #3662e3;
}

.header-link {
  color: #fff;
}

.header-link:hover {
  color: #3662e3;
}

.header-choose-color {
  color: #3662e3;
}

.footer-leading-text {
  color: #fff;
}

.footer-link {
  color: #c9ccd2;
}

.footer-link:hover {
  color: #3662e3;
}

.footer-desc-text {
  color: #c9ccd2;
}

.tags-choose-color {
  background-color: #3662e3;
}

.tags-color {
  background-color: gray;
}

.header-profile-menu {
  color: #374151;
}

.header-profile-menu:hover {
  color: #3662e3;
}

.box-shadow-game {
  background: rgba(0, 0, 0, 0.45);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(19px);
  -webkit-backdrop-filter: blur(19px);
  border-radius: 10px;
}

.h-587 {
  height: 587px;
}

img {
  border-radius: 6px;
}

.h-1000 {
  height: 1000px;
}

.color-1 {
  background-color: #212233;
}
.color-2 {
  background-color: #1a1b28;
}

.box-shadow-game-1 {
  box-shadow: rgba(192, 162, 162, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;
}

/* Firefox (uncomment to work in Firefox, although other properties will not work!)  */
/** {
    scrollbar-width: thin;
    scrollbar-color: #13007C #000000;
  }*/

/* Chrome, Edge and Safari */
/* *::-webkit-scrollbar {
    height: 10px;
    width: 10px;
  }
  *::-webkit-scrollbar-track {
    border-radius: 5px;
    background-color: #000000;
  }

  *::-webkit-scrollbar-track:hover {
    background-color: #000845;
  }

  *::-webkit-scrollbar-track:active {
    background-color: #B8C0C2;
  }

  *::-webkit-scrollbar-thumb {
    border-radius: 12px;
    background-color: #e7e7e7;
  }

  *::-webkit-scrollbar-thumb:hover {
    background-color: #A3A3A3;
  }

  *::-webkit-scrollbar-thumb:active {
    background-color: #A3A3A3;
  } */

.main-div {
  display: flex;
  gap: 10px;
  overflow: hidden;
}
.bg-background {
  background-color: #000000;
}
/* 自定义 Swiper 样式 */
.swiper-button-prev,
.swiper-button-next {
  --swiper-navigation-size: 20px;
  color: white !important;
}

.swiper-pagination-bullet {
  background: white !important;
  opacity: 0.5 !important;
}

.swiper-pagination-bullet-active {
  opacity: 1 !important;
}

/* 隐藏默认导航按钮的背景 */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-size: 16px !important;
}


.ppg-buy-now-btn {
  display: inline-block;
  background: #19cb92;
  border-radius: 29px;
  color: #fff;
  padding: 14px 42px;
  margin: 10px;
  text-decoration: none;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
}
.ppg-checkout-modal {
  z-index: 99999;
  display: none;
  background-color: transparent;
  border: 0px none transparent;
  visibility: visible;
  margin: 0px;
  padding: 0px;
  -webkit-tap-highlight-color: transparent;
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
}
.ppg-checkout-modal.ppg-show {
  display: block;
}
.ppg-btn-close {
  position: absolute;
  display: none;
  align-items: center;
  justify-content: center;
  top: 20px;
  right: 35px;
  background: rgb(0 0 0 / 35%);
  height: 50px;
  width: 50px;
  border: none;
  outline: none;
  cursor: pointer;
}
.ppg-btn-close.ppg-show {
  display: flex;
}
.ppg-btn-close img {
  width: 24px;
}
.ppg-iframe {
  width: 100%;
  height: 100%;
  border: 0;
  overflow-x: hidden;
  overflow-y: auto;
}
.ppg-loader {
  position: absolute;
  top: calc(50% - 24px);
  left: calc(50% - 24px);
  width: 48px;
  height: 48px;
  border: 5px solid #000;
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: ppg-rotation 1s linear infinite;
}

@keyframes ppg-rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
