import { nextauthUrl, paddleApiKey, paddleSandbox } from "~/configs/globalConfig";

export async function POST(req: Request) {
  const json = await req.json();

  const reqJson = {
    items: [
      {
        price_id: json.price_id,
        quantity: 1,
      },
    ],
    checkout: {
      url: paddleSandbox ? `https://aisong.ai/pricing` : `${nextauthUrl}/pricing`,
    },
  };
  const url = paddleSandbox ? `https://sandbox-api.paddle.com/transactions` : `https://api.paddle.com/transactions`;
  const response = await fetch(`${url}`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${paddleApiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(reqJson),
  });
  const result = await response.json();
  console.log("result-=->", result);
  const transactionId = result?.data?.id;
  const checkoutUrl = result?.data?.checkout?.url;
  return Response.json({ status: 0, transactionId: transactionId, checkoutUrl: checkoutUrl });
}
