import { NextRequest, NextResponse } from "next/server";
import { validateSignature } from "~/libs/paddle/paddle";
import { getDb } from "~/libs/db/db";
import { managePaddleSubscription } from "~/libs/paddle/handle-paddle";
import { domainNameLowercase, paddleWebhookSecret } from "~/configs/globalConfig";

const db = getDb();
export async function POST(req: NextRequest) {
  const signature = req.headers.get("Paddle-Signature")!;
  const body = await req.text();

  const isValid = await validateSignature(signature, body, paddleWebhookSecret);

  if (!isValid)
    return NextResponse.json(
      {
        message: "Invalid webhook signature!",
      },
      {
        status: 401,
      }
    );

  const parsedBody = JSON.parse(body);
  // console.log("parsedBody-=->", parsedBody);
  const data = parsedBody.data;
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 判断是不是当前域名的 webhook 数据，是的话才处理
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  const domain = data?.custom_data?.domain;
  const domain_name = domainNameLowercase;
  if (domain?.toLowerCase() !== domain_name?.toLowerCase()) {
    // 不是当前域名的 webhook 数据，不处理
    return NextResponse.json(
      {
        message: "success",
      },
      {
        status: 200,
      }
    );
  }
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 是当前域名的 webhook 数据，继续往下处理
  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-
  // 存储事件数据
  const event_id = parsedBody.event_id;
  const event_type = parsedBody.event_type;
  const occurred_at = parsedBody.occurred_at;
  const notification_id = parsedBody.notification_id;
  const id = data.id;
  const status = data.status;
  const customer_id = data.customer_id;
  const user_id = data.custom_data?.user_id;
  const origin = data?.origin;
  await db.query(
    `
    insert into paddle_webhook_record(event_id, event_type, occurred_at, notification_id, id,  status, customer_id, user_id, data, origin) values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
  `,
    [event_id, event_type, occurred_at, notification_id, id, status, customer_id, user_id, parsedBody, origin]
  );

  switch (parsedBody.event_type) {
    case "subscription.created":
      // handle subscription created event
      await managePaddleSubscription(parsedBody);
      break;
    case "subscription.updated":
      // handle subscription updated event
      await managePaddleSubscription(parsedBody);
      break;
    case "subscription.cancelled":
      // handle subscription cancelled event
      await managePaddleSubscription(parsedBody);
      break;
    case "transaction.completed":
      // handle transaction succeeded event
      // await managePaddleSubscription(parsedBody);
      break;
    default:
      break;
  }

  return NextResponse.json(
    {
      message: "done",
    },
    {
      status: 200,
    }
  );
}
