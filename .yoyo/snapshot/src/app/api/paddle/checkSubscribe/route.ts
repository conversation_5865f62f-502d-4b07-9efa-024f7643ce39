import {getUserByServerSession} from "~/servers/common/user";
import {checkPaddleSubscribe} from "~/servers/pay/subscribe";

export const revalidate = 0;
export const GET = async (req: Request) => {
  const user = await getUserByServerSession();
  if (!user.user_id) {
    return Response.json({status: 1});
  }
  const checkResult = await checkPaddleSubscribe(user.user_id);

  return Response.json({status: 0, checkResult: checkResult});
}
