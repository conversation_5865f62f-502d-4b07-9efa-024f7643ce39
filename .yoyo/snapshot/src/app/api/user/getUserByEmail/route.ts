import { getUserByEmailAndCode, getUserByServerSession } from "~/servers/common/user";

export async function POST(req: Request) {
  let json = await req.json();

  const user_info = await getUserByServerSession();
  if (!user_info.user_id) {
    return Response.json(user_info);
  }
  const email = json.email;
  if (email !== user_info.email) {
    return Response.json(json);
  }

  const result = await getUserByEmailAndCode(json, user_info);
  return Response.json(result);
}
