import { notLoginFreeUseTimes } from "~/configs/globalConfig";
import { checkUserTimes, getNotLoginFreeTimesByFingerprint } from "~/servers/pay/manageUserTimes";

export const revalidate = 0;
export const maxDuration = 200;

export async function POST(req: Request) {
  const { user_id } = await req.json();

  // 判断 user_id 是否是指纹
  if (user_id.includes("_guest")) {
    const fingerprint = user_id.split("_")[0];
    // 查询使用次数
    const notLoginFreeTimes = notLoginFreeUseTimes;
    const notLoginFreeTimesByFingerprint = await getNotLoginFreeTimesByFingerprint(fingerprint);
    let freeTimes = notLoginFreeTimes - notLoginFreeTimesByFingerprint;
    if (freeTimes < 0) {
      freeTimes = 0;
    }
    if (freeTimes > 0) {
      return Response.json({ status: 1, message: "User is available", freeTimes: freeTimes });
    } else {
      return Response.json({
        status: 601,
        message: "You have no free credits, please come back tomorrow, or Log in to get more credits",
      });
    }
  }

  // 判断是否使用完了次数
  const checkUserTimesStatus = await checkUserTimes(user_id);
  if (!checkUserTimesStatus) {
    return Response.json({
      status: 602,
      message: "You have no free credits, please come back tomorrow, or Pricing to get more credits",
    });
  }

  // 可以继续使用
  return Response.json({
    status: 1,
    message: "User is available",
  });
}
