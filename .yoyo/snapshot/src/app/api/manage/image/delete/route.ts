import { NextResponse } from "next/server";
import { deleteImage } from "~/servers/manage/page/image";

export async function POST(request) {
  try {
    const body = await request.json();
    const { uid } = body;

    // 删除图片
    const result = await deleteImage({ uid });

    return NextResponse.json(result);
  } catch (error) {
    console.error("删除图片出错:", error);
    return NextResponse.json(
      {
        code: 500,
        message: "删除图片失败: " + error.message,
      },
      { status: 500 }
    );
  }
}
