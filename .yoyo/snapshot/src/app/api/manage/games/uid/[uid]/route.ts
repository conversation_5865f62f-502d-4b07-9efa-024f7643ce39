import { NextRequest, NextResponse } from "next/server";
import { saveGameByUid, getGameByUid, deleteGameByUid } from "~/servers/manage/manageGames";

export const maxDuration = 100;
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const { uid, action, data } = await req.json();
    
    switch (action) {
      case 'get':
        const result = await getGameByUid(uid);
        return NextResponse.json(result);
        
      case 'update':
        // Ensure the UID in the URL is used
        data.uid = uid;
        const updatedResult = await saveGameByUid(data);
        return NextResponse.json(updatedResult);
        
      case 'delete':
        const deleteResult = await deleteGameByUid(uid);
        return NextResponse.json(deleteResult);
        
      default:
        return NextResponse.json(
          { code: 400, message: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error(`Error in games API route:`, error);
    return NextResponse.json(
      { code: 500, message: "Internal server error" },
      { status: 500 }
    );
  }
} 