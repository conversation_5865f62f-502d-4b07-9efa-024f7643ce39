import { NextRequest, NextResponse } from "next/server";
import { saveGameByUid, getGameByUid, getGames } from "~/servers/manage/manageGames";

// 根据 UID 获取游戏或获取游戏列表
export async function GET(request: NextRequest) {
  try {
    // 解析 URL 参数
    const searchParams = request.nextUrl.searchParams;
    const uid = searchParams.get('uid');
    
    // 如果提供了uid，则获取特定游戏
    if (uid) {
      const result = await getGameByUid(uid);
      return NextResponse.json({
        code: result.code,
        data: result.data,
        message: result.message,
        success: result.code === 200
      });
    }
    
    // 否则返回游戏列表（带分页）
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const skipSize = parseInt(searchParams.get('skipSize') || '0');
    
    const result = await getGames({ pageSize, skipSize });
    
    return NextResponse.json({
      code: result.code,
      data: result.data,
      message: result.message,
      success: result.code === 200
    });
    
  } catch (error) {
    console.error("Error in games API route:", error);
    return NextResponse.json({
      code: 500,
      message: "Internal server error",
      success: false
    }, { status: 500 });
  }
}

// 创建或更新游戏（通过uid）
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const result = await saveGameByUid(data);
    
    return NextResponse.json({
      code: result.code,
      data: result.data,
      message: result.message,
      success: result.code === 200
    }, { status: result.code >= 400 ? result.code : 200 });
    
  } catch (error) {
    console.error("Error in games POST API route:", error);
    return NextResponse.json({
      code: 500,
      message: "Internal server error",
      success: false
    }, { status: 500 });
  }
} 