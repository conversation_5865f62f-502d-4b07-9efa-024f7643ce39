import { getUserByServerSession } from "~/servers/common/user";
import { 
  changePageStatus, 
  batchChangePageStatus 
} from "~/servers/game/pageService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

// POST: Change status of one or multiple pages
export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const json = await req.json();
    const { uid, uids, status } = json;

    // Validate required parameters
    if (status === undefined) {
      return Response.json({
        status: 400,
        message: "Status is required",
      });
    }

    // Single page status change
    if (uid) {
      const updatedPage = await changePageStatus(uid, status);
      
      if (!updatedPage) {
        return Response.json({
          status: 404,
          message: "Page not found",
        });
      }

      return Response.json({
        status: 200,
        message: "Page status updated successfully",
        data: updatedPage,
      });
    } 
    // Batch status change
    else if (uids && Array.isArray(uids) && uids.length > 0) {
      const result = await batchChangePageStatus(uids, status);
      
      if (!result.success) {
        return Response.json({
          status: 404,
          message: "No pages were updated",
          failedUids: result.failedUids,
        });
      }

      return Response.json({
        status: 200,
        message: `${result.updatedCount} page(s) updated successfully`,
        updatedCount: result.updatedCount,
        failedUids: result.failedUids,
      });
    } else {
      return Response.json({
        status: 400,
        message: "Either uid or uids array is required",
      });
    }
  } catch (error) {
    console.error("Error updating page status:", error);
    return Response.json({
      status: 500,
      message: "An error occurred while updating page status",
    });
  }
} 