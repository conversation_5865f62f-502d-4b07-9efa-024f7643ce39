import { getUserByServerSession } from "~/servers/common/user";
import { getPayproOrderList } from "~/servers/manage/manageOrder";
import { checkSuperUser } from "~/utils/checkWhiteUser";

export const maxDuration = 300;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkSuperUser(user_info)) {
    return new Response(
      JSON.stringify({
        resultList: [],
        totalPage: 0,
        countTotal: 0,
      }),
      {
        headers: { "Content-Type": "application/json" },
        status: 403,
      }
    );
  }

  const result = await getPayproOrderList(json);

  return new Response(JSON.stringify(result), {
    headers: { "Content-Type": "application/json" },
    status: 200,
  });
}
