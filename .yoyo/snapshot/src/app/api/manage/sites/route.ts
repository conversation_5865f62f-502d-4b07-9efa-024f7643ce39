import { getUserByServerSession } from "~/servers/common/user";
import { getSites, createSite } from "~/servers/game/siteService";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 100;
export const revalidate = 0;

export async function POST(req: Request) {
  try {
    const user_info = await getUserByServerSession();
    if (!checkAdminUser(user_info)) {
      return Response.json({
        status: 403,
        message: "Permission denied",
      });
    }

    const { action, data } = await req.json();
    
    switch (action) {
      case 'list':
        const { page = 1, pageSize = 10, name } = data || {};
        const result = await getSites(page, pageSize, name);
        return Response.json({
          status: 200,
          message: "Success",
          data: result,
        });
        
      case 'create':
        if (!data.name) {
          return Response.json({
            status: 400,
            message: "Site name is required",
          });
        }
        
        const newSite = await createSite(data);
        return Response.json({
          status: 200,
          message: "Site created successfully",
          data: newSite,
        });
        
      default:
        return Response.json({
          status: 400,
          message: "Invalid action",
        });
    }
  } catch (error) {
    console.error(`Error in sites API route:`, error);
    return Response.json({
      status: 500,
      message: "An error occurred while processing the request",
      error: error.message
    });
  }
} 