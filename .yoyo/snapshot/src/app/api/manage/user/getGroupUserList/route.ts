import { getUserByServerSession } from "~/servers/common/user";
import { getGroupUserList } from "~/servers/manage/manageUser";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const maxDuration = 300;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return new Response(JSON.stringify({ resultList: [] }), {
      headers: { "Content-Type": "application/json" },
      status: 403,
    });
  }

  const result = await getGroupUserList(json);

  return new Response(JSON.stringify(result), {
    headers: { "Content-Type": "application/json" },
    status: 200,
  });
}
