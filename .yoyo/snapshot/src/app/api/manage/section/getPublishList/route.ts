import { getUserByServerSession } from "~/servers/common/user";
import { getPublishSectionList } from "~/servers/manage/page/section";
import { checkAdminUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

export async function GET() {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return Response.json({
      status: 403,
      message: "没有权限！",
    });
  }

  const result = await getPublishSectionList();

  return Response.json(result);
}
