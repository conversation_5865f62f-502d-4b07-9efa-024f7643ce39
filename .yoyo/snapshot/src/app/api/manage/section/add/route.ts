import { getUserByServerSession } from "~/servers/common/user";
import { addNewSection } from "~/servers/manage/page/section";
import { checkDeveloperUser } from "~/utils/checkWhiteUser";

export const revalidate = 0;

export async function POST(req: Request) {
  const json = await req.json();

  const user_info = await getUserByServerSession();
  if (!checkDeveloperUser(user_info)) {
    return Response.json({
      status: 403,
      message: "没有权限！",
    });
  }

  const result = await addNewSection(json);

  return Response.json(result);
}
