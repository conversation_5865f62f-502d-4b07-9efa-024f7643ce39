import { getDb } from "~/libs/db/db";
import { stripe } from "~/libs/stripe/stripe";

export const maxDuration = 100;
export const revalidate = 0;

const db = getDb();
export const GET = async (req: Request) => {
  // 查询 stripe_subscriptions 表中的数据，判断订阅是否到期，到期的调用接口取消订阅
  // 获取当前时间，转换成 UTC时间
  const now = new Date();
  const nowUTCString = now.toISOString().replace("T", " ").replace("Z", "");
  console.log("now==", now);
  console.log("nowUTCString==", nowUTCString);
  const { rows: resultList } = await db.query(
    `
    SELECT * FROM stripe_subscriptions ss
            left join stripe_customers sc on sc.user_id=ss.user_id
            where
--                  ss.cancel_at_period_end=true
--                and
                ss.current_period_end<$1 and ss.status='active'
            order by ss.current_period_end
            limit 100
    ;
    `,
    [now]
  );
  console.log("resultList.length==>", resultList.length);
  const errIds = [];
  if (resultList.length > 0) {
    for (let i = 0; i < resultList.length; i++) {
      const item = resultList[i];
      // 取消订阅
      try {
        console.log("item.id==", item.id);
        const subscription = await stripe.subscriptions.cancel(item.id);
        console.log("subscription==", subscription);
      } catch (e) {
        console.log("e==", e);
        console.log("item.id==", item.id);
        errIds.push(item.id);
        // break;
      }
    }
    console.log("errIds==", errIds);
  }

  return Response.json({
    msg: "success",
    now: now,
    errIds: errIds,
    dataLength: resultList.length,
    data: resultList,
  });
};
