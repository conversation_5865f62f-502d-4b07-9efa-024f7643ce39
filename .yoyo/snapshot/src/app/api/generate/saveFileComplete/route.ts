import { getDb } from "~/libs/db/db";
import { notifyToWeixin } from "~/servers/common/weixin";

const db = getDb();
// Worker上传文件完成的回调
export const POST = async (req: Request) => {
  const query = new URL(req.url).searchParams;
  const uid = query.get("uid")!;

  if (!uid) {
    console.log("uid is required-=-=->", uid);
    return Response.json({ msg: "uid is required", status: 400 });
  }

  if (process.env.NODE_ENV !== "production") {
    // 本地测试通知文件上传成功
    await notifyToWeixin(`文件上传成功-=->uid=-${uid}`);
  }

  // 保存文件的结果
  const resultJson = await req.json();
  console.log("resultJson-=-=->", resultJson);

  return Response.json({ msg: "success", status: 200 });
};
