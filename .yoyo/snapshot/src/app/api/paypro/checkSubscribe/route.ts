import { getUserByServerSession } from "~/servers/common/user";
import { checkPayproSubscribe } from "~/servers/pay/subscribe";

export const revalidate = 0;
export const GET = async (req: Request) => {
  const user = await getUserByServerSession();
  if (!user.user_id) {
    return Response.json({ status: 1 });
  }
  const checkResult = await checkPayproSubscribe(user.user_id);

  return Response.json({ status: 0, checkResult: checkResult });
};
