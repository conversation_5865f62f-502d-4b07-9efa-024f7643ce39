import { storageUrl } from "~/configs/globalConfig";
import { generatePresignedUrl } from "~/libs/r2/R2";
import { generateNewLongUID, generateNewShortUniqueUID } from "~/utils/uidUtil";

export const GET = async (req: Request) => {
  const { searchParams } = new URL(req.url);
  const fileExtension = searchParams.get("fileExtension");

  if (!fileExtension) {
    return Response.json({ status: 601, msg: "No file uploaded." });
  }

  const strUUID = generateNewShortUniqueUID();
  const objectKey = `images/${strUUID}.${fileExtension}`;
  const objectUrl = `${storageUrl}/${objectKey}`;
  const presignedUrl = await generatePresignedUrl(objectKey);
  return Response.json({ presignedUrl, objectKey, objectUrl });
};
