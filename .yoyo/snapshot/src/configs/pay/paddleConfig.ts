
// paddle 生产环境支付价格配置
const subscription_priceProd = [
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 2990,
    id: "pri_01j4qwzsgnebv26aye0596t4ay",
    availableTimes: 1500,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 990,
    id: "pri_01j4qwza2zqjs69xc0vn0rfjbw",
    availableTimes: 300,
    downloadTimes: 1000,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 190,
    id: "pri_01j4qwywvh2h7xs7kmt3z7nmjf",
    availableTimes: 20,
    downloadTimes: 100,
  }
];

// paddle 测试环境支付价格配置
const subscription_priceTest = [
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 2990,
    id: "pri_01j3y3ycv5c3xee0x96q37nex6",
    availableTimes: 1500,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 990,
    id: "pri_01j3y3xbbag39gvjhhggavayr8",
    availableTimes: 300,
    downloadTimes: 1000,
  },
  {
    currency: "usd",
    type: "recurring",
    unit_amount: 190,
    id: "pri_01j3y8mw3wkgd3n5bf9eqpwjeh",
    availableTimes: 20,
    downloadTimes: 100,
  }
];

export const paddle_subscription_priceList = (process.env.NODE_ENV === 'production' ? subscription_priceProd: subscription_priceTest);

/**
 * 根据价格 id 获取订阅产品价格
 */
export const getPaddleSubscriptionPrice = (priceId) => {
  return paddle_subscription_priceList.find(price => price.id === priceId);
}
