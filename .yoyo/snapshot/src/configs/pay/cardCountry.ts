const countryList = [
  { code: "US", english: "United States", chinese: "美国" },
  { code: "AE", english: "United Arab Emirates", chinese: "阿联酋" },
  { code: "AG", english: "Antigua and Barbuda", chinese: "安提瓜和巴布达" },
  { code: "AL", english: "Albania", chinese: "阿尔巴尼亚" },
  { code: "AM", english: "Armenia", chinese: "亚美尼亚" },
  { code: "AR", english: "Argentina", chinese: "阿根廷" },
  { code: "AT", english: "Austria", chinese: "奥地利" },
  { code: "AU", english: "Australia", chinese: "澳大利亚" },
  { code: "BA", english: "Bosnia and Herzegovina", chinese: "波斯尼亚和黑塞哥维那" },
  { code: "BE", english: "Belgium", chinese: "比利时" },
  { code: "BG", english: "Bulgaria", chinese: "保加利亚" },
  { code: "BH", english: "Bahrain", chinese: "巴林" },
  { code: "BO", english: "Bolivia", chinese: "玻利维亚" },
  { code: "CA", english: "Canada", chinese: "加拿大" },
  { code: "CH", english: "Switzerland", chinese: "瑞士" },
  { code: "CI", english: "Ivory Coast", chinese: "科特迪瓦" },
  { code: "CL", english: "Chile", chinese: "智利" },
  { code: "CO", english: "Colombia", chinese: "哥伦比亚" },
  { code: "CR", english: "Costa Rica", chinese: "哥斯达黎加" },
  { code: "CY", english: "Cyprus", chinese: "塞浦路斯" },
  { code: "CZ", english: "Czech Republic", chinese: "捷克" },
  { code: "DE", english: "Germany", chinese: "德国" },
  { code: "DK", english: "Denmark", chinese: "丹麦" },
  { code: "DO", english: "Dominican Republic", chinese: "多米尼加共和国" },
  { code: "EC", english: "Ecuador", chinese: "厄瓜多尔" },
  { code: "EE", english: "Estonia", chinese: "爱沙尼亚" },
  { code: "EG", english: "Egypt", chinese: "埃及" },
  { code: "ES", english: "Spain", chinese: "西班牙" },
  { code: "ET", english: "Ethiopia", chinese: "埃塞俄比亚" },
  { code: "FI", english: "Finland", chinese: "芬兰" },
  { code: "FR", english: "France", chinese: "法国" },
  { code: "GB", english: "United Kingdom", chinese: "英国" },
  { code: "GH", english: "Ghana", chinese: "加纳" },
  { code: "GM", english: "Gambia", chinese: "冈比亚" },
  { code: "GR", english: "Greece", chinese: "希腊" },
  { code: "GT", english: "Guatemala", chinese: "危地马拉" },
  { code: "GY", english: "Guyana", chinese: "圭亚那" },
  { code: "HK", english: "Hong Kong", chinese: "中国香港" },
  { code: "HR", english: "Croatia", chinese: "克罗地亚" },
  { code: "HU", english: "Hungary", chinese: "匈牙利" },
  { code: "ID", english: "Indonesia", chinese: "印度尼西亚" },
  { code: "IE", english: "Ireland", chinese: "爱尔兰" },
  { code: "IL", english: "Israel", chinese: "以色列" },
  { code: "IS", english: "Iceland", chinese: "冰岛" },
  { code: "IT", english: "Italy", chinese: "意大利" },
  { code: "JM", english: "Jamaica", chinese: "牙买加" },
  { code: "JO", english: "Jordan", chinese: "约旦" },
  { code: "JP", english: "Japan", chinese: "日本" },
  { code: "KE", english: "Kenya", chinese: "肯尼亚" },
  { code: "KH", english: "Cambodia", chinese: "柬埔寨" },
  { code: "KR", english: "South Korea", chinese: "韩国" },
  { code: "KW", english: "Kuwait", chinese: "科威特" },
  { code: "LC", english: "Saint Lucia", chinese: "圣卢西亚" },
  { code: "LI", english: "Liechtenstein", chinese: "列支敦士登" },
  { code: "LK", english: "Sri Lanka", chinese: "斯里兰卡" },
  { code: "LT", english: "Lithuania", chinese: "立陶宛" },
  { code: "LU", english: "Luxembourg", chinese: "卢森堡" },
  { code: "LV", english: "Latvia", chinese: "拉脱维亚" },
  { code: "MA", english: "Morocco", chinese: "摩洛哥" },
  { code: "MD", english: "Moldova", chinese: "摩尔多瓦" },
  { code: "MG", english: "Madagascar", chinese: "马达加斯加" },
  { code: "MK", english: "North Macedonia", chinese: "北马其顿" },
  { code: "MN", english: "Mongolia", chinese: "蒙古" },
  { code: "MO", english: "Macau", chinese: "中国澳门" },
  { code: "MT", english: "Malta", chinese: "马耳他" },
  { code: "MU", english: "Mauritius", chinese: "毛里求斯" },
  { code: "MX", english: "Mexico", chinese: "墨西哥" },
  { code: "MY", english: "Malaysia", chinese: "马来西亚" },
  { code: "NA", english: "Namibia", chinese: "纳米比亚" },
  { code: "NG", english: "Nigeria", chinese: "尼日利亚" },
  { code: "NL", english: "Netherlands", chinese: "荷兰" },
  { code: "NO", english: "Norway", chinese: "挪威" },
  { code: "NZ", english: "New Zealand", chinese: "新西兰" },
  { code: "OM", english: "Oman", chinese: "阿曼" },
  { code: "PA", english: "Panama", chinese: "巴拿马" },
  { code: "PE", english: "Peru", chinese: "秘鲁" },
  { code: "PH", english: "Philippines", chinese: "菲律宾" },
  { code: "PL", english: "Poland", chinese: "波兰" },
  { code: "PT", english: "Portugal", chinese: "葡萄牙" },
  { code: "PY", english: "Paraguay", chinese: "巴拉圭" },
  { code: "QA", english: "Qatar", chinese: "卡塔尔" },
  { code: "RO", english: "Romania", chinese: "罗马尼亚" },
  { code: "RS", english: "Serbia", chinese: "塞尔维亚" },
  { code: "RW", english: "Rwanda", chinese: "卢旺达" },
  { code: "SA", english: "Saudi Arabia", chinese: "沙特阿拉伯" },
  { code: "SE", english: "Sweden", chinese: "瑞典" },
  { code: "SG", english: "Singapore", chinese: "新加坡" },
  { code: "SI", english: "Slovenia", chinese: "斯洛文尼亚" },
  { code: "SK", english: "Slovakia", chinese: "斯洛伐克" },
  { code: "SN", english: "Senegal", chinese: "塞内加尔" },
  { code: "SV", english: "El Salvador", chinese: "萨尔瓦多" },
  { code: "TH", english: "Thailand", chinese: "泰国" },
  { code: "TN", english: "Tunisia", chinese: "突尼斯" },
  { code: "TR", english: "Turkey", chinese: "土耳其" },
  { code: "TT", english: "Trinidad and Tobago", chinese: "特立尼达和多巴哥" },
  { code: "TZ", english: "Tanzania", chinese: "坦桑尼亚" },
  { code: "UY", english: "Uruguay", chinese: "乌拉圭" },
  { code: "UZ", english: "Uzbekistan", chinese: "乌兹别克斯坦" },
  { code: "VN", english: "Vietnam", chinese: "越南" },
  { code: "ZA", english: "South Africa", chinese: "南非" },
  { code: "BD", english: "Bangladesh", chinese: "孟加拉国" },
  { code: "BJ", english: "Benin", chinese: "贝宁" },
  { code: "MC", english: "Monaco", chinese: "摩纳哥" },
  { code: "NE", english: "Niger", chinese: "尼日尔" },
  { code: "SM", english: "San Marino", chinese: "圣马力诺" },
  { code: "AZ", english: "Azerbaijan", chinese: "阿塞拜疆" },
  { code: "BN", english: "Brunei", chinese: "文莱" },
  { code: "BT", english: "Bhutan", chinese: "不丹" },
  { code: "AO", english: "Angola", chinese: "安哥拉" },
  { code: "DZ", english: "Algeria", chinese: "阿尔及利亚" },
  { code: "TW", english: "Taiwan", chinese: "中国台湾" },
  { code: "BS", english: "Bahamas", chinese: "巴哈马" },
  { code: "BW", english: "Botswana", chinese: "博茨瓦纳" },
  { code: "GA", english: "Gabon", chinese: "加蓬" },
  { code: "LA", english: "Laos", chinese: "老挝" },
  { code: "MZ", english: "Mozambique", chinese: "莫桑比克" },
  { code: "KZ", english: "Kazakhstan", chinese: "哈萨克斯坦" },
  { code: "PK", english: "Pakistan", chinese: "巴基斯坦" },
  { code: "BR", english: "Brazil", chinese: "巴西" },
  { code: "MQ", english: "France", chinese: "法国马提尼克岛" },
  { code: "IN", english: "India", chinese: "印度" },
  { code: "GE", english: "Georgia", chinese: "格鲁吉亚" },
  { code: "HN", english: "Honduras", chinese: "洪都拉斯" },
  { code: "PR", english: "Puerto Rico", chinese: "波多黎各" },
  { code: "KN", english: "St. Kitts & Nevis", chinese: "圣基茨和尼维斯" },
  { code: "LR", english: "Liberia", chinese: "利比里亚" },
  { code: "UA", english: "Ukraine", chinese: "乌克兰" },
  { code: "BY", english: "Belarus", chinese: "白俄罗斯" },
  { code: "CN", english: "China", chinese: "中国" },
  { code: "RU", english: "Russia", chinese: "俄罗斯" },
  { code: "SX", english: "Sint Maarten", chinese: "荷属圣马丁" },
  { code: "KG", english: "Kyrgyz Republic", chinese: "吉尔吉斯斯坦" },
  { code: "AD", english: "Andorra", chinese: "安道尔" },
  { code: "BB", english: "Barbados", chinese: "巴巴多斯" },
  { code: "MV", english: "Maldives", chinese: "马尔代夫" },
  { code: "PF", english: "French Polynesia", chinese: "法属波利尼西亚" },
  { code: "NP", english: "Nepal", chinese: "尼泊尔" },
  { code: "RE", english: "Reunion", chinese: "留尼汪" },
  { code: "GN", english: "Guinea", chinese: "几内亚" },
  { code: "FJ", english: "Fiji", chinese: "斐济" },
  { code: "UG", english: "Uganda", chinese: "乌干达" },
  { code: "GP", english: "Guadeloupe", chinese: "瓜地罗普" },
  { code: "LB", english: "Lebanon", chinese: "黎巴嫩" },
  { code: "GD", english: "Grenada", chinese: "格林纳达" },
  { code: "MP", english: "Northern Mariana Islands", chinese: "北马里亚纳群岛" },
  { code: "BF", english: "Burkina Faso", chinese: "布基纳法索" },
  { code: "PG", english: "Papua New Guinea", chinese: "巴布亚新几内亚" },
  { code: "XX", english: "Other", chinese: "未知国家、地区及组织" },
  { code: "AF", english: "Afghanistan", chinese: "阿富汗" },
  { code: "AI", english: "Anguilla", chinese: "安圭拉" },
  { code: "AS", english: "American Samoa", chinese: "美属萨摩亚" },
  { code: "AW", english: "Aruba", chinese: "阿鲁巴" },
  { code: "AX", english: "Åland Islands", chinese: "奥兰群岛" },
  { code: "BI", english: "Burundi", chinese: "布隆迪" },
  { code: "BL", english: "Saint Barthélemy", chinese: "圣巴泰勒米" },
  { code: "BM", english: "Bermuda", chinese: "百慕大" },
  { code: "BQ", english: "Bonaire, Sint Eustatius and Saba", chinese: "荷兰加勒比区" },
  { code: "BV", english: "Bouvet Island", chinese: "布韦岛" },
  { code: "CC", english: "Cocos (Keeling) Islands", chinese: "科科斯（基林）群岛" },
  { code: "CD", english: "Congo, Democratic Republic of the", chinese: "刚果民主共和国" },
  { code: "CF", english: "Central African Republic", chinese: "中非共和国" },
  { code: "CG", english: "Congo", chinese: "刚果共和国" },
  { code: "CK", english: "Cook Islands", chinese: "库克群岛" },
  { code: "CM", english: "Cameroon", chinese: "喀麦隆" },
  { code: "CU", english: "Cuba", chinese: "古巴" },
  { code: "CV", english: "Cabo Verde", chinese: "佛得角" },
  { code: "CW", english: "Curaçao", chinese: "库拉索" },
  { code: "CX", english: "Christmas Island", chinese: "圣诞岛" },
  { code: "DJ", english: "Djibouti", chinese: "吉布提" },
  { code: "DM", english: "Dominica", chinese: "多米尼克" },
  { code: "EA", english: "Ceuta, Melilla", chinese: "休达和梅利利亚" },
  { code: "EH", english: "Western Sahara", chinese: "西撒哈拉" },
  { code: "ER", english: "Eritrea", chinese: "厄立特里亚" },
  { code: "FK", english: "Falkland Islands", chinese: "福克兰群岛" },
  { code: "FM", english: "Micronesia", chinese: "密克罗尼西亚" },
  { code: "FO", english: "Faroe Islands", chinese: "法罗群岛" },
  { code: "GF", english: "French Guiana", chinese: "法属圭亚那" },
  { code: "GG", english: "Guernsey", chinese: "根西岛" },
  { code: "GI", english: "Gibraltar", chinese: "直布罗陀" },
  { code: "GL", english: "Greenland", chinese: "格陵兰" },
  { code: "GQ", english: "Equatorial Guinea", chinese: "赤道几内亚" },
  { code: "GS", english: "South Georgia and the South Sandwich Islands", chinese: "南乔治亚和南桑威奇群岛" },
  { code: "GU", english: "Guam", chinese: "关岛" },
  { code: "GW", english: "Guinea-Bissau", chinese: "几内亚比绍" },
  { code: "HM", english: "Heard Island and McDonald Islands", chinese: "赫德岛和麦克唐纳群岛" },
  { code: "IC", english: "Canary Islands", chinese: "加那利群岛" },
  { code: "IM", english: "Isle of Man", chinese: "马恩岛" },
  { code: "IO", english: "British Indian Ocean Territory", chinese: "英属印度洋领地" },
  { code: "IQ", english: "Iraq", chinese: "伊拉克" },
  { code: "IR", english: "Iran", chinese: "伊朗" },
  { code: "JE", english: "Jersey", chinese: "泽西岛" },
  { code: "KI", english: "Kiribati", chinese: "基里巴斯" },
  { code: "KM", english: "Comoros", chinese: "科摩罗" },
  { code: "KP", english: "North Korea", chinese: "朝鲜" },
  { code: "KY", english: "Cayman Islands", chinese: "开曼群岛" },
  { code: "LS", english: "Lesotho", chinese: "莱索托" },
  { code: "LY", english: "Libya", chinese: "利比亚" },
  { code: "ME", english: "Montenegro", chinese: "黑山" },
  { code: "MF", english: "Saint Martin", chinese: "法属圣马丁" },
  { code: "MH", english: "Marshall Islands", chinese: "马绍尔群岛" },
  { code: "ML", english: "Mali", chinese: "马里" },
  { code: "MM", english: "Myanmar", chinese: "缅甸" },
  { code: "MR", english: "Mauritania", chinese: "毛里塔尼亚" },
  { code: "MS", english: "Montserrat", chinese: "蒙特塞拉特" },
  { code: "MW", english: "Malawi", chinese: "马拉维" },
  { code: "NC", english: "New Caledonia", chinese: "新喀里多尼亚" },
  { code: "NF", english: "Norfolk Island", chinese: "诺福克岛" },
  { code: "NI", english: "Nicaragua", chinese: "尼加拉瓜" },
  { code: "NR", english: "Nauru", chinese: "瑙鲁" },
  { code: "NU", english: "Niue", chinese: "纽埃" },
  { code: "PM", english: "Saint Pierre and Miquelon", chinese: "圣皮埃尔和密克隆" },
  { code: "PN", english: "Pitcairn", chinese: "皮特凯恩群岛" },
  { code: "PS", english: "Palestine", chinese: "巴勒斯坦" },
  { code: "PW", english: "Palau", chinese: "帕劳" },
  { code: "SB", english: "Solomon Islands", chinese: "所罗门群岛" },
  { code: "SC", english: "Seychelles", chinese: "塞舌尔" },
  { code: "SD", english: "Sudan", chinese: "苏丹" },
  { code: "SH", english: "Saint Helena, Ascension and Tristan da Cunha", chinese: "圣赫勒拿、阿森松和特里斯坦-达库尼亚" },
  { code: "SJ", english: "Svalbard and Jan Mayen", chinese: "斯瓦尔巴和扬马延" },
  { code: "SL", english: "Sierra Leone", chinese: "塞拉利昂" },
  { code: "SO", english: "Somalia", chinese: "索马里" },
  { code: "SR", english: "Suriname", chinese: "苏里南" },
  { code: "SS", english: "South Sudan", chinese: "南苏丹" },
  { code: "ST", english: "Sao Tome and Principe", chinese: "圣多美和普林西比" },
  { code: "SY", english: "Syria", chinese: "叙利亚" },
  { code: "SZ", english: "Eswatini", chinese: "斯威士兰" },
  { code: "TC", english: "Turks and Caicos Islands", chinese: "特克斯和凯科斯群岛" },
  { code: "TD", english: "Chad", chinese: "乍得" },
  { code: "TF", english: "French Southern Territories", chinese: "法属南部领地" },
  { code: "TG", english: "Togo", chinese: "多哥" },
  { code: "TJ", english: "Tajikistan", chinese: "塔吉克斯坦" },
  { code: "TK", english: "Tokelau", chinese: "托克劳" },
  { code: "TL", english: "Timor-Leste", chinese: "东帝汶" },
  { code: "TM", english: "Turkmenistan", chinese: "土库曼斯坦" },
  { code: "TO", english: "Tonga", chinese: "汤加" },
  { code: "TV", english: "Tuvalu", chinese: "图瓦卢" },
  { code: "UM", english: "United States Minor Outlying Islands", chinese: "美国本土外小岛屿" },
  { code: "VA", english: "Holy See", chinese: "梵蒂冈" },
  { code: "VC", english: "Saint Vincent and the Grenadines", chinese: "圣文森特和格林纳丁斯" },
  { code: "VE", english: "Venezuela", chinese: "委内瑞拉" },
  { code: "VG", english: "Virgin Islands, British", chinese: "英属维尔京群岛" },
  { code: "VI", english: "Virgin Islands, U.S.", chinese: "美属维尔京群岛" },
  { code: "VU", english: "Vanuatu", chinese: "瓦努阿图" },
  { code: "WF", english: "Wallis and Futuna", chinese: "瓦利斯和富图纳" },
  { code: "WS", english: "Samoa", chinese: "萨摩亚" },
  { code: "YE", english: "Yemen", chinese: "也门" },
  { code: "YT", english: "Mayotte", chinese: "马约特" },
  { code: "ZM", english: "Zambia", chinese: "赞比亚" },
  { code: "ZW", english: "Zimbabwe", chinese: "津巴布韦" },
];

export const getCountryByCode = (code: string) => {
  // 根据 code 获取国家信息，如果没有，就返回原始的 code
  const country = countryList.find((item) => item.code === code);
  if (country) {
    return country;
  }
  return { code, english: code, chinese: code };
};
