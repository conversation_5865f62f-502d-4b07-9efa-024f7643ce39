const priceProd = [
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 2990,
    id: 111064,
    availableTimes: 2000,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 1990,
    id: 111063,
    availableTimes: 900,
    downloadTimes: 1200,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 990,
    id: 111061,
    availableTimes: 300,
    downloadTimes: 500,
  },
];

const priceTest = [
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 2990,
    id: 111060,
    availableTimes: 2000,
    downloadTimes: -1,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 1990,
    id: 111058,
    availableTimes: 900,
    downloadTimes: 1200,
  },
  {
    currency: "usd",
    type: "one_time",
    unit_amount: 990,
    id: 111059,
    availableTimes: 300,
    downloadTimes: 500,
  },
];

export const priceList = process.env.NODE_ENV === "production" ? priceProd : priceTest;

/**
 * 根据价格 id 获取产品价格
 */
export const getPriceByPayProPriceId = (priceId) => {
  return priceList.find((price) => price.id == priceId);
};
