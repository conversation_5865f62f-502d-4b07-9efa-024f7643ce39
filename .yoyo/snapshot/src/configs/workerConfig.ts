// 保存文件的 worker
//-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-

// 调用保存文件到 R2 的 worker 请求参数格式
// const workerRequestData = {
//   bucketName: `love-study-ai`,
//   files: [
//     {
//       fileKey: `texts/test.txt`,
//       fileUrl: `https://test.com/test.txt`,
//       fileContentType: `text/plain`,
//     },
//   ],
//   callbackUrl: "", // 需要写回调地址接收保存完成的通知，根据需求写相应代码
//   storageUrl: "",
// };

// 保存文件到 R2 的 worker
const fileSaveWorkerTest = "https://upload-file-to-r2-v3-test-0311.qiayue9582.workers.dev";
const fileSaveWorkerProd = "https://upload-file-to-r2-v3-0311.qiayue9582.workers.dev";

// 根据环境变量选择调用保存文件到 R2 的 worker 地址，POST 请求
export const fileSaveWorker = process.env.NODE_ENV === "production" ? fileSaveWorkerProd : fileSaveWorkerTest;
