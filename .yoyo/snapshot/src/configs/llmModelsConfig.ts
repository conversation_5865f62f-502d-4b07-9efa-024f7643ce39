// Configuration for available LLM models
// This avoids the need for a database table

export const availableLlmModels = [
  {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    maxTokens: 8192,
    temperature: 0.7,
    supportJson: true
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    maxTokens: 8192,
    temperature: 0.7,
    supportJson: true
  }
];

/**
 * 获取模型的默认参数配置
 * @param modelId - 模型ID
 * @returns 模型参数配置对象或null
 */
export function getModelParams(modelId: string): {
  maxTokens: number;
  temperature: number;
  supportJson: boolean;
} | null {
  const model = availableLlmModels.find(m => m.id === modelId);
  if (!model) {
    return null;
  }
  
  return {
    maxTokens: model.maxTokens,
    temperature: model.temperature,
    supportJson: model.supportJson
  };
}

export const getDefaultLlmModel = () => {
  return 'gpt-4o-mini';
}; 