'use client'
import React, { useState, useEffect } from 'react';
import { useRouter } from "next/navigation";
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { ArrowUpIcon, PlusCircleIcon, CheckCircleIcon, ClockIcon } from "@heroicons/react/24/outline";
import Link from 'next/link';
import HeaderManage from "~/components/common/HeaderManage";

const DashboardComponent = ({
  locale,
}) => {
  const router = useRouter();
  const [pagePath] = useState("dashboard");

  const {
    userData,
    setShowLoadingModal,
  } = useCommonContext();

  const [authStatus, setAuthStatus] = useState('pending');

  // 仪表盘统计数据
  const [stats, setStats] = useState({
    totalGames: 0,
    publishedPages: 0,
    draftPages: 0,
    totalTemplates: 0,
    totalSites: 0
  });
  const [recentPages, setRecentPages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const checkAuth = async () => {
      if (!userData?.user_id) {
        // userData is not yet loaded, stay in pending state and return
        setAuthStatus('pending');
        return;
      }

      try {
        const isAdmin = await checkAdminUser(userData);

        if (!isAdmin) {
          setAuthStatus('unauthorized');
        } else {
          setAuthStatus('authorized');
          fetchDashboardData();
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        setAuthStatus('unauthorized');
      }
    };

    const fetchDashboardData = async () => {
      setIsLoading(true);
      setError("");
      try {
        // 获取游戏总数
        const gamesRes = await fetch("/api/manage/games");
        let totalGames = 0;
        if (gamesRes.ok) {
          const gamesData = await gamesRes.json();
          totalGames = gamesData?.data?.resultList?.length || 0;
        } else {
          console.error("DashboardComponent - fetchDashboardData: Failed to fetch games.");
        }

        // 获取页面统计
        const pagesCountRes = await fetch("/api/manage/pages/count");
        let publishedPages = 0, draftPages = 0;
        if (pagesCountRes.ok) {
          const pagesCountData = await pagesCountRes.json();
          publishedPages = pagesCountData?.data?.published || 0;
          draftPages = pagesCountData?.data?.draft || 0;
        } else {
          console.error("DashboardComponent - fetchDashboardData: Failed to fetch pages count.");
        }

        // 获取模板总数
        const templatesRes = await fetch("/api/manage/templates");
        let totalTemplates = 0;
        if (templatesRes.ok) {
          const templatesData = await templatesRes.json();
          totalTemplates = Array.isArray(templatesData?.data) ? templatesData.data.length : 0;
        } else {
          console.error("DashboardComponent - fetchDashboardData: Failed to fetch templates.");
        }

        // 获取站点总数
        const sitesRes = await fetch("/api/manage/sites/uid", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'list',
            data: {
              page: 1,
              pageSize: 100
            }
          })
        });
        let totalSites = 0;
        if (sitesRes.ok) {
          const sitesData = await sitesRes.json();
          totalSites = sitesData?.data?.data?.resultList?.length || 0;
        } else {
          console.error("DashboardComponent - fetchDashboardData: Failed to fetch sites.");
        }

        // 获取最近生成页面（取前5条）
        const recentPagesRes = await fetch("/api/manage/pages?pageSize=5&page=1");
        let recentPagesList = [];
        if (recentPagesRes.ok) {
          const recentPagesData = await recentPagesRes.json();
          recentPagesList = recentPagesData?.resultList || [];
        } else {
          console.error("DashboardComponent - fetchDashboardData: Failed to fetch recent pages.");
        }

        setStats({
          totalGames,
          publishedPages,
          draftPages,
          totalTemplates,
          totalSites
        });
        setRecentPages(recentPagesList);
      } catch (err) {
        console.error("DashboardComponent - fetchDashboardData: Error caught:", err);
        setError("数据加载失败，请稍后重试。");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [userData]);

  useEffect(() => {
    return () => {
      if (authStatus !== 'pending') {
        // setShowLoadingModal(false); // Consider if this is needed on unmount
      }
    };
  }, [authStatus, setShowLoadingModal]);

  if (authStatus === 'pending') {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
        <p className="text-gray-500">Loading dashboard...</p>
      </div>
    );
  }

  if (authStatus === 'unauthorized') {
    return (
      <div className="flex flex-col justify-center items-center min-h-[calc(100vh-100px)] text-center px-4">
        <h2 className="text-2xl font-semibold text-red-600 mb-2">Access Denied</h2>
        <p className="text-gray-700">You do not have permission to view this page.</p>
        <p className="text-gray-500 mt-1">Please log in with an administrator account.</p>
      </div>
    );
  }

  return (
    <>
      <HeaderManage locale={locale} page="manage/dashboard" />
      <div className="container mx-auto px-4 py-8">
        {error && <div className="text-red-500 mb-4">{error}</div>}
        <h1 className="text-2xl font-bold mb-6">管理控制台</h1>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-gray-500 text-sm font-medium">游戏总数</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.totalGames}</p>
            <Link href={`/${locale}/manage/games`} className="text-blue-500 text-sm hover:underline mt-2 inline-block">
              查看全部 →
            </Link>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-gray-500 text-sm font-medium">已发布页面数</h3>
            <p className="text-3xl font-bold text-green-600">{stats.publishedPages}</p>
            <Link href={`/${locale}/manage/pages`} className="text-blue-500 text-sm hover:underline mt-2 inline-block">
              查看全部 →
            </Link>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-gray-500 text-sm font-medium">模板总数</h3>
            <p className="text-3xl font-bold text-purple-600">{stats.totalTemplates}</p>
            <Link href={`/${locale}/manage/templates`} className="text-blue-500 text-sm hover:underline mt-2 inline-block">
              查看全部 →
            </Link>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="text-gray-500 text-sm font-medium">站点总数</h3>
            <p className="text-3xl font-bold text-orange-600">{stats.totalSites}</p>
            <Link href={`/${locale}/manage/sites`} className="text-blue-500 text-sm hover:underline mt-2 inline-block">
              查看全部 →
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">快捷操作</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => router.push(`/${locale}/manage/games/new`)}
              className="p-4 bg-blue-50 text-blue-700 rounded-lg border border-blue-200 hover:bg-blue-100 flex flex-col items-center justify-center"
            >
              <span className="text-lg font-medium">添加新游戏</span>
              <span className="text-sm text-gray-500 mt-1">创建新的游戏记录</span>
            </button>

            <button
              onClick={() => router.push(`/${locale}/manage/pages/`)}
              className="p-4 bg-green-50 text-green-700 rounded-lg border border-green-200 hover:bg-green-100 flex flex-col items-center justify-center"
            >
              <span className="text-lg font-medium">生成新页面</span>
              <span className="text-sm text-gray-500 mt-1">生成新的落地页</span>
            </button>

            <button
              onClick={() => router.push(`/${locale}/manage/templates/new`)}
              className="p-4 bg-purple-50 text-purple-700 rounded-lg border border-purple-200 hover:bg-purple-100 flex flex-col items-center justify-center"
            >
              <span className="text-lg font-medium">添加新模板</span>
              <span className="text-sm text-gray-500 mt-1">创建新的页面模板</span>
            </button>

            <button
              onClick={() => router.push(`/${locale}/manage/sites/new`)}
              className="p-4 bg-orange-50 text-orange-700 rounded-lg border border-orange-200 hover:bg-orange-100 flex flex-col items-center justify-center"
            >
              <span className="text-lg font-medium">添加新站点</span>
              <span className="text-sm text-gray-500 mt-1">配置新的站点设置</span>
            </button>
          </div>
        </div>

        {/* Recent Pages */}
        <div>
          <h2 className="text-xl font-semibold mb-4">最近生成的页面</h2>
          {isLoading ? (
            <p className="text-gray-500">加载中...</p>
          ) : (
            <div className="bg-white rounded-lg shadow border border-gray-200 overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0 z-10">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">标题</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">游戏</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">创建日期</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">状态</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {recentPages.map((page) => (
                    <tr key={page.id || page.uid} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap max-w-xs truncate">
                        <div className="text-sm font-medium text-gray-900 truncate">{page.title}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap max-w-xs truncate">
                        <div className="text-sm text-gray-500 truncate">{page.game_keyword || page.game || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap max-w-xs truncate">
                        <div className="text-sm text-gray-500 truncate">{page.created_at ? new Date(page.created_at).toLocaleDateString() : '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap max-w-xs truncate">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${page.status === 1 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>{page.status === 1 ? '已发布' : '草稿'}</span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
                <Link
                  href={`/${locale}/manage/pages`}
                  className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                >
                  查看全部页面 →
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}

export default DashboardComponent; 