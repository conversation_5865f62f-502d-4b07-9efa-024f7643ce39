'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import HeaderManage from '~/components/common/HeaderManage';
import { Prompt } from '~/utils/types/game';
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { toast } from "react-toastify";

interface PromptListComponentProps {
  locale: string;
}

const PromptListComponent = ({ locale }: PromptListComponentProps) => {
  const router = useRouter();
  const { userData, setShowLoadingModal } = useCommonContext();
  const [authStatus, setAuthStatus] = useState('pending');
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPrompts, setTotalPrompts] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  
  // Search state
  const [keyFilter, setKeyFilter] = useState('');
  const [descriptionFilter, setDescriptionFilter] = useState('');
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);
  
  // Check user authorization
  useEffect(() => {
    // TEMPORARY: Force authentication for testing
    setAuthStatus('authorized');
    setShowLoadingModal(false);
    
    /* Original authentication logic - comment out for testing
    if (userData && Object.keys(userData).length > 0) {
      if (userData.hasOwnProperty('role')) {
        if (checkAdminUser(userData)) {
          setAuthStatus('authorized');
          setShowLoadingModal(false);
        } else {
          setAuthStatus('unauthorized');
          setShowLoadingModal(false);
        }
      } else {
        setAuthStatus('pending');
        setShowLoadingModal(true);
      }
    } else if (userData === undefined) {
      setAuthStatus('pending');
      setShowLoadingModal(true);
    } else {
      setAuthStatus('unauthorized');
      setShowLoadingModal(false);
    }
    */
  }, [userData, setShowLoadingModal]);
  
  // Fetch prompts with filters
  const fetchPrompts = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams({
        page: currentPage.toString(),
        pageSize: pageSize.toString()
      });
      
      if (keyFilter) {
        queryParams.append('key', keyFilter);
      }
      
      if (descriptionFilter) {
        queryParams.append('description', descriptionFilter);
      }
      
      const response = await fetch(`/api/manage/prompts?${queryParams.toString()}`);
      const data = await response.json();
      
      if (response.ok) {
        setPrompts(data.data.prompts);
        setTotalPrompts(data.data.total);
      } else {
        setError(data.message || 'Failed to load prompts');
        toast.error(data.message || 'Failed to load prompts');
      }
    } catch (err) {
      setError('An error occurred while fetching prompts');
      toast.error('An error occurred while fetching prompts');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch prompts when page, filters, or auth status changes
  useEffect(() => {
    if (authStatus !== 'authorized') return;
    fetchPrompts();
  }, [currentPage, pageSize, authStatus]);
  
  // Handle key filter change with debounce
  const handleKeyFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setKeyFilter(value);
    
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    setSearchTimeout(
      setTimeout(() => {
        setCurrentPage(1); // Reset to first page
        fetchPrompts();
      }, 500)
    );
  };
  
  // Handle description filter change with debounce
  const handleDescriptionFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDescriptionFilter(value);
    
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    setSearchTimeout(
      setTimeout(() => {
        setCurrentPage(1); // Reset to first page
        fetchPrompts();
      }, 500)
    );
  };
  
  // Handle delete prompt
  const handleDeletePrompt = async (id: number, key: string) => {
    if (!confirm(`Are you sure you want to delete the prompt "${key}"?`)) {
      return;
    }
    
    try {
      const response = await fetch(`/api/manage/prompts/${id}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success('Prompt deleted successfully');
        fetchPrompts(); // Refresh the list
      } else {
        toast.error(data.message || 'Failed to delete prompt');
      }
    } catch (err) {
      toast.error('An error occurred while deleting the prompt');
      console.error(err);
    }
  };
  
  // Calculate total pages
  const totalPages = Math.ceil(totalPrompts / pageSize);
  
  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const range = [];
    const showPages = 5; // Number of page links to show
    
    let start = Math.max(1, currentPage - Math.floor(showPages / 2));
    let end = Math.min(totalPages, start + showPages - 1);
    
    // Adjust start if end is at max
    if (end === totalPages) {
      start = Math.max(1, end - showPages + 1);
    }
    
    for (let i = start; i <= end; i++) {
      range.push(i);
    }
    
    return range;
  };
  
  // Conditional rendering based on authorization state
  if (authStatus === 'pending') {
    return (
      <>
        <HeaderManage locale={locale} page="prompts" />
        <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
          <p className="text-gray-500">Loading...</p>
        </div>
      </>
    );
  }
  
  if (authStatus === 'unauthorized') {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page="prompts" />
        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-100px)] text-center px-4">
          <h2 className="text-2xl font-semibold text-red-600 mb-2">Access Denied</h2>
          <p className="text-gray-700">You do not have permission to access this page.</p>
          <p className="text-gray-500 mt-1">Please log in with an administrator account.</p>
        </div>
      </>
    );
  }
  
  return (
    <>
      <HeaderManage locale={locale} page="prompts" />
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6 flex justify-between items-center">
          <h1 className="text-2xl font-bold">Prompts</h1>
          <Link
            href={`/${locale}/manage/prompts/new`}
            className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded"
          >
            Add New Prompt
          </Link>
        </div>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        
        {/* Search filters */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="keyFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Key
            </label>
            <input
              type="text"
              id="keyFilter"
              value={keyFilter}
              onChange={handleKeyFilterChange}
              placeholder="Search by prompt key..."
              className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="descriptionFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Description
            </label>
            <input
              type="text"
              id="descriptionFilter"
              value={descriptionFilter}
              onChange={handleDescriptionFilterChange}
              placeholder="Search by description..."
              className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        
        {/* Prompts table */}
        <div className="overflow-x-auto bg-white shadow rounded">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Version</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    Loading prompts...
                  </td>
                </tr>
              ) : prompts.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    No prompts found
                  </td>
                </tr>
              ) : (
                prompts.map((prompt) => (
                  <tr key={prompt.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {prompt.key}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {prompt.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {prompt.version || '1.0'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(prompt.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link 
                          href={`/${locale}/manage/prompts/${prompt.id}`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => handleDeletePrompt(prompt.id, prompt.key)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {!isLoading && totalPages > 1 && (
          <div className="flex justify-center mt-6">
            <nav className="flex items-center">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1 rounded-l border ${
                  currentPage === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-blue-600 hover:bg-blue-50'
                }`}
              >
                Previous
              </button>
              
              {getPageNumbers().map(pageNum => (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 border-t border-b ${
                    currentPage === pageNum
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  {pageNum}
                </button>
              ))}
              
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className={`px-3 py-1 rounded-r border ${
                  currentPage === totalPages
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-white text-blue-600 hover:bg-blue-50'
                }`}
              >
                Next
              </button>
            </nav>
          </div>
        )}
      </div>
    </>
  );
};

export default PromptListComponent; 