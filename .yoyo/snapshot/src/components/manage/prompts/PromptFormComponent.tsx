'use client';
import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import HeaderManage from '~/components/common/HeaderManage';
import { Prompt } from '~/utils/types/game';
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { toast } from "react-toastify";

interface PromptFormComponentProps {
  locale: string;
  promptId?: string;
}

const PromptFormComponent = ({ locale, promptId }: PromptFormComponentProps) => {
  const router = useRouter();
  const { userData, setShowLoadingModal } = useCommonContext();
  const [authStatus, setAuthStatus] = useState('pending');
  
  // Form state
  const [formData, setFormData] = useState({
    key: '',
    description: '',
    llm_model_compatibility: [] as string[],
    version: '1.0',
    content_template: ''
  });
  
  // Available LLM models for selection
  const [availableModels] = useState([
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-4-turbo',
    'claude-2',
    'claude-instant',
    'llama-2-70b',
  ]);
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  
  // Check user authorization
  useEffect(() => {
    // TEMPORARY: Force authentication for testing
    setAuthStatus('authorized');
    setShowLoadingModal(false);
    
    /* Original authentication logic - comment out for testing
    if (userData && Object.keys(userData).length > 0) {
      if (userData.hasOwnProperty('role')) {
        if (checkAdminUser(userData)) {
          setAuthStatus('authorized');
          setShowLoadingModal(false);
        } else {
          setAuthStatus('unauthorized');
          setShowLoadingModal(false);
        }
      } else {
        setAuthStatus('pending');
        setShowLoadingModal(true);
      }
    } else if (userData === undefined) {
      setAuthStatus('pending');
      setShowLoadingModal(true);
    } else {
      setAuthStatus('unauthorized');
      setShowLoadingModal(false);
    }
    */
  }, [userData, setShowLoadingModal]);
  
  // Fetch prompt data if in edit mode
  useEffect(() => {
    if (!promptId || promptId === 'new' || authStatus !== 'authorized') return;
    
    setIsEditMode(true);
    setIsLoading(true);
    setError(null);
    
    const fetchPrompt = async () => {
      try {
        const response = await fetch(`/api/manage/prompts/${promptId}`);
        const data = await response.json();
        
        if (response.ok) {
          const prompt = data.data;
          
          // Convert llm_model_compatibility from string or object to array
          let modelCompatibility = prompt.llm_model_compatibility || [];
          
          // Handle if it's a string (JSON)
          if (typeof modelCompatibility === 'string') {
            try {
              modelCompatibility = JSON.parse(modelCompatibility);
            } catch (e) {
              console.error('Error parsing model compatibility:', e);
              modelCompatibility = [];
            }
          }
          
          // Set form data
          setFormData({
            key: prompt.key || '',
            description: prompt.description || '',
            llm_model_compatibility: Array.isArray(modelCompatibility) ? modelCompatibility : [],
            version: prompt.version || '1.0',
            content_template: prompt.content_template || ''
          });
        } else {
          setError(data.message || 'Failed to load prompt');
          toast.error(data.message || 'Failed to load prompt');
        }
      } catch (err) {
        setError('An error occurred while fetching prompt data');
        toast.error('An error occurred while fetching prompt data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPrompt();
  }, [promptId, authStatus]);
  
  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle model selection
  const handleModelSelection = (model: string) => {
    setFormData(prev => {
      const currentModels = [...prev.llm_model_compatibility];
      
      // Toggle selection
      if (currentModels.includes(model)) {
        return {
          ...prev,
          llm_model_compatibility: currentModels.filter(m => m !== model)
        };
      } else {
        return {
          ...prev,
          llm_model_compatibility: [...currentModels, model]
        };
      }
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (isSaving) return;
    
    // Validate form data
    if (!formData.key.trim()) {
      toast.error('Prompt key is required');
      return;
    }
    
    if (!formData.content_template.trim()) {
      toast.error('Content template is required');
      return;
    }
    
    setIsSaving(true);
    setError(null);
    
    try {
      // Prepare data for API
      const apiData = {
        key: formData.key.trim(),
        description: formData.description.trim() || null,
        llm_model_compatibility: formData.llm_model_compatibility.length > 0 
          ? formData.llm_model_compatibility 
          : null,
        version: formData.version.trim() || '1.0',
        content_template: formData.content_template.trim()
      };
      
      const response = await fetch(
        isEditMode ? `/api/manage/prompts/${promptId}` : '/api/manage/prompts',
        {
          method: isEditMode ? 'PUT' : 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(apiData)
        }
      );
      
      const data = await response.json();
      
      if (response.ok) {
        toast.success(`Prompt ${isEditMode ? 'updated' : 'created'} successfully`);
        
        // Redirect to prompt list
        router.push(`/${locale}/manage/prompts`);
      } else {
        setError(data.message || `Failed to ${isEditMode ? 'update' : 'create'} prompt`);
        toast.error(data.message || `Failed to ${isEditMode ? 'update' : 'create'} prompt`);
      }
    } catch (err) {
      setError(`An error occurred while ${isEditMode ? 'updating' : 'creating'} the prompt`);
      toast.error(`An error occurred while ${isEditMode ? 'updating' : 'creating'} the prompt`);
      console.error(err);
    } finally {
      setIsSaving(false);
    }
  };
  
  // Conditional rendering based on authorization state
  if (authStatus === 'pending') {
    return (
      <>
        <HeaderManage locale={locale} page="prompts" />
        <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
          <p className="text-gray-500">Loading...</p>
        </div>
      </>
    );
  }
  
  if (authStatus === 'unauthorized') {
    return (
      <>
        <meta name="robots" content="noindex" />
        <HeaderManage locale={locale} page="prompts" />
        <div className="flex flex-col justify-center items-center min-h-[calc(100vh-100px)] text-center px-4">
          <h2 className="text-2xl font-semibold text-red-600 mb-2">Access Denied</h2>
          <p className="text-gray-700">You do not have permission to access this page.</p>
          <p className="text-gray-500 mt-1">Please log in with an administrator account.</p>
        </div>
      </>
    );
  }
  
  if (isEditMode && isLoading) {
    return (
      <>
        <HeaderManage locale={locale} page="prompts" />
        <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
          <p className="text-gray-500">Loading prompt data...</p>
        </div>
      </>
    );
  }
  
  return (
    <>
      <HeaderManage locale={locale} page="prompts" />
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">
              {isEditMode ? 'Edit Prompt' : 'Create New Prompt'}
            </h1>
            <Link 
              href={`/${locale}/manage/prompts`}
              className="text-blue-600 hover:text-blue-800"
            >
              Back to Prompt List
            </Link>
          </div>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4">
              <span className="block sm:inline">{error}</span>
            </div>
          )}
        </div>
        
        <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="key">
              Prompt Key *
            </label>
            <input
              id="key"
              name="key"
              type="text"
              value={formData.key}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="e.g., game_description_v1"
              required
              disabled={isEditMode}
              title={isEditMode ? "Prompt key cannot be changed after creation" : ""}
            />
            {isEditMode && (
              <p className="text-sm text-gray-500 mt-1">Prompt key cannot be changed after creation.</p>
            )}
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
              Description
            </label>
            <input
              id="description"
              name="description"
              type="text"
              value={formData.description}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="A brief description of what this prompt does"
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Compatible LLM Models
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {availableModels.map(model => (
                <div key={model} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`model-${model}`}
                    checked={formData.llm_model_compatibility.includes(model)}
                    onChange={() => handleModelSelection(model)}
                    className="mr-2"
                  />
                  <label htmlFor={`model-${model}`} className="text-sm text-gray-700">
                    {model}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="version">
              Version
            </label>
            <input
              id="version"
              name="version"
              type="text"
              value={formData.version}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="1.0"
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="content_template">
              Content Template *
            </label>
            <p className="text-sm text-gray-500 mb-2">
              Use {'{{parameter_name}}'} syntax for parameters in your prompt template.
            </p>
            <textarea
              id="content_template"
              name="content_template"
              value={formData.content_template}
              onChange={handleInputChange}
              className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline font-mono"
              placeholder="Enter your prompt template here..."
              rows={15}
              required
            />
          </div>
          
          <div className="flex items-center justify-between mt-8">
            <Link
              href={`/${locale}/manage/prompts`}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isSaving}
              className={`bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isSaving ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Prompt' : 'Create Prompt')}
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default PromptFormComponent; 