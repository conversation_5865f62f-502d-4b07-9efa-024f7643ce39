'use client'
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import HeaderManage from "~/components/common/HeaderManage";
import { Game, Template, Prompt } from '~/utils/types/game';
import { Site } from '~/types/site';
import { getPromptKeyNameMap } from '~/configs/promptsConfig';
import { availableLlmModels } from '~/configs/llmModelsConfig';

interface GameGeneratePagesComponentProps {
  locale: string;
  gameUid: string; // 使用UID标识游戏（唯一方式）
}

export const GameGeneratePagesComponent = ({ locale, gameUid }: GameGeneratePagesComponentProps) => {
  const router = useRouter();
  
  const [game, setGame] = useState<Game | null>(null);
  const [sites, setSites] = useState<Site[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [promptKeyNameMap, setPromptKeyNameMap] = useState<Record<string, string>>({});
  const [llmModels, setLlmModels] = useState<{id: string, name: string}[]>([]);
  
  const [isLoading, setIsLoading] = useState(true);
  const [sitesLoading, setSitesLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedSites, setSelectedSites] = useState<{ siteUid: string }[]>([]);
  
  // Generation states
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationResults, setGenerationResults] = useState<{
    success: any[];
    failed: any[];
    game?: {
      uid: string;
      keyword: string;
    };
  } | null>(null);
  const [generationError, setGenerationError] = useState('');
  const [showResults, setShowResults] = useState(false);
  
  // Load game data
  useEffect(() => {
    const fetchGameData = async () => {
      setIsLoading(true);
      try {
        // 使用UID路径获取游戏数据
        const apiUrl = `/api/manage/games/uid/${gameUid}`;
          
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            uid: gameUid,
            action: 'get'
          })
        });
        
        if (!response.ok) {
          throw new Error(`获取游戏数据失败: ${response.status}`);
        }
        
        const result = await response.json();
        if (result.code !== 200) {
          throw new Error(result.message || '获取游戏数据失败');
        }
        
        setGame(result.data);
      } catch (err) {
        //console.error('获取游戏数据出错:', err);
        setError('无法加载游戏数据，请重试。');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchGameData();
  }, [gameUid]);
  
  // Load sites, templates, prompts, and LLM models
  useEffect(() => {
    const fetchData = async () => {
      setSitesLoading(true);
      try {
        // 获取真实站点数据
        const sitesResponse = await fetch('/api/manage/sites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'list',
            data: {
              page: 1,
              pageSize: 100
            }
          })
        });
        
        if (!sitesResponse.ok) {
          throw new Error(`获取站点数据失败: ${sitesResponse.status}`);
        }
        
        const sitesResult = await sitesResponse.json();
        
        if (sitesResponse.ok && sitesResult.status === 200 && sitesResult.data && Array.isArray(sitesResult.data.sites)) {
          setSites(sitesResult.data.sites);
        } else {
          //console.error('站点API返回错误或数据格式不正确: ', sitesResult);
          if (!sitesResponse.ok) {
             throw new Error(`获取站点数据失败: ${sitesResponse.status}`);
          } else {
             throw new Error(sitesResult.message || '获取站点列表失败或数据格式不正确');
          }
        }
        
        // 模板数据
        const templatesResponse = await fetch('/api/manage/templates');
        if (templatesResponse.ok) {
          const templatesResult = await templatesResponse.json();
          if (templatesResult.code === 200 && templatesResult.data) {
            setTemplates(templatesResult.data.resultList || []);
          }
        }
        
        // Prompts数据从配置获取
        setPromptKeyNameMap(getPromptKeyNameMap());
        setPrompts(
          Object.entries(getPromptKeyNameMap()).map(([key, name], index) => ({
            id: index + 1,
            key,
            name,
            is_delete: false
          }))
        );
        
        // LLM模型从配置获取
        setLlmModels(availableLlmModels);
      } catch (err) {
        //console.error('加载站点数据出错:', err);
        setError(prev => prev ? `${prev}; 无法加载站点数据` : '无法加载站点数据，请重试。');
      } finally {
        setSitesLoading(false);
      }
    };
    
    fetchData();
  }, []);
  
  // Add a site to the selection
  const addSite = () => {
    if (sitesLoading) {
      alert('站点数据加载中，请稍候');
      return;
    }
    if (sites.length === 0) {
      alert('没有可用站点');
      return;
    }
    const availableSites = sites.filter(site => !selectedSites.some(selected => selected.siteUid === site.uid));
    if (availableSites.length === 0) {
      alert('已选择所有可用站点');
      return;
    }
    const nextSite = availableSites[0];
    setSelectedSites([...selectedSites, { siteUid: nextSite.uid }]);
  };
  
  // Remove a site from the selection
  const removeSite = (index: number) => {
    setSelectedSites(selectedSites.filter((_, i) => i !== index));
  };
  
  // Update site selection
  const updateSiteSelection = (index: number, value: string) => {
    setSelectedSites(selectedSites.map((site, i) => (i === index ? { siteUid: value } : site)));
  };
  
  // Reset generation state
  const resetGenerationState = () => {
    setGenerationResults(null);
    setGenerationError('');
    setShowResults(false);
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    resetGenerationState();
    if (selectedSites.length === 0) {
      setGenerationError('请至少选择一个站点');
      //console.warn('Validation failed: No sites selected.'); // Log validation failure
      return;
    }
    if (!game?.keyword) {
      setGenerationError('游戏关键词不能为空');
      //console.warn('Validation failed: Game keyword is empty.'); // Log validation failure
      return;
    }
    
    // 获取选中的站点信息，包括域名
    const siteInfos = selectedSites.map(site => {
      const selectedSite = sites.find(s => s.uid === site.siteUid);
      return {
        siteUid: site.siteUid,
        domain: selectedSite?.domain || ''
      };
    }).filter(site => site.siteUid && site.domain);

    if (siteInfos.length === 0) {
      setGenerationError('没有有效的站点');
      //console.warn('Validation failed: No valid site infos found.'); // Log validation failure
      return;
    }

    // 检查每个站点是否已存在相同关键词且已上线的页面
    const checkResults = await Promise.all(siteInfos.map(async (site) => {
      try {
        const apiUrl = `/api/manage/pages?siteUid=${site.siteUid}&status=1&keyword=${encodeURIComponent(game.keyword)}`;
        const response = await fetch(apiUrl);
        const result = await response.json();
        if (result.status === 200 && result.resultList && result.resultList.length > 0) {
          return {
            siteUid: site.siteUid,
            domain: site.domain,
            exists: true,
            existingPage: result.resultList[0]
          };
        }
        return {
          siteUid: site.siteUid,
          domain: site.domain,
          exists: false
        };
      } catch (error) {
        //console.error(`Error during pre-check for site ${site.siteUid}:`, error); // Log pre-check error
        return {
          siteUid: site.siteUid,
          domain: site.domain,
          error: '检查页面存在性时出错'
        };
      }
    }));

    // 过滤出可以生成页面的站点
    const validSites = checkResults.filter(result => !result.exists && !result.error);
    const invalidSites = checkResults.filter(result => result.exists || result.error);

    if (validSites.length === 0) {
      setGenerationError('所有选中的站点都有相同关键词的已上线页面');
      //console.warn('Generation aborted: No valid sites left after pre-check.'); // Log abortion reason
      return;
    }
    
    const requestData = { 
      gameUid, 
      sites: validSites,
      gameInfo: {
        iframe_url: game.iframe_url || '',
        keyword: game.keyword
      }
    };
    
    setIsGenerating(true);
    try {
      const response = await fetch('/api/manage/pages/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });
      const result = await response.json();
      if (response.ok && result.status === 200) {
        // 合并生成结果和已存在的页面信息
        const successResults = result.data.success || [];
        const failedResults = [
          ...(result.data.failed || []),
          ...invalidSites.map(site => ({
            siteUid: site.siteUid,
            siteName: sites.find(s => s.uid === site.siteUid)?.name || '未知站点',
            error: site.exists ? '该站点已有相同关键词的页面' : site.error
          }))
        ];
        
        setGenerationResults({
          ...result.data,
          success: successResults,
          failed: failedResults
        });
        setShowResults(true);
      } else {
        setGenerationError(result.message || '生成页面时出错');
        //console.error('Generation failed with backend error:', result.message || '未知错误'); // Log backend error
      }
    } catch (error) {
      setGenerationError('页面生成请求失败，请检查网络连接');
      //console.error('Generation request failed (network/parsing error):', error); // Log network error
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Navigate to view a generated page
  const viewGeneratedPage = (pageUid: string) => {
    router.push(`/${locale}/manage/pages/${pageUid}`);
  };
  
  // Generate back link based on game UID
  const getBackLink = () => {
    return `/${locale}/manage/games/uid/${gameUid}`;
  };
  
  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page="games" />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <div className="mx-auto max-w-[80%]">
          <div className="mb-6">
            <h1 className="text-2xl font-bold">为游戏生成落地页</h1>
            <button
              onClick={() => router.push(`/${locale}/manage/games/`)}
              className="mt-2 text-blue-600 hover:underline"
            >
              ← 返回游戏列表
            </button>
          </div>
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <p>Loading...</p>
            </div>
          ) : game ? (
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-2">
                  游戏: {game.keyword}
                </h2>
                <div className="text-gray-600">
                  <div>ID: {game.id}</div>
                  {game.uid && <div>UID: {game.uid}</div>}
                  <div>关键词: {game.keyword}</div>
                </div>
              </div>
              
              {/* Reference Data */}
              <div className="mb-6">
                <h3 className="font-semibold mb-2">参考资料</h3>
                <textarea
                  value={game.reference_data || ''}
                  onChange={(e) => setGame({...game!, reference_data: e.target.value})}
                  rows={6}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="关于游戏的参考资料，用于LLM生成内容"
                />
                <p className="text-sm text-gray-500 mt-1">
                  提供详细的游戏信息会帮助LLM生成更好的内容
                </p>
              </div>
              
              {/* Selected Sites */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold">目标站点</h3>
                  <button
                    type="button"
                    onClick={addSite}
                    className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded"
                    disabled={sites.length === 0 || sitesLoading}
                  >
                    {sitesLoading ? '加载中...' : '添加站点'}
                  </button>
                </div>
                
                {selectedSites.length === 0 ? (
                  <div className="p-4 bg-gray-50 text-gray-500 rounded text-center">
                    {sitesLoading 
                      ? '站点数据加载中...' 
                      : (sites.length === 0 ? '没有可用站点' : '请添加至少一个目标站点')}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {selectedSites.map((selected, index) => (
                        <div key={index} className="p-4 border rounded bg-gray-50 relative">
                          <button
                            type="button"
                            onClick={() => removeSite(index)}
                            className="absolute top-2 right-2 w-6 h-6 flex items-center justify-center bg-red-100 hover:bg-red-200 text-red-700 rounded-full"
                          >
                            ×
                          </button>
                          <div className="mb-3">
                            <label className="block mb-1 text-sm font-medium">站点</label>
                            <select
                            value={selected.siteUid}
                            onChange={e => updateSiteSelection(index, e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded"
                              disabled={sitesLoading || isGenerating}
                            >
                              {sitesLoading ? (
                                <option>加载中...</option>
                              ) : sites.length === 0 ? (
                                <option>没有可用站点</option>
                              ) : (
                              sites.map(site => (
                                <option key={site.uid} value={site.uid}>
                                    {site.name} ({site.domain})
                                  </option>
                                ))
                              )}
                            </select>
                          </div>
                            </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Generation Results */}
              {(isGenerating || generationResults || generationError) && (
                <div className="my-6 p-4 border rounded bg-gray-50">
                  <h3 className="font-semibold mb-2">生成状态</h3>
                  
                  {isGenerating && (
                    <div className="flex items-center text-blue-700 mb-2">
                      <div className="mr-2 h-5 w-5 border-t-2 border-blue-500 rounded-full animate-spin"></div>
                      <p>正在生成页面，请稍候...</p>
                    </div>
                  )}
                  
                  {generationError && (
                    <div className="p-3 mb-3 bg-red-100 text-red-700 rounded">
                      {generationError}
                    </div>
                  )}
                  
                  {generationResults && showResults && (
                    <div className="mt-3">
                      <div className="mb-3 text-green-700">
                        成功生成 {generationResults.success.length} 个页面
                        {generationResults.failed.length > 0 && `, ${generationResults.failed.length} 个失败`}
                      </div>
                      
                      {generationResults.success.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-semibold mb-2">成功生成的页面:</h4>
                          <div className="space-y-2">
                            {generationResults.success.map((result, idx) => (
                              <div key={idx} className="p-2 border rounded bg-white flex justify-between items-center">
                                <div>
                                  <span className="font-medium">{result.siteName}</span>
                                  <span className="text-sm text-gray-500 ml-2">({result.pageUid.substring(0, 8)}...)</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {generationResults.failed.length > 0 && (
                        <div>
                          <h4 className="text-sm font-semibold mb-2">生成失败的页面:</h4>
                          <div className="space-y-2">
                            {generationResults.failed.map((result, idx) => (
                              <div key={idx} className="p-2 border rounded bg-red-50 text-red-700">
                                <span className="font-medium">{result.siteName || '未知站点'}</span>
                                <span className="text-sm ml-2">错误: {result.error || '未知错误'}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {/* Submit button */}
              <div className="mt-6 flex justify-end">
                {/* Conditionally render the '取消' button */}
                {showResults && (
                  <button
                    type="reset"
                    onClick={resetGenerationState}
                    className="mr-3 px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded"
                  >
                    取消
                  </button>
                )}
                {/* Add the new '查看页面列表' button */}
                {showResults && (
                  <button
                    onClick={() => router.push(`/${locale}/manage/pages`)}
                    className="ml-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    查看
                  </button>
                )}
                {/* Conditionally render the '生成页面' button */}
                {!showResults && (
                  <button
                    type="submit"
                    className={`px-4 py-2 ${
                      isGenerating ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
                    } text-white rounded flex items-center`}
                    disabled={selectedSites.length === 0 || isGenerating}
                  >
                    {isGenerating ? (
                      <>
                        <div className="mr-2 h-4 w-4 border-t-2 border-white rounded-full animate-spin"></div>
                        正在生成...
                      </>
                    ) : (
                      <>生成页面</>
                    )}
                  </button>
                )}
              </div>
            </form>
          ) : (
            <div className="p-4 bg-red-100 text-red-700 rounded">
              找不到游戏数据
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default GameGeneratePagesComponent; 