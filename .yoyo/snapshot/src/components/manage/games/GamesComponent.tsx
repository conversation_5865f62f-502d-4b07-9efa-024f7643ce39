'use client'
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import GameListComponent from './GameListComponent';
import HeaderManage from "~/components/common/HeaderManage";
import { useCommonContext } from "~/context/common-context";
import { checkAdminUser } from "~/utils/checkWhiteUser";

const GamesComponent = ({ locale }) => {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState('unauthorized');
  const { userData, setShowLoadingModal } = useCommonContext();


  // Comment out the authentication check
  
  useEffect(() => {
    // Check authentication if needed
    const checkAuth = async () => {
      try {
        // In a real implementation, verify user permissions
        const isAdmin = await checkAdminUser(userData);
        if (!isAdmin) {
          setAuthStatus('unauthorized');
        } else {
          setAuthStatus('authorized');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        setAuthStatus('unauthorized');
      }
    };
    
    checkAuth();
  }, []);
  

  // Remove conditional rendering for loading and unauthorized
  // Always render the content
  return (
    <>
      <HeaderManage locale={locale} page="manage/games" />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">游戏管理</h1>
        <p className="text-gray-600 mb-8">管理所有游戏项目信息</p>
        <GameListComponent locale={locale} />
      </div>
    </>
  );
};

export default GamesComponent; 