'use client'
import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';

interface GameFormComponentProps {
  locale: string;
  gameUid?: string; // 使用UID标识游戏（唯一方式）
}

const GameFormComponent = ({ locale, gameUid = null }: GameFormComponentProps) => {
  const router = useRouter();
  const isEditing = !!gameUid;
  
  // Form state
  const [formData, setFormData] = useState<{
    keyword: string;
    iframe_url: string;
    reference_data: string;
    tags: string;
    uid?: string;  // 保存游戏的UID，用于更新操作
  }>({
    keyword: '',
    iframe_url: '',
    reference_data: '',
    tags: '',
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [submitSuccess, setSubmitSuccess] = useState(false);
  
  // Fetch game data if editing
  useEffect(() => {
    if (isEditing) {
      const fetchGame = async () => {
        setIsLoading(true);
        try {
          // 使用UID路径获取游戏数据
          const apiUrl = `/api/manage/games/uid/${gameUid}`;

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              uid: gameUid,
              action: 'get'
            })
          });
          
          if (!response.ok) {
            throw new Error(`获取游戏数据失败: ${response.status}`);
          }
          
          const result = await response.json();
          
          if (result.code !== 200) {
            throw new Error(result.message || '获取游戏数据失败');
          }
          
          const data = result.data;
          
          setFormData({
            keyword: data.keyword || '',
            iframe_url: data.iframe_url || '',
            reference_data: data.reference_data || '',
            tags: data.tags ? (typeof data.tags === 'string' ? data.tags : JSON.stringify(data.tags)) : '',
            uid: data.uid || null,
          });
        } catch (err) {
          console.error('获取游戏数据出错:', err);
          setError('无法加载游戏数据，请重试。');
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchGame();
    }
  }, [gameUid, isEditing]);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSubmitSuccess(false);
    
    try {
      const method = isEditing ? 'POST' : 'POST';
      let url;
      
      // 构建API URL
      if (isEditing) {
        // 编辑模式：优先使用表单中的UID，然后使用传入的参数
        url = `/api/manage/games/uid/${formData.uid || gameUid}`;
      } else {
        // 创建模式
        url = '/api/manage/games';
      }
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(isEditing ? {
          uid: formData.uid || gameUid,
          action: 'update',
          data: {
            keyword: formData.keyword,
            iframe_url: formData.iframe_url,
            reference_data: formData.reference_data,
            tags: formData.tags,
          }
        } : formData),
      });
      
      const result = await response.json();
      
      if (!response.ok || result.code !== 200) {
        throw new Error(result.message || `错误: ${response.status}`);
      }
      
      setSubmitSuccess(true);
      
      // 成功后重定向到游戏列表
      setTimeout(() => {
        router.push(`/${locale}/manage/games`);
      }, 1500);
    } catch (err) {
      console.error('提交表单出错:', err);
      setError(err instanceof Error ? err.message : '保存游戏失败，请重试。');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading && isEditing) {
    return (
      <div className="flex justify-center items-center min-h-[calc(100vh-100px)]">
        <p className="text-gray-500">加载中...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">{isEditing ? '编辑游戏' : '添加新游戏'}</h1>
      
      {/* Success message */}
      {submitSuccess && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded">
          {isEditing ? '游戏更新成功！' : '游戏创建成功！'} 正在返回列表...
        </div>
      )}
      
      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {/* UID display if available */}
        {formData.uid && (
          <div className="mb-4">
            <label className="block mb-1 font-medium">
              UID
            </label>
            <div className="p-2 bg-gray-100 border border-gray-300 rounded">
              {formData.uid}
            </div>
            <p className="text-sm text-gray-500 mt-1">
              游戏唯一标识符，由系统生成
            </p>
          </div>
        )}
        
        {/* Keyword */}
        <div className="mb-4">
          <label className="block mb-1 font-medium">
            关键词 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="keyword"
            value={formData.keyword}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="游戏关键词（唯一标识）"
            disabled={isLoading}
          />
          <p className="text-sm text-gray-500 mt-1">
            用于唯一标识游戏，不可重复
          </p>
        </div>
        
        {/* iFrame URL */}
        <div className="mb-4">
          <label className="block mb-1 font-medium">
            iframe URL <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="iframe_url"
            value={formData.iframe_url}
            onChange={handleChange}
            required
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="游戏iframe嵌入地址（可选）"
            disabled={isLoading}
          />
          {formData.iframe_url && (
            <div className="mt-2">
              <p className="text-gray-500 text-sm mb-1">预览：</p>
              <div className="w-full h-60 border overflow-hidden">
                <iframe
                  src={formData.iframe_url}
                  className="w-full h-full"
                  title={formData.keyword}
                  sandbox="allow-scripts allow-same-origin"
                />
              </div>
            </div>
          )}
        </div>
        
        {/* Reference Data */}
        <div className="mb-4">
          <label className="block mb-1 font-medium">
            参考资料 <span className="text-red-500">*</span>
          </label>
          <textarea
            name="reference_data"
            value={formData.reference_data}
            onChange={handleChange}
            rows={6}
            required
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="关于游戏的参考资料，用于LLM生成内容"
            disabled={isLoading}
          />
        </div>
        
        {/* Tags */}
        <div className="mb-4">
          <label className="block mb-1 font-medium">
            标签
          </label>
          <input
            type="text"
            name="tags"
            value={formData.tags}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="用逗号分隔的标签列表（例如：action, rpg, strategy）"
            disabled={isLoading}
          />
          <p className="text-sm text-gray-500 mt-1">
            用逗号分隔多个标签
          </p>
        </div>
        
        {/* Submit button */}
        <div className="mt-6 flex justify-end">
          <button
            type="button"
            onClick={() => router.push(`/${locale}/manage/games`)}
            className="mr-3 px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded"
            disabled={isLoading}
          >
            取消
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
            disabled={isLoading}
          >
            {isLoading ? '保存中...' : isEditing ? '更新游戏' : '创建游戏'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default GameFormComponent; 