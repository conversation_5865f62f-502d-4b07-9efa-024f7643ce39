import { useEffect, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '~/components/ui/card';

interface Site {
  uid: string;
  name: string;
}

interface SiteSelectorProps {
  onSiteSelect: (siteUid: string) => void;
}

export default function SiteSelector({ onSiteSelect }: SiteSelectorProps) {
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSites();
  }, []);

  const fetchSites = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/manage/sites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          data: { page: 1, pageSize: 100 }
        })
      });
      const data = await response.json();
      if (data.status === 200 && data.data?.sites) {
        setSites(data.data.sites);
      } else {
        setSites([]);
      }
    } catch (err) {
      setSites([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>选择站点</CardTitle>
      </CardHeader>
      <CardContent>
        <select
          className="w-full p-2 border border-gray-300 rounded"
          disabled={loading}
          onChange={e => onSiteSelect(e.target.value)}
          defaultValue=""
        >
          <option value="" disabled>请选择站点</option>
          {sites.map(site => (
            <option key={site.uid} value={site.uid}>{site.name}</option>
          ))}
        </select>
      </CardContent>
    </Card>
  );
} 