'use client'
import React, { useState } from 'react';
import { Card, CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { RecommendationsList } from './RecommendationsList';
import { RecommendationForm } from './RecommendationForm';
import { useCurrentSite } from '~/lib/hooks/useCurrentSite';
import { useToast } from '~/hooks/use-toast';
import { HeaderManage } from '../HeaderManage';
import SiteSelector from './SiteSelector';
import RecommendationZonesComponent from './RecommendationZonesComponent';
import { Recommendation } from './RecommendationsList';

interface Zone {
  id: string;
  name: string;
}

interface RecommendationsComponentProps {
  locale: string;
}

export function RecommendationsComponent({ locale }: RecommendationsComponentProps) {
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingRecommendation, setEditingRecommendation] = useState<number | null>(null);
  const [shouldRefresh, setShouldRefresh] = useState(false);
  const { site } = useCurrentSite();
  const { toast } = useToast();
  const [selectedSiteUid, setSelectedSiteUid] = useState<string>('');
  const [zones, setZones] = useState<Zone[]>([]);
  const [selectedZoneId, setSelectedZoneId] = useState<string>('');
  const [editingRecommendationData, setEditingRecommendationData] = useState<Recommendation | null>(null);
  const [isLoadingEditingData, setIsLoadingEditingData] = useState(false);

  const handleAddNew = () => {
    setIsAddingNew(true);
    setEditingRecommendation(null);
    setEditingRecommendationData(null);
  };

  const handleEdit = async (id: number) => {
    setIsLoadingEditingData(true);
    try {
      const res = await fetch('/api/manage/recommendations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'get', id }),
      });
      const data = await res.json();

      if (res.ok && data.status === 200 && data.data) {
        setEditingRecommendationData(data.data);
        setEditingRecommendation(id);
        setIsAddingNew(false);
      } else {
        console.error('Failed to fetch recommendation data:', data.message || res.statusText);
        toast({
          title: '错误',
          description: data.message || '获取推荐内容详情失败',
          variant: 'destructive',
        });
        setEditingRecommendation(null);
        setEditingRecommendationData(null);
      }
    } catch (error) {
      console.error('Error fetching recommendation data:', error);
      toast({
        title: '错误',
        description: '获取推荐内容详情时发生未知错误',
        variant: 'destructive',
      });
      setEditingRecommendation(null);
      setEditingRecommendationData(null);
    } finally {
      setIsLoadingEditingData(false);
    }
  };

  const handleCancel = () => {
    setIsAddingNew(false);
    setEditingRecommendation(null);
    setEditingRecommendationData(null);
  };

  const handleSaveSuccess = () => {
    setIsAddingNew(false);
    setEditingRecommendation(null);
    setShouldRefresh(prev => !prev);
    setEditingRecommendationData(null);
    toast({
      title: '成功',
      description: '推荐内容已保存',
      variant: 'default',
    });
  };

  const handleSiteSelect = async (siteUid: string) => {
    setSelectedSiteUid(siteUid);
    const res = await fetch('/api/manage/recommendations/zones', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'get', siteUid }),
    });
    const data = await res.json();
    if (data.status === 200 && Array.isArray(data.data)) {
      setZones(data.data);
      setSelectedZoneId(data.data[0]?.id || '');
    } else {
      setZones([]);
      setSelectedZoneId('');
    }
  };

  const handleZonesChange = async (newZones: Zone[]) => {
    setZones(newZones);
    if (!newZones.find(z => z.id === selectedZoneId)) {
      setSelectedZoneId(newZones[0]?.id || '');
    }
    if (selectedSiteUid) {
      await fetch('/api/manage/recommendations/zones', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'sort', siteUid: selectedSiteUid, zones: newZones }),
      });
    }
    setShouldRefresh(prev => !prev);
  };

  const renderContent = () => {
    if (isAddingNew) {
      return (
        <RecommendationForm
          siteId={selectedSiteUid}
          zones={zones}
          onCancel={handleCancel}
          onSuccess={handleSaveSuccess}
          locale={locale}
          defaultZoneId={selectedZoneId}
        />
      );
    }

    if (editingRecommendation !== null && isLoadingEditingData) {
      return <p>加载推荐内容详情...</p>;
    }

    if (editingRecommendation !== null && editingRecommendationData) {
      return (
        <RecommendationForm
          siteId={selectedSiteUid}
          recommendationId={editingRecommendation}
          zones={zones}
          initialData={editingRecommendationData}
          onCancel={handleCancel}
          onSuccess={handleSaveSuccess}
          locale={locale}
        />
      );
    }

    return (
      <>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">推荐内容管理</h2>
          <Button onClick={handleAddNew}>添加新推荐</Button>
        </div>
        <RecommendationsList
          siteId={selectedSiteUid}
          onEdit={handleEdit}
          refreshTrigger={shouldRefresh}
          locale={locale}
        />
      </>
    );
  };

  return (
    <div className="space-y-8">
      <Card className="mb-6">
        <CardContent>
          <SiteSelector onSiteSelect={handleSiteSelect} />
        </CardContent>
      </Card>
      {selectedSiteUid && (
        <Card className="mb-6">
          <CardContent>
            <RecommendationZonesComponent
              siteUid={selectedSiteUid}
              zones={zones}
              onZonesChange={handleZonesChange}
              selectedZoneId={selectedZoneId}
              onZoneSelect={setSelectedZoneId}
            />
          </CardContent>
        </Card>
      )}
      <Card>
        <CardContent>
          {selectedSiteUid ? renderContent() : <p>请先选择站点</p>}
        </CardContent>
      </Card>
    </div>
  );
} 