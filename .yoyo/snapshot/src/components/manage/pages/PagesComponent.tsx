'use client'
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import PageListComponent from './PageListComponent';
import HeaderManage from "~/components/common/HeaderManage";
// Comment out the authentication check import
// import { checkAdminUser } from "~/utils/checkWhiteUser";

const PagesComponent = ({ locale }) => {
  const router = useRouter();
  const [authStatus, setAuthStatus] = useState('authorized');
  const [userData, setUserData] = useState(null);

  // Comment out the authentication check
  /*
  useEffect(() => {
    // Check authentication if needed
    const checkAuth = async () => {
      try {
        // In a real implementation, verify user permissions
        const isAdmin = await checkAdminUser();
        if (!isAdmin) {
          setAuthStatus('unauthorized');
        } else {
          setAuthStatus('authorized');
        }
      } catch (error) {
        console.error('Error checking auth:', error);
        setAuthStatus('unauthorized');
      }
    };
    
    checkAuth();
  }, []);
  */

  // Remove conditional rendering for loading and unauthorized
  // Always render the content
  return (
    <>
      <HeaderManage locale={locale} page="manage/pages" />
      <div className="container mx-auto px-4 py-8">
        <PageListComponent locale={locale} />
      </div>
    </>
  );
};

export default PagesComponent; 