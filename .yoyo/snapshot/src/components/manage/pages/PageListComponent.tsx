'use client'

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Box,
  Chip,
  IconButton,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Visibility as ViewIcon,
  Publish as UploadIcon,
  Delete as DeleteIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { checkAdminUser } from '~/utils/checkWhiteUser';
import { useCommonContext } from '~/context/common-context';
import HeaderManage from "~/components/common/HeaderManage";

// Temporarily bypassing auth check for testing
const bypassAuth = true;

// Status options
const statusOptions = [
  { value: 'all', label: 'All Status' },
  { value: 'draft', label: 'Draft' },
  { value: 'published', label: 'Published' },
  { value: 'error', label: 'Error' }
];

// Status color mapping
const statusColors = {
  draft: 'info',
  published: 'success',
  error: 'error'
};

interface PageListComponentProps {
  locale: string;
}

export const PageListComponent = ({ locale }: PageListComponentProps) => {
  const router = useRouter();

  const [pages, setPages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Filters
  const [filters, setFilters] = useState({
    gameUid: '',
    siteUid: '',
    status: '',
    keyword: ''
  });
  
  // Reference data
  const [games, setGames] = useState<any[]>([]);
  const [sites, setSites] = useState<any[]>([]);
  const [gamesLoading, setGamesLoading] = useState(true);
  const [sitesLoading, setSitesLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  
  // Count data
  const [counts, setCounts] = useState({
    total: 0,
    draft: 0,
    published: 0
  });

  const { userData } = useCommonContext();

  // Check authorization
  useEffect(() => {
    async function checkAuth() {
      if (bypassAuth) {
        setIsAuthorized(true);
        return;
      }

      const isAdmin = await checkAdminUser(userData);
      if (!isAdmin) {
        toast.error('Unauthorized: Admin access required');
      }
      setIsAuthorized(isAdmin);
    }

    checkAuth();
  }, []);

  // Load pages
  useEffect(() => {
    const fetchPages = async () => {
      setIsLoading(true);
      
      try {
        // Build filter query
        const queryParams = new URLSearchParams();
        queryParams.append('page', currentPage.toString());
        queryParams.append('pageSize', '10');
        
        if (filters.gameUid) queryParams.append('gameUid', filters.gameUid);
        if (filters.siteUid) queryParams.append('siteUid', filters.siteUid);
        if (filters.status) queryParams.append('status', filters.status);
        if (filters.keyword) queryParams.append('keyword', filters.keyword);
        
        const response = await fetch(`/api/manage/pages?${queryParams.toString()}`);
        
        if (!response.ok) {
          throw new Error(`获取页面列表失败: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === 200) {
          setPages(result.resultList || []);
          setTotalPages(result.totalPage || 1);
          setTotalCount(result.countTotal || 0);
        } else {
          throw new Error(result.message || '获取页面列表失败');
        }
      } catch (err) {
        console.error('获取页面列表出错:', err);
        setError('无法加载页面列表，请重试。');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPages();
  }, [currentPage, filters]);

  // Load reference data: games and sites
  useEffect(() => {
    const fetchReferenceData = async () => {
      try {
        // Fetch games
        setGamesLoading(true);
        const gamesResponse = await fetch('/api/manage/games');
        if (gamesResponse.ok) {
          const gamesResult = await gamesResponse.json();
          if (gamesResult.code === 200 && gamesResult.data) {
            setGames(gamesResult.data.resultList || []);
          }
        }
        setGamesLoading(false);
        // Fetch sites（修正为POST+action:list）
        setSitesLoading(true);
        const sitesResponse = await fetch('/api/manage/sites', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'list',
            data: { page: 1, pageSize: 100 }
          })
        });
        if (sitesResponse.ok) {
          const sitesResult = await sitesResponse.json();
          if (sitesResult.status === 200 && sitesResult.data) {
            setSites(sitesResult.data.sites || []);
          }
        }
        setSitesLoading(false);
      } catch (err) {
        console.error('获取参考数据出错:', err);
      }
    };
    fetchReferenceData();
  }, []);
  
  // Load counts
  useEffect(() => {
    const fetchCounts = async () => {
      try {
        // Build filter query
        const queryParams = new URLSearchParams();
        
        if (filters.gameUid) queryParams.append('gameUid', filters.gameUid);
        if (filters.siteUid) queryParams.append('siteUid', filters.siteUid);
        
        const response = await fetch(`/api/manage/pages/count?${queryParams.toString()}`);
        
        if (response.ok) {
          const result = await response.json();
          
          if (result.status === 200 && result.data) {
            setCounts({
              total: result.data.total || 0,
              draft: result.data.draft || 0,
              published: result.data.published || 0
            });
          }
        }
      } catch (err) {
        console.error('获取页面统计出错:', err);
      }
    };
    
    fetchCounts();
  }, [filters.gameUid, filters.siteUid]);
  
  // Handle filter changes
  const handleFilterChange = (field: string, value: string) => {
    setFilters({
      ...filters,
      [field]: value
    });
    
    // Reset to first page
    setCurrentPage(1);
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Handle view page
  const handleViewPage = (pageUid: string) => {
    router.push(`/${locale}/manage/pages/edit/${pageUid}`);
  };

  // 格式化URL，确保格式一致
  const formatUrl = (url: string) => {
    if (!url) return '';
    url = url.replace(/\/+$/, '');
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      if (url.includes('localhost')) {
        url = 'http://' + url;
      } else {
        url = 'https://' + url;
      }
    }
    return url;
  };

  if (!isAuthorized) {
    return (
      <Card>
        <CardContent>
          <Typography color="error">
            You are not authorized to access this page. Please contact an administrator.
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <meta name="robots" content="noindex" />
      <HeaderManage locale={locale} page="pages" />
      <div className="pt-4 my-auto min-h-[100vh] pb-36 bg-white text-black">
        <div className="mx-auto max-w-[80%]">
          <div className="mb-6 flex justify-between items-center">
            <h1 className="text-2xl font-bold">页面管理</h1>
          </div>
          
          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}
          
          {/* Stats */}
          <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded shadow-sm border">
              <div className="text-xl font-semibold">{counts.total}</div>
              <div className="text-sm text-gray-600">总页面数</div>
            </div>
            <div className="p-4 bg-yellow-50 rounded shadow-sm border">
              <div className="text-xl font-semibold">{counts.draft}</div>
              <div className="text-sm text-gray-600">草稿页面</div>
            </div>
            <div className="p-4 bg-green-50 rounded shadow-sm border">
              <div className="text-xl font-semibold">{counts.published}</div>
              <div className="text-sm text-gray-600">已发布页面</div>
            </div>
          </div>
          
          {/* Filters */}
          <div className="mb-6 p-4 bg-gray-50 rounded shadow-sm border">
            <h2 className="font-semibold mb-3">筛选条件</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block mb-1 text-sm font-medium">游戏</label>
                <select
                  value={filters.gameUid}
                  onChange={(e) => handleFilterChange('gameUid', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  disabled={gamesLoading}
                >
                  <option value="">全部游戏</option>
                  {games.map((game) => (
                    <option key={game.uid} value={game.uid}>
                      {game.keyword}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block mb-1 text-sm font-medium">站点</label>
                <select
                  value={filters.siteUid}
                  onChange={(e) => handleFilterChange('siteUid', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  disabled={sitesLoading}
                >
                  <option value="">全部站点</option>
                  {sites.map((site) => (
                    <option key={site.uid} value={site.uid}>
                      {site.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block mb-1 text-sm font-medium">状态</label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                >
                  <option value="">全部状态</option>
                  <option value="0">草稿</option>
                  <option value="1">已发布</option>
                </select>
              </div>
              
              <div>
                <label className="block mb-1 text-sm font-medium">关键词</label>
                <input
                  type="text"
                  value={filters.keyword}
                  onChange={(e) => handleFilterChange('keyword', e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="搜索标题或slug"
                />
              </div>
            </div>
          </div>
          
          {/* Pages table */}
          <div className="bg-white rounded shadow-sm border overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    标题
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    游戏
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    站点
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {isLoading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center">
                      <div className="flex justify-center items-center">
                        <div className="mr-2 h-5 w-5 border-t-2 border-blue-500 rounded-full animate-spin"></div>
                        <p>加载中...</p>
                      </div>
                    </td>
                  </tr>
                ) : pages.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      没有找到符合条件的页面
                    </td>
                  </tr>
                ) : (
                  pages.map((page) => (
                    <tr key={page.uid} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {page.title}
                          {page.is_homepage && (
                            <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                              首页
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">{page.slug}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{page.game_keyword || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{page.site_name || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                          ${page.status === 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                          {page.status === 0 ? '草稿' : '已发布'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(page.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewPage(page.uid)}
                          className="text-blue-600 hover:text-blue-900 mr-2"
                        >
                          编辑
                        </button>
                        {page.status === 0 ? (
                          <button
                            onClick={() => toast.error('页面上线后才能查看')}
                            className="text-green-600 hover:text-green-900"
                            style={{ marginLeft: 8 }}
                          >
                            查看
                          </button>
                        ) : (
                          <a
                            href={formatUrl(page.public_url)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-green-600 hover:text-green-900"
                            style={{ marginLeft: 8 }}
                          >
                            查看
                          </a>
                        )}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-4 flex justify-center">
              <nav className="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  &laquo; 上一页
                </button>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Calculate which pages to show
                  let pageNum;
                  if (totalPages <= 5) {
                    // Show all pages if 5 or fewer
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    // Show first 5 pages
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    // Show last 5 pages
                    pageNum = totalPages - 4 + i;
                  } else {
                    // Show current page and 2 pages on each side
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      className={`relative inline-flex items-center px-4 py-2 border ${
                        currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      } text-sm font-medium`}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                
                <button
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页 &raquo;
                </button>
              </nav>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageListComponent; 