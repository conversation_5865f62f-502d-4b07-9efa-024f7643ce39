'use client'
import { useState, useCallback } from 'react';
import Pagination from '~/components/common/Pagination';
import { debounce } from 'lodash';
import HeaderManage from "~/components/common/HeaderManage";
import { availableTemplates } from '~/configs/templatesConfig';

interface TemplateListComponentProps {
  locale: string;
}

const TemplateListComponent = ({ locale }: TemplateListComponentProps) => {
  // State for templates
  const [templates] = useState(availableTemplates);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Search state
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Handle search term changes with debounce
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setDebouncedSearchTerm(term);
      setPage(1); // Reset to first page when search changes
    }, 500),
    []
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    debouncedSearch(term);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Filter templates based on search term
  const filteredTemplates = templates.filter(t => t.name.includes(debouncedSearchTerm));

  // Paginate the filtered templates
  const pagedTemplates = filteredTemplates.slice((page - 1) * pageSize, page * pageSize);

  return (
    <>
      <HeaderManage locale={locale} page="manage/templates" />
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">模板管理</h1>
          {/* <Link
            href={`/${locale}/manage/templates/new`}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            创建新模板
          </Link> */}
        </div>

        <div className="flex justify-between items-center mb-6">
          <div className="w-1/2">
            <input
              type="text"
              placeholder="搜索模板名称..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
            <p>{error}</p>
          </div>
        )}

        {pagedTemplates.length === 0 ? (
          <div className="bg-gray-100 p-8 text-center rounded-lg">
            <p className="text-gray-600">
              {debouncedSearchTerm ? '没有找到匹配的模板' : '暂无模板，请创建新模板'}
            </p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="py-3 px-4 border-b text-left font-medium">Key</th>
                    <th className="py-3 px-4 border-b text-left font-medium">名称</th>
                    <th className="py-3 px-4 border-b text-left font-medium">描述</th>
                  </tr>
                </thead>
                <tbody>
                  {pagedTemplates.map((template) => (
                    <tr key={template.key} className="hover:bg-gray-50">
                      <td className="py-3 px-4 border-b">{template.key}</td>
                      <td className="py-3 px-4 border-b font-medium">{template.name}</td>
                      <td className="py-3 px-4 border-b">{template.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6">
              <Pagination
                currentPage={page}
                totalPages={Math.max(1, Math.ceil(filteredTemplates.length / pageSize))}
                onPageChange={handlePageChange}
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default TemplateListComponent; 