import HeadInfo from "~/components/common/HeadInfo";
import GoogleAdsense from "~/components/GoogleAdsense";
import Header from "~/components/common/GameHeader";
import { SiteData } from "~/types/site";
// Import reusable landing components
import Introduction from "~/components/landing/Introduction";
import Feature from "~/components/landing/Feature";
import HowToUse from "~/components/landing/HowToUse";
import Tip from "~/components/landing/Tip";
import Faq from "~/components/landing/Faq";
import Cta from "~/components/landing/Cta";
import GameRecommendations from "~/components/landing/GameRecommendations"; // Reusing Recommendation component
import IframeCard from "~/components/landing/IframeCard"; // Import IframeCard
import Footer from "~/components/common/Footer";

interface DefaultTemplateProps {
  locale: string;
  pageData: SiteData;
}

const DefaultTemplate = ({ locale, pageData }: DefaultTemplateProps) => {
  const pagePath = `game/${pageData.page.slug}`;
  const content = pageData.page.json_content;
  const recommendedGames = pageData.recommendedGames || []; 
  const recommendationZones = pageData.recommendationZones || []; // Get recommendation zones

  // 提取站点名称和导航链接
  const siteName = pageData.site?.name || content.title || 'Game Center';
  const navigationLinks = pageData.site?.config?.navigation_links || [];

  // Group recommended games by zone
  const gamesByZone: { [key: string]: any[] } = {};
  recommendedGames.forEach((game: any) => {
    if (!gamesByZone[game.recommendation_zone]) {
      gamesByZone[game.recommendation_zone] = [];
    }
    gamesByZone[game.recommendation_zone].push(game);
  });

  // Map zone data to GameRecommendations expected props dynamically
  const dynamicRecommendationsInfo: { [zoneName: string]: { title: string; image: string; link: string; }[] } = {};

  recommendationZones.forEach((zone: any) => {
    const gamesInZone = gamesByZone[zone.id] || [];
    if (gamesInZone.length > 0) {
       dynamicRecommendationsInfo[zone.name] = gamesInZone.map((game: any) => ({
        title: game.title || '',
        image: (game.cover_img_url === '' ? null : game.cover_img_url) || null,
        link: game.jump_url || '',
      }));
    }
  });

  // 美化样式
  const outerStyle = {
    background: '#181f2a',
    // backgroundImage: 'url("/images/background.jpg")',
    // backgroundSize: 'cover',
    // backgroundPosition: 'center',
    // backgroundAttachment: 'fixed',
    minHeight: '100vh',
    color: '#fff',
    fontFamily: 'Inter, Roboto, Arial, sans-serif',
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: 'center' as const,
    padding: '0 16px',
    position: 'relative' as const,
    overflow: 'hidden',
  };

  const contentStyle = {
    maxWidth: 800,
    width: '100%',
    margin: '0 auto',
    paddingTop: '40px',
    paddingBottom: '40px',
    paddingLeft: '0',
    paddingRight: '0',
  };
  
  const sectionStyle = {
    // backgroundColor: 'rgba(30, 41, 59, 0.10)',
    borderRadius: '16px',
    // boxShadow: '0 4px 24px 0 rgba(31, 38, 135, 0.10)',
    backdropFilter: 'blur(3px)',
    padding: '24px',
    marginBottom: '32px',
    h1Color:'#FF9500',
    h2Color: 'rgba(255, 149, 0, 0.95)',
    h3Color: '#4A90E2',
    descriptionColor: '#949da1',
    buttonBackgroundColor: '#4A90E2',
    buttonTextColor: '#FFFFFF',
    buttonBorderRadius: '8px',
    buttonHoverBackgroundColor: '#3e7ec9',
    buttonColor: '#4A90E2',
    cardBackgroundColor: 'rgba(17, 24, 39, 0.85)',
    titleColor:'#E5E7EB',
    textColor:'#E5E7EB',
    h1Gradient: 'linear-gradient(90deg, #FF9500 0%, #4A90E2 100%)',
  };

  const cardStyle = {
    backgroundColor: 'rgba(17, 24, 39, 0.85)', // 更深
    borderRadius: '16px',
    boxShadow: '0 4px 24px 0 rgba(31, 38, 135, 0.15)',
    padding: '20px',
    marginBottom: '16px',
  };

  return (
    <div style={outerStyle}>
      {/* <div>Template Loaded</div> Temporary text */}
      <HeadInfo 
        locale={locale} 
        page={pagePath} 
        title={content.title} 
        description={content.description} 
      />
      {/* Render Header component */}
      <Header siteName={siteName} navigationLinks={navigationLinks} />
      <div style={contentStyle}>
        <div className="pt-0 sm:pt-14">
          
          {/* Render the IframeCard component */}
          {pageData.page.iframe_url && (
             <div id="game-iframe">
               <IframeCard 
                 landingInfo={{
                   iframeSrc: pageData.page.iframe_url,
                   title: content.name, // Use game name as iframe title
                   description: content.description, // Use game description
                   h1: content.h1,
                 }}
                 styles={sectionStyle}
               />
             </div>
          )}

          {/* Render Recommended Games section first */}
          {(Object.keys(dynamicRecommendationsInfo).length > 0) &&
            <>
              <GameRecommendations landingInfo={dynamicRecommendationsInfo} styles={sectionStyle} />
            </>
          }

          {/* Render sections based on json_content fields */}
          {Array.isArray(content.components) && content.components.length > 0 ? (
             content.components.map?.((component: any, index: number) => {
               return null;
             })
           ) : (
             // Render based on explicit fields if 'components' array is missing or empty
             <>
               {content.introduction && <Introduction landingInfo={content.introduction} styles={sectionStyle} />}
               {content.features && <Feature landingInfo={content.features} styles={sectionStyle} />}
               {content.how && <HowToUse landingInfo={content.how} styles={sectionStyle} />}
               {content.tips && <Tip landingInfo={content.tips} styles={sectionStyle} />}
               {content.faq && <Faq landingInfo={content.faq} styles={sectionStyle} />}
               {content.cta && <Cta landingInfo={content.cta} styles={sectionStyle} />}
             </>
           )}
        </div>
      </div>
      <GoogleAdsense />
      <Footer locale={locale} page={pagePath} />
    </div>
  );
};

export default DefaultTemplate; 