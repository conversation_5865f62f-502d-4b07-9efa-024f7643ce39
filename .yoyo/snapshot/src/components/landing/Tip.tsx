"use client";
// import { motion } from 'framer-motion'; // Removed framer-motion
// import { Hexagon } from 'lucide-react'; // Removed lucide-react
import Markdown from "react-markdown";
import { useState, useEffect } from "react";
import { useCommonContext } from "~/context/common-context";
import { getGridCol } from "~/utils/strUtil";
import { LandingComponentStyles } from "./types";

interface TipsItem {
  h3?: string;
  content?: string;
  icon?: string;
}

interface TipsProps {
  locale?: string; // Added locale based on other components
  landingInfo: {
    h2?: string;
    content?: TipsItem[];
  };
  styles?: LandingComponentStyles;
  cardStyle?: React.CSSProperties;
}

export default function Tips({ locale, landingInfo, styles, cardStyle }: TipsProps) {
  const { mobile } = useCommonContext();

  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  return (
    <section id="tips" className="py-12 lg:py-16" style={
      styles
    }>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div className="grid grid-cols-1">
          {landingInfo.content.map((item, index) => (
            <div
              key={index}
              className={`flex flex-col items-center text-center bg-transparent${index !== landingInfo.content.length - 1 ? ' mb-8' : ''}`}
              style={cardStyle}
            >
              <div className="w-full border border-[#E5E7EB] rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-semibold mb-3" style={{ color: styles?.h3Color || undefined }}>{item.h3}</h3>
                {item.content && (
                  <div className="text-base mt-2" style={{ color: styles?.descriptionColor || undefined }}>
                    <Markdown>{item.content}</Markdown>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
