'use client';

import Markdown from 'react-markdown';
import { LandingComponentStyles } from "./types";

interface HowToUseItem {
  h3?: string;
  content?: string;
}

interface HowToUseProps {
  landingInfo: {
    h2?: string;
    content?: HowToUseItem[];
  };
  styles?: LandingComponentStyles;
}

export default function HowToUse({ landingInfo, styles }: HowToUseProps) {
  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  return (
    <section id="how-to-use" className="py-12 lg:py-16" style={styles}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div className="grid grid-cols-1 gap-10">
          {landingInfo.content.map((item, index) => (
            <div
              key={index}
              className="bg-[rgba(17,24,39,0.92)] rounded-xl shadow-lg p-8 mb-4 transition-transform duration-200 hover:scale-105 hover:shadow-2xl flex gap-4"
              style={{ boxShadow: '0 2px 12px 0 rgba(0,0,0,0.10)', ...styles }}
            >
              {/* 序号圆圈 */}
              <div
                className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full text-base font-bold shadow-md"
                style={{ background: styles?.buttonBackgroundColor || '#FF9500', color: styles?.buttonTextColor || '#fff', border: '2px solid #181f2a' }}
              >
                {index + 1}
              </div>
              
              {/* 内容区 */}
              <div className="flex-1">
                {item.h3 && (
                  <h3 className="text-2xl font-semibold mb-3" style={{ color: styles?.h3Color || '#4A90E2' }}>
                    {item.h3}
                  </h3>
                )}
                {item.content && (
                  <div className="text-base" style={{ color: styles?.descriptionColor || '#bfc9d1' }}>
                    <Markdown>{item.content}</Markdown>
                  </div>
                )}
              </div>
            </div>

          ))}
        </div>
      </div>
    </section>
  );
}
