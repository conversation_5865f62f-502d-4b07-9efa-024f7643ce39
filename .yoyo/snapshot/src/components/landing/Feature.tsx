"use client";
// import { motion } from 'framer-motion'; // Removed framer-motion
// import { Hexagon } from 'lucide-react'; // Removed lucide-react
import Markdown from "react-markdown";
import { useState, useEffect } from "react";
import { useCommonContext } from "~/context/common-context";
import { getGridCol } from "~/utils/strUtil";
import { LandingComponentStyles } from "./types";

interface FeatureItem {
  h3?: string;
  content?: string;
  icon?: string;
}

interface featureProps {
  landingInfo: {
    h2?: string;
    content?: FeatureItem[];
  };
  styles?: LandingComponentStyles;
  cardStyle?: React.CSSProperties;
}

// Removed itemVariants
// const itemVariants = {
//   hidden: { y: 20, opacity: 0 },
//   visible: { y: 0, opacity: 1 }
// };

export default function Feature({ landingInfo, styles, cardStyle }: featureProps) {
  const { mobile } = useCommonContext();

  if (!landingInfo || !landingInfo.content || !Array.isArray(landingInfo.content)) {
    return null;
  }

  return (
    <section id="features" className="py-12 lg:py-16" style={styles}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-12" style={{ color: styles?.h2Color || undefined }}>{landingInfo.h2}</h2>
        <div // Changed motion.div back to div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8"
          // Removed variants, initial, animate
          // variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
          // initial="hidden"
          // animate="visible"
        >
          {landingInfo.content.map((item, index) => (
            <div // Changed motion.div back to div
              key={index}
              className="flex flex-col items-center text-center"
              style={cardStyle}
              // Removed variants
              // variants={itemVariants}
            >
              {/* Icon rendering logic can be added here if needed */}
              <h3 className="text-2xl font-semibold mb-3" style={{ color: styles?.h3Color || '#4A90E2' }}>{item.h3}</h3>
              <div className="w-full h-px bg-[#bfc9d1] mb-4" />
              <p className="text-base" style={{ color: styles?.descriptionColor || '#bfc9d1' }}>{item.content}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
