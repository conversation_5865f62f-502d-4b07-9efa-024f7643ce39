"use client";

import { useState, useEffect } from "react";
import { whiteLoadingSvg } from "../svg";
import Particles from "~/components/ui/Particles";
import { toggleFullscreen } from "~/utils/full-screen";
import Link from "next/link";
import {
  FacebookShareButton, FacebookIcon,
  TwitterShareButton, TwitterIcon,
  RedditShareButton, RedditIcon,
  WhatsappShareButton, WhatsappIcon,
  TelegramShareButton, TelegramIcon,
  LineShareButton, LineIcon
} from 'react-share';
import { LandingComponentStyles } from "./types";
// import { ShareSocial } from 'react-share-social';

interface IframeProps {
  landingInfo: {
    title?: string;
    description?: string;
    h1?: string;
    src?: string;
    iframeSrc?: string;
    width?: string | number;
    height?: string | number;
    allowFullScreen?: boolean;
    aspectRatio?: "16:9" | "4:3" | "1:1" | "3:4";
    buttonText?: string;
    buttonLink?: string;
  };
  styles?: LandingComponentStyles;
}

export default function IframeCard({ landingInfo, styles }: IframeProps) {
  const [loading, setLoading] = useState(true);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const { 
    src, 
    iframeSrc = "",
    title = "", 
    description = "", 
    width = "100%", 
    height = "600px", 
    allowFullScreen = true,
    buttonText = "Play Now",
    buttonLink = "#"
  } = landingInfo;

  // 使用src或iframeSrc作为iframe源
  const iframeSource = src || iframeSrc;

  useEffect(() => {
    const handleFullscreenChange = () => {
      const isFullscreenNow = !!(document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).mozFullScreenElement || (document as any).msFullscreenElement);
      setIsFullScreen(isFullscreenNow);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
      document.removeEventListener("webkitfullscreenchange", handleFullscreenChange);
      document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
      document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFullScreen = async () => {
    const iframeElement = document.querySelector("iframe");
    if (!iframeElement) return;

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    toggleFullscreen({
      element: iframeElement,
      isFullScreen: false,
      isMobile: isMobile,
      onEnter: () => {
        console.log("进入全屏");
      },
      onExit: () => {
        console.log("退出全屏");
      },
    });
  };

  const getAspectRatioClass = () => {
    switch (landingInfo?.aspectRatio) {
      case "4:3":
        return "md:pb-[75%] pb-[100%]"; // PC端 4:3，移动端 1:1
      case "1:1":
        return "pb-[100%]";
      case "3:4":
        return "md:pb-[75%] pb-[133.33%]"; // PC端 4:3，移动端 3:4
      case "16:9":
      default:
        return "md:pb-[56.25%] pb-[75%]"; // PC端 16:9，移动端 4:3
    }
  };

  return (
    <div
      id="hero"
      className="py-4 md:py-24 lg:py-20 my-auto relative"
      style={styles}
    >
      {/* Particles animation for background effect */}
      <Particles className="absolute inset-0 z-10" quantity={50} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-20">
        {/* 文本内容区域 - 标题和描述 */}
        <div className="text-center mb-12">
          <h1 
            className="text-3xl md:text-5xl lg:text-5xl font-bold mb-6 leading-tight bg-clip-text"
            style={{
              background: styles?.h1Gradient || styles?.h1Color || undefined,
              WebkitBackgroundClip: styles?.h1Gradient ? 'text' : undefined,
              WebkitTextFillColor: styles?.h1Gradient ? 'transparent' : undefined,
              backgroundClip: styles?.h1Gradient ? 'text' : undefined,
              color: styles?.h1Gradient ? 'transparent' : (styles?.h1Color || undefined),
            }}
          >
            {landingInfo.h1}
          </h1>
          
          <p
            className="hidden md:block text-lg md:text-xl mb-8 max-w-2xl mx-auto"
            style={{ color: styles?.descriptionColor || undefined }}
          >
            {description}
          </p>

        </div>
        
        {/* 游戏区域 - iframe居中展示 */}
        <div className="mx-auto max-w-5xl flex justify-center items-center" id="gameFrame" style={{ minHeight: '600px' }}>
          {iframeSource ? (
            <div className="relative flex justify-center items-center w-full" style={{ width: '100%', maxWidth: '900px', height: '800px' }}>
              <iframe
                src={iframeSource}
                title={title}
                className="w-full h-full border-0 rounded-lg shadow-[0_8px_30px_rgba(0,0,0,0.4)] hover:shadow-[0_8px_30px_rgba(0,0,0,0.5)] transition-shadow duration-300"
                style={{ minWidth: '320px', minHeight: '800px', maxWidth: '500px', maxHeight: '800px', background: '#222' }}
                allowFullScreen={allowFullScreen}
                onLoad={() => setLoading(false)}
              />
            </div>
          ) : (
            // Fallback game image if no iframe source
            <div className="relative rounded-lg overflow-hidden shadow-[0_8px_30px_rgba(0,0,0,0.4)] hover:shadow-[0_8px_30px_rgba(0,0,0,0.5)] transition-all transform hover:scale-[1.02]">
              <img 
                src="/icon/game-screenshot.jpg" 
                alt={title || "Game screenshot"} 
                className="w-full h-auto rounded-lg"
                onError={(e) => {
                  e.currentTarget.src = "/icon/favicon.ico";
                  e.currentTarget.className = "w-full h-auto rounded-lg object-contain bg-gray-800 p-4";
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end justify-center p-6">
                <button 
                  className="px-6 py-3 rounded-lg font-medium transition-all transform hover:scale-105"
                  style={{ 
                    backgroundColor: styles?.buttonBackgroundColor || "#4f46e5",
                    color: styles?.buttonTextColor || "#ffffff"
                  }}
                >
                  {buttonText}
                </button>
              </div>
            </div>
          )}
        </div>
        {/* 分享功能区域 */}
        <div className="flex items-center justify-center gap-4 mt-6">
          <FacebookShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <FacebookIcon size={36} round />
          </FacebookShareButton>
          <TwitterShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <TwitterIcon size={36} round />
          </TwitterShareButton>
          <RedditShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <RedditIcon size={36} round />
          </RedditShareButton>
          <WhatsappShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <WhatsappIcon size={36} round />
          </WhatsappShareButton>
          <TelegramShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <TelegramIcon size={36} round />
          </TelegramShareButton>
          <LineShareButton url={typeof window !== 'undefined' ? window.location.href : ''}>
            <LineIcon size={36} round />
          </LineShareButton>
        </div>

      </div>
    </div>
  );
}
