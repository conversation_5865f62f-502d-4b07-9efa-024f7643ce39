const CreditShow = ({
  textStr,
  replaceList = []
}) => {
  let resultElements = [textStr];

  replaceList.forEach(item => {
    const newElements = [];
    resultElements.forEach(element => {
      if (typeof element === 'string') {
        const parts = element.split(item.replaceStr);
        for (let i = 0; i < parts.length; i++) {
          if (i > 0) {
            newElements.push(
              <span key={`credit-${item.replaceStr}-${i}`} className="text-red-400">
                {item.creditCount}
              </span>
            );
          }
          if (parts[i]) {
            newElements.push(parts[i]);
          }
        }
      } else {
        newElements.push(element);
      }
    });
    resultElements = newElements;
  });
  return <span>{resultElements}</span>;
};

export default CreditShow;
