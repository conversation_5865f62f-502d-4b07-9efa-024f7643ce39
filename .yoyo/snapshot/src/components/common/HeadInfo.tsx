import { Fragment } from "react";
import { websiteUrl, domainNameLowercase } from "~/configs/globalConfig";
import { languages } from "~/i18n/config";
import { getLinkHref } from "~/utils/buildLink";

const HeadInfo = ({
  locale,
  page,
  title,
  description,
  languageList = languages,
  openGraph = {
    image: "",
  },
  socialUrl = "",
  twitter = {
    image: "",
  },
}) => {
  const url = websiteUrl + getLinkHref(locale, socialUrl !== "" ? socialUrl : page);

  // console.log('[HeadInfo] page:', page);

  return (
    <>
      <title>
        {page === "game/" 
          ? title 
          : `${title} | ${domainNameLowercase}`}
      </title>
      <meta name="description" content={description} />
      <meta property="og:description" content={`${description}`} />
      <meta property="og:title" content={`${title}`} />
      <meta property="og:type" content="website" />
      {openGraph.image !== "" ? (
        <>
          <meta property="og:image" content={`${openGraph.image}`} />
          <meta property="og:image:alt" content={`${title}`} />
          <meta property="og:image:width" content="800" />
          <meta property="og:image:height" content="600" />
        </>
      ) : null}
      {url !== "" ? <meta property="og:url" content={`${url}`} /> : null}

      <meta property="twitter:card" content="summary" />
      {twitter.image !== "" ? <meta property="twitter:image" content={`${twitter.image}`} /> : null}
      {url ? <meta property="twitter:url" content={`${url}`} /> : null}
      <meta property="twitter:title" content={`${title}`} />
      <meta property="twitter:description" content={`${description}`} />

      {languageList.map((item) => {
        const currentPage = page;
        let hrefLang = item.code;
        let href: string;
        if (currentPage) {
          href = `${websiteUrl}/${item.lang}/${currentPage}`;
          if (item.lang == "en") {
            href = `${websiteUrl}/${currentPage}`;
          }
        } else {
          href = `${websiteUrl}/${item.lang}`;
          if (item.lang == "en") {
            href = `${websiteUrl}/`;
          }
        }
        if (item.lang == "en") {
          return (
            <Fragment key={href + "x-default"}>
              <link rel="alternate" hrefLang={"x-default"} href={href} />
              <link rel="alternate" hrefLang={hrefLang} href={href} />
            </Fragment>
          );
        } else {
          return <link key={href} rel="alternate" hrefLang={hrefLang} href={href} />;
        }
      })}
      {languageList.map((item) => {
        const currentPage = page;

        if (locale == item.lang) {
          let canonicalHref: string;

          const normalizedPageForCanonical = (typeof currentPage === 'string' ? currentPage : '').toLowerCase().trim().replace(/^\/|\/$/g, '');

          const isRootPageForCanonical = normalizedPageForCanonical === "" || normalizedPageForCanonical === "index" || normalizedPageForCanonical === "home" || normalizedPageForCanonical === "game";

          if (isRootPageForCanonical) {
            canonicalHref = `${websiteUrl}/`;
          } else {
            canonicalHref = `${websiteUrl}/${item.lang}/${currentPage}`;
            if (item.lang == "en") {
              canonicalHref = `${websiteUrl}/${currentPage}`;
            }
          }
          return <link key={canonicalHref + "canonical"} rel="canonical" href={canonicalHref} />;
        }
      })}
      <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
      <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
      <link rel="shortcut icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <meta name="apple-mobile-web-app-title" content="TinyFun" />
      <link rel="manifest" href="/site.webmanifest" />
    </>
  );
};

export default HeadInfo
