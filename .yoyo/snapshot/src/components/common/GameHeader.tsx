'use client'
import Link from "next/link";
import React, { useState } from "react";
// import { getLinkHref } from "~/utils/buildLink"; // 不再需要这个导入

interface GameHeaderProps {
  locale?: string;
  page?: string;
  siteName: string;
  navigationLinks: Array<{ label: string; url: string; target?: string; }>;
}

const GameHeader: React.FC<GameHeaderProps> = ({ 
  locale = 'default', 
  page = '', 
  siteName, 
  navigationLinks 
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // 处理页面跳转 (移除 handlePageChange 函数)
  // const handlePageChange = (url: string) => {
  //   if (page !== url) {
  //     window.location.href = getLinkHref(locale, url);
  //   }
  // };

  return (
    <header className="top-0 left-0 w-full z-50 bg-opacity-90 px-8 py-6 flex items-center justify-between shadow-lg">
      {/* Left section: Icon and Site Name */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 flex items-center justify-center rounded-full bg-white overflow-hidden">
          <img src="/favicon.ico" alt="Site Icon" className="w-full h-full object-contain" />
        </div>
        <Link 
          href="/"
          className="flex items-center"
        >
          <img src="/logo.svg" alt="Site Logo" className="h-8 object-contain" />
        </Link>
      </div>

      {/* Right section: Navigation Links (Desktop) */}
      <nav className="hidden sm:flex flex-1 justify-end items-center space-x-8">
        {navigationLinks.map((link, idx) => (
          <Link
            key={idx}
            href={link.url} // 直接使用 link.url
            target={link.target || "_self"}
            // onClick={() => handlePageChange(link.url)} // 移除点击事件
            className={`
              text-gray-200 font-medium
              px-4 py-2
              rounded-md
              hover:bg-gray-700 hover:text-white
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
              transition
            `}
          >
            {link.label}
          </Link>
        ))}
      </nav>

      {/* Mobile Menu Button (Hamburger icon) */}
      <div className="md:hidden lg:hidden xl:hidden ml-auto">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="text-white focus:outline-none"
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-40 flex flex-col items-center justify-center space-y-6 md:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="absolute top-4 right-4 text-white focus:outline-none"
          >
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
          {navigationLinks.map((link, index) => (
            <Link
              key={index}
              href={link.url} // 直接使用 link.url
              target={link.target || "_self"}
              onClick={() => {
                setIsMobileMenuOpen(false);
                // handlePageChange(link.url); // 移除点击事件
              }}
              className="text-white text-3xl font-bold hover:text-gray-300 transition-colors duration-200"
            >
              {link.label}
            </Link>
          ))}
        </div>
      )}
    </header>
  );
};

export default GameHeader; 