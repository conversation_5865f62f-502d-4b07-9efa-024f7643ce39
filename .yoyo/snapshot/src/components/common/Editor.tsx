"use client";
import { useCommonContext } from "~/context/common-context";
import React, { useState, useRef, useEffect } from "react";
import "react-markdown-editor-lite/lib/index.css";
import ReactMarkdown from "react-markdown";
import MdEditor from "react-markdown-editor-lite";
import remarkGfm from "remark-gfm";
import LoadingModal from "./LoadingModal";
import { useRouter } from "next/navigation";
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/20/solid";
// import { styleData } from "~/configs/generator";
import { getFileExtensionByFileType } from "~/utils/fileType";
import { toast } from "react-toastify";

const VideoComponent = ({ src, type }) => {
  return (
    <video controls width="100%" className="my-4">
      <source src={src} type={type} />
      Your browser does not support the video tag.
    </video>
  );
};

const MarkdownEditor = ({ locale }) => {
  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    setShowLoadingModal(false);
    getDetailByUid();
    return () => { };
  }, []);

  const router = useRouter();

  const [markdown, setMarkdown] = useState("");
  const [html, setHtml] = useState("");
  const [title, setTitle] = useState("");
  const [uid, setUid] = useState("");
  const [pageTitle, setPageTitle] = useState("");
  const [pageDescription, setPageDescription] = useState("");
  const [pageUrl, setPageUrl] = useState("");
  const [showAdditionalFields, setShowAdditionalFields] = useState(false);
  const [contentDescription, setContentDescription] = useState("");
  const [contentBanner, setContentBanner] = useState("");
  const [formTitle, setFormTitle] = useState("");
  const [formDescription, setFormDescription] = useState("");

  const { setShowLoadingModal, userData, setShowLoginModal, setToastText, setShowToastModal } = useCommonContext();

  const handleEditorChange = ({ text, html }) => {
    setMarkdown(text);
    setHtml(html);
  };
  // 图片上传
  async function onImageUpload(file) {
    // 扩展允许的文件类型
    const allowedTypes = {
      image: ["image/png", "image/jpeg", "image/gif", "image/webp"],
      video: ["video/mp4", "video/webm", "video/quicktime"],
    };

    const isImage = allowedTypes.image.includes(file.type);
    const isVideo = allowedTypes.video.includes(file.type);

    if (!isImage && !isVideo) {
      toast("只支持图片(PNG/JPEG/GIF/WEBP)和视频(MP4/WEBM/MOV)格式", {
        type: "error",
      });
      return "";
    }

    setLoadingText(isImage ? "Uploading image..." : "Uploading video...");
    setShowLoadingModal(true);

    // 获取文件扩展名
    const fileExtension = getFileExtensionByFileType(file.type, isImage ? "jpg" : "mp4");
    const presignedUrlResult = await getPresignedUrl(fileExtension);

    const presignedUrl = presignedUrlResult.presignedUrl;
    const fileUrl = presignedUrlResult.imageUrl;

    const uploadResult = await uploadImageToR2(presignedUrl, file, file.type);
    setShowLoadingModal(false);

    if (uploadResult) {
      // 直接返回文件 URL，不需要包装成 video 标签
      return fileUrl;
    } else {
      return "";
    }
  }

  // 获取预签名 url
  const getPresignedUrl = async (fileExtension) => {
    console.log("getPresignedUrl", fileExtension);
    const responseData = await fetch(`/api/upload/getBlogPresignedUrl?fileExtension=${fileExtension}`);
    console.log("responseData", responseData);
    const result = await responseData.json();
    return result;
  };

  // 前端上传图片到 R2
  const uploadImageToR2 = async (presignedUrl, file, fileType) => {
    const response = await fetch(presignedUrl, {
      method: "PUT",
      body: file,
      headers: {
        "Content-Type": fileType,
      },
    });
    const status = await response.status;
    if (status !== 200) {
      return false;
    } else {
      return true;
    }
  };
  // Get generator list
  const options = [
    { name: "首页", value: "blog" },

    // ...Object.keys(styleData).map((key) => ({
    //   name: styleData[key].name,
    //   value: styleData[key].value,
    // })),
  ];

  const generateSmartUrl = () => {
    if (!title) {
      toast("请先输入H1文章标题", { type: "error" });
      return;
    }
    const smartUrl = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-");
    setPageUrl(smartUrl);
  };

  const [selectedGenerator, setSelectedGenerator] = useState(options[0]);

  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  const handlePageUrlChange = (e) => {
    const urlPattern = /^[a-z0-9-]*$/;

    if (urlPattern.test(e.target.value)) {
      setPageUrl(e.target.value);
    } else {
      toast("Invalid URL format, only lowercase letters, numbers, and hyphens are allowed", {
        type: "error",
      });
    }
  };

  const handleSubmit = async (status) => {
    if (!userData?.email) {
      setShowLoginModal(true);
      return;
    }
    if (!title || !pageTitle || !pageDescription || !pageUrl || !contentDescription) {
      setToastText("所有字段都是必填的");
      setShowToastModal(true);
      return;
    }
    setLoadingText("Publishing topic...");
    setShowLoadingModal(true);

    const body = {
      uid: uid,
      content_title: title,
      content_markdown: markdown,
      generator: selectedGenerator.value,
      title: pageTitle,
      description: pageDescription,
      blog_url: pageUrl,
      user_id: userData.user_id,
      language: locale,
      content_description: contentDescription,
      content_banner: contentBanner,
      form_title: formTitle,
      form_description: formDescription,
    };
    let responseData;
    if (status == "publish") {
      responseData = await fetch(`/api/blog/save`, {
        method: "POST",
        body: JSON.stringify(body),
      });
    } else {
      responseData = await fetch(`/api/blog/saveDraft`, {
        method: "POST",
        body: JSON.stringify(body),
      });
    }
    const result = await responseData.json();
    if (result.code == 601) {
      setShowLoginModal(true);
    }
    if (result.code == 400) {
      setShowLoadingModal(false);
      toast(result.message, {
        type: "error",
      });
      return;
    }
    if (result.code == 200) {
      setShowLoadingModal(false);
      resettled();
      router.push(`/manage/blog/list`);
    }
  };
  const resettled = () => {
    setUid("");
    setTitle("");
    setMarkdown("");
    setHtml("");
    setPageTitle("");
    setPageDescription("");
    setPageUrl("");
    setContentDescription("");
    setContentBanner("");
    setFormTitle("");
    setFormDescription("");
  };

  // Get post details
  const getDetailByUid = async () => {
    // Get uid from URL
    const url = window.location.href;
    const uidCurrent = url.split("?uid=")[1];
    if (!uidCurrent) {
      return;
    }
    const response = await fetch(`/api/blog/getBlogByUid`, {
      method: "POST",
      body: JSON.stringify({ uid: uidCurrent }),
    });
    const result = await response.json();
    const data = result.data;
    setUid(uidCurrent);
    setTitle(data.content_title);
    setMarkdown(data.content_markdown);
    setPageTitle(data.title);
    setPageDescription(data.description);
    setPageUrl(data.blog_url);
    setSelectedGenerator(options.find((option) => option.value === data.generator));
    setContentDescription(data.content_description || "");
    setContentBanner(data.content_banner || "");
    setFormTitle(data.form_title || "");
    setFormDescription(data.form_description || "");
  };

  const [loadingText, setLoadingText] = useState("Loading...");

  const handleContentBannerUpload = async (event) => {
    const file = event.target.files[0];
    if (file) {
      const imageUrl = await onImageUpload(file);
      if (imageUrl) {
        setContentBanner(imageUrl);
      }
    }
  };

  return (
    <div className="min-h-screen  p-4">
      <LoadingModal loadingText={loadingText} />
      <div className="max-w-6xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 text-gray-900">
          <div className="flex items-center space-x-2 mb-4">
            <div className="flex-grow">
              <label htmlFor="pageUrl" className="block text-sm font-medium text-gray-700">
                URL
              </label>
              <div className="flex items-center space-x-2">
                <Listbox value={selectedGenerator} onChange={setSelectedGenerator}>
                  <ListboxButton
                    className={classNames("relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 sm:px-3")}
                  >
                    <span className="text-gray-900 hidden truncate sm:ml-2 sm:block">{selectedGenerator.name}</span>
                  </ListboxButton>
                  <ListboxOptions
                    anchor="bottom"
                    className={classNames("mt-1 max-h-56  text-gray-900 w-overflow-auto rounded-lg bg-white py-3 text-base shadow ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm")}
                  >
                    {options.map((person) => (
                      <ListboxOption
                        key={person.name}
                        value={person}
                        className={({ active }) => classNames(active ? "bg-gray-100" : "bg-white", "relative cursor-pointer hover:text-blue-500 select-none px-3 py-2")}
                      >
                        <CheckIcon className="invisible size-4 fill-white group-data-[selected]:visible" />
                        <div className="ml-3 block truncate font-medium">{person.name}</div>
                      </ListboxOption>
                    ))}
                  </ListboxOptions>
                </Listbox>
                <input
                  type="text"
                  name="pageUrl"
                  id="pageUrl"
                  className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none"
                  placeholder="页面 URL (格式必须为英文小写字母，多个单词使用 - 连接)"
                  value={pageUrl}
                  onChange={handlePageUrlChange}
                  required
                />
                <button onClick={generateSmartUrl} className="bg-blue-500 hover:bg-blue-600 text-white font-bold p-2 flex rounded whitespace-nowrap">
                  生成url
                </button>
              </div>
            </div>
          </div>

          <div className="space-y-4 mb-4 transition-all duration-300 ease-in-out">
            <div>
              <label htmlFor="pageTitle" className="block text-sm font-medium text-gray-700">
                Title
              </label>
              <input
                type="text"
                name="pageTitle"
                id="pageTitle"
                className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none"
                placeholder="Title"
                value={pageTitle}
                onChange={(e) => setPageTitle(e.target.value)}
                required
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                name="description"
                id="description"
                className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none resize-y"
                placeholder="Description"
                value={pageDescription}
                onChange={(e) => setPageDescription(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              H1
            </label>
            <input
              type="text"
              name="title"
              id="title"
              className="w-full  mt-1 p-2 border-b-2 border-gray-200 focus:border-indigo-500 outline-none"
              placeholder="标题（此为 文章标题 ）"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="contentDescription" className="block text-sm font-medium text-gray-700">
              H2
            </label>
            <textarea
              name="contentDescription"
              id="contentDescription"
              className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none resize-y"
              placeholder="内容描述 (Content Description)"
              value={contentDescription}
              onChange={(e) => setContentDescription(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="formTitle" className="block text-sm font-medium text-gray-700">
              Form H2
            </label>
            <input
              type="text"
              name="formTitle"
              id="formTitle"
              className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none"
              placeholder="表单标题"
              value={formTitle}
              onChange={(e) => setFormTitle(e.target.value)}
              required
            />
          </div>
          <div className="mb-4">
            <label htmlFor="formDescription" className="block text-sm font-medium text-gray-700">
              Form H3
            </label>
            <textarea
              name="formDescription"
              id="formDescription"
              className="w-full mt-1 p-2 border-b border-gray-200 focus:border-indigo-500 outline-none resize-y"
              placeholder="表单描述"
              value={formDescription}
              onChange={(e) => setFormDescription(e.target.value)}
              required
            />
          </div>
          <div className="flex items-center space-x-2 mb-4">
            <input type="file" accept="image/*" onChange={handleContentBannerUpload} className="hidden" id="contentBannerUpload" required />
            <label htmlFor="contentBannerUpload" className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
              上传 banner
            </label>
            {contentBanner && <img src={contentBanner} alt="Content Banner" className="h-10 w-auto" />}
          </div>

          <div className="h-[calc(100vh-300px)] min-h-[360px]">
            <MdEditor
              plugins={[
                "header",
                "font-bold",
                "font-italic",
                "font-strikethrough",
                "list-unordered",
                "list-ordered",
                "block-quote",
                "block-code-inline",
                "block-code-block",
                "table",
                "image",
                "link",
                "clear",
                "mode-toggle",
                "full-screen",
              ]}
              value={markdown}
              style={{
                height: "100%",
                border: "0",
              }}
              renderHTML={(text) => {
                return (
                  <ReactMarkdown
                    className="prose max-w-none"
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // 添加对图片标签的处理，检查是否包含视频链接
                      img: ({ src, alt }) => {
                        // 如果 src 是视频链接
                        if (src && src.match(/\.(mp4|webm|mov)$/i)) {
                          return <VideoComponent src={src} type={`video/${src.split(".").pop()}`} />;
                        }
                        // 如果 src 为空或未定义，返回 null 或占位符图像
                        if (!src) {
                          return null; // 或者使用占位符: <div className="placeholder-image">No image</div>
                        }
                        // 正常返回图片元素
                        return <img src={src} alt={alt || ""} />;
                      },
                    }}
                  >
                    {text}
                  </ReactMarkdown>
                );
              }}
              onChange={handleEditorChange}
              onImageUpload={onImageUpload}
            />
          </div>
        </div>

        <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            onClick={() => handleSubmit("publish")}
            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            发布
          </button>
          <button
            onClick={() => handleSubmit("draft")}
            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-500 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

export default MarkdownEditor;
