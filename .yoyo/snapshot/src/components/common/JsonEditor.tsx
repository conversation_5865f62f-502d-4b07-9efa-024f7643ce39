import { Editor } from "@monaco-editor/react";
import { FC } from "react";

interface JsonEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
}

const JsonEditor = ({ value, onChange }) => {
  const editorOptions = {
    minimap: { enabled: false },
    fontSize: 14,
    formatOnPaste: true,
    formatOnType: false,
    roundedSelection: false,
    scrollBeyondLastLine: false,
    readOnly: false,
    theme: "vs",
    wordWrap: "on" as const,
    automaticLayout: true,
    tabSize: 2,
  };

  return (
    <div className="h-[calc(100vh-180px)]">
      <Editor
        height="100%"
        defaultLanguage="json"
        value={value}
        onChange={onChange}
        options={editorOptions}
        beforeMount={(monaco) => {
          monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
            validate: true,
            schemas: [
              {
                uri: "http://myserver/schema.json",
                // schema 应用到哪些文件
                fileMatch: ["*"],
                schema: {
                  type: "array",
                  items: {
                    type: "object",
                    required: ["type", "props"],
                    properties: {
                      type: {
                        type: "string",
                        enum: ["IframeCard", "Cta", "Faq", "Feature", "Introduction", "List", "HowToUse", "ListSlider"],
                      },
                      props: {
                        type: "object",
                      },
                    },
                  },
                },
              },
            ],
          });
        }}
      />
    </div>
  );
};

export default JsonEditor;
