"use client";
import { getLinkHref } from "~/utils/buildLink";
import { ChevronRightIcon, HomeIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { useCommonContext } from "~/context/common-context";
import { Home } from "lucide-react";

const BreadcrumbPage = ({ locale = "en", menuList = [] }) => {
  const { setShowLoadingModal, menuText } = useCommonContext();

  return (
    <nav className="px-1 md:px-2 max-w-full overflow-hidden" aria-label="Breadcrumb">
      <ol role="list" className="flex items-center space-x-0.5 md:space-x-2 w-full text-xs md:text-sm lg:text-base overflow-x-auto whitespace-nowrap scrollbar-hide">
        <li className="flex-shrink-0">
          <div>
            <a
              href={getLinkHref(locale)}
              className="text-gray-600 font-semibold hover:text-[#F94F4F] transition-colors duration-200"
              onClick={() => setShowLoadingModal(true)}
              title={process.env.NEXT_PUBLIC_SITE_NAME}
            >
              <span className="truncate max-w-[60px] sm:max-w-[100px] md:max-w-[120px]">{process.env.NEXT_PUBLIC_SITE_NAME}</span>
            </a>
          </div>
        </li>
        {menuList.map((item, index) => {
          if (index === menuList.length - 1) {
            return (
              <li key={item.pagePath + index} className="flex-shrink-0">
                <div className="flex items-center">
                  <ChevronRightIcon className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0 text-gray-400" aria-hidden="true" />
                  {item.pagePath ? (
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, item.pagePath)}
                      className="ml-0.5 md:ml-1 font-medium text-gray-600 hover:text-[#F94F4F] transition-colors duration-200 truncate max-w-[80px] sm:max-w-[120px] md:max-w-[350px] lg:max-w-[450px]"
                      title={item.pageName}
                    >
                      {item.pageName}
                    </Link>
                  ) : (
                    <div className="ml-0.5 md:ml-1 font-medium text-gray-500 truncate max-w-[80px] sm:max-w-[120px] md:max-w-[150px] lg:max-w-[200px]" title={item.pageName}>
                      {item.pageName}
                    </div>
                  )}
                </div>
              </li>
            );
          }

          return (
            <li key={item.pagePath + index} className="flex-shrink-0">
              <div className="flex items-center">
                <ChevronRightIcon className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0 text-gray-400" aria-hidden="true" />
                {item.pagePath ? (
                  <Link
                    prefetch={false}
                    href={getLinkHref(locale, item.pagePath)}
                    className="ml-0.5 md:ml-1 font-medium text-gray-600 hover:text-[#F94F4F] transition-colors duration-200 truncate max-w-[60px] sm:max-w-[100px] md:max-w-[150px] lg:max-w-[180px]"
                    onClick={() => setShowLoadingModal(true)}
                    title={item.pageName}
                  >
                    {item.pageName}
                  </Link>
                ) : (
                  <div className="ml-0.5 md:ml-1 font-medium text-gray-500 truncate max-w-[60px] sm:max-w-[100px] md:max-w-[120px] lg:max-w-[150px]" title={item.pageName}>
                    {item.pageName}
                  </div>
                )}
              </div>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default BreadcrumbPage;
