'use client'
import { useState } from 'react'
import { Dialog } from '@headlessui/react'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { GlobeAltIcon } from '@heroicons/react/24/outline'
import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import Link from "next/link";
import { languages } from "~/i18n/config";
import { useCommonContext } from '~/context/common-context'
import LoadingModal from "./LoadingModal";
import GeneratingModal from "~/components/common/GeneratingModal";
import LoginButton from '../auth/LoginButton';
import LoginModal from '../auth/LoginModal';
import LogoutModal from "../auth/LogoutModal";
import { getLinkHref } from "~/utils/buildLink";
import OneTapComponent from "~/components/auth/OneTapComponent";

import {
  isMultiLanguage,
  domainNameLowercase,
} from "~/configs/globalConfig";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { ToastContainer } from "react-toastify";

// 导航菜单常量
const manageGames = "manage/games";           // 游戏管理
const managePages = "manage/pages";           // 页面管理
const manageRecommendations = "manage/recommendations"; // 推荐管理
const manageTemplates = "manage/templates";   // 模板管理
const manageSites = "manage/sites";           // 站点管理

export default function HeaderManage({ locale, page }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { setShowLoadingModal, userData, commonText } = useCommonContext();

  const [pageResult] = useState(getLinkHref(locale, page));


  return (
    <header className="sticky bg-white top-0 z-20 w-full border border-b-gray-100 text-black">
      <OneTapComponent />
      <LoadingModal loadingText={commonText.loadingText} />
      <GeneratingModal generatingText={commonText.generateText} />
      <LoginModal redirectPath={pageResult} />
      <LogoutModal redirectPath={pageResult} />
      <ToastContainer />
      <nav className="mx-auto max-w-[80%] flex items-center justify-between p-6 lg:px-8" aria-label="Global">
        <div className="flex">
          <a href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5">
            <img className="h-8 w-auto" src={"/icon/favicon.ico"} width={22} height={24} alt={domainNameLowercase} />
          </a>
          <a href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5">
            <img className="h-8 w-auto" src={"/icon/logo.svg"} width={32} height={24} alt={domainNameLowercase} />
          </a>
        </div>
        <div className="flex lg:hidden">
          <button type="button" className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-500" onClick={() => setMobileMenuOpen(true)}>
            <span className="sr-only">Open main menu</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>
        <div className="hidden ltr:lg:ml-14 rtl:lg:mr-14 lg:flex lg:flex-1 lg:gap-x-6">
          {/* 只保留五个指定的菜单项 */}
          {checkAdminUser(userData) ? (
            <>
              <Link
                prefetch={false}
                href={getLinkHref(locale, manageSites)}
                className={`text-sm font-semibold leading-6 hover:text-[#f05011] ${page == manageSites ? "header-choose-color" : ""}`}
              >
                站点管理
              </Link>
              <Link
                prefetch={false}
                href={getLinkHref(locale, manageGames)}
                className={`text-sm font-semibold leading-6 hover:text-[#f05011] ${page == manageGames ? "header-choose-color" : ""}`}
              >
                游戏管理
              </Link>
              <Link
                prefetch={false}
                href={getLinkHref(locale, managePages)}
                className={`text-sm font-semibold leading-6 hover:text-[#f05011] ${page == managePages ? "header-choose-color" : ""}`}
              >
                页面管理
              </Link>
              <Link
                prefetch={false}
                href={getLinkHref(locale, manageRecommendations)}
                className={`text-sm font-semibold leading-6 hover:text-[#f05011] ${page == manageRecommendations ? "header-choose-color" : ""}`}
              >
                推荐管理
              </Link>
              <Link
                prefetch={false}
                href={getLinkHref(locale, manageTemplates)}
                className={`text-sm font-semibold leading-6 hover:text-[#f05011] ${page == manageTemplates ? "header-choose-color" : ""}`}
              >
                模板管理
              </Link>
            </>
          ) : null}
        </div>

        {isMultiLanguage ? (
          <Menu as="div" className="hidden lg:relative lg:inline-block lg:text-left z-30">
            <div>
              <Menu.Button className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold hover:border-[rgba(255,255,255,0.9)]">
                <GlobeAltIcon className="w-5 h-5" />
                {locale == "default" ? "EN" : locale.toUpperCase()}
                <ChevronDownIcon className="ltr:-mr-1 rtl:-ml-1 h-5 w-5" aria-hidden="true" />
              </Menu.Button>
            </div>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-30 mt-2 w-26 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div className="py-1 z-30">
                  {languages.map((item) => {
                    let hrefValue = `/${item.lang}`;
                    if (page) {
                      hrefValue = `/${item.lang}/${page}`;
                    }
                    return (
                      <Menu.Item key={item.lang}>
                        <Link prefetch={false} href={hrefValue} className={"z-30"}>
                          <span className={"text-gray-700 block px-4 py-2 text-sm hover:text-[#2d6ae0] z-30"}>{item.language}</span>
                        </Link>
                      </Menu.Item>
                    );
                  })}
                </div>
              </Menu.Items>
            </Transition>
          </Menu>
        ) : null}
        <div className="hidden ltr:lg:ml-2 rtl:lg:mr-2 lg:relative lg:inline-block lg:text-left">
          <LoginButton buttonType={userData?.email ? 1 : 0} />
        </div>
      </nav>
      <Dialog as="div" className="lg:hidden" open={mobileMenuOpen} onClose={setMobileMenuOpen}>
        <div className="fixed inset-0 z-30" />
        <Dialog.Panel className="fixed inset-y-0 right-0 z-30 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
          <div className="flex items-center justify-between">
            <div className="flex">
              <Link prefetch={false} href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5">
                <img className="h-8 w-auto" src={"/icon/favicon.ico"} width={22} height={24} alt={domainNameLowercase} />
              </Link>
              <Link prefetch={false} href={getLinkHref(locale, "manage/dashboard")} className="-m-1.5 ltr:ml-0.5 rtl:mr-0.5 p-1.5">
                <img className="h-8 w-auto" src={"/icon/logo.svg"} width={32} height={24} alt={domainNameLowercase} />
              </Link>
            </div>
            <button type="button" className="-m-2.5 rounded-md p-2.5 z-20" onClick={() => setMobileMenuOpen(false)}>
              <span className="sr-only">Close menu</span>
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
          <div className="mt-6 flow-root">
            <div className="divide-y divide-gray-500/10">
              <div className="space-y-2 py-6">
                {/* 移动端菜单项 */}
                {checkAdminUser(userData) ? (
                  <>
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, manageGames)}
                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${page == manageGames ? "text-[#f05011]" : ""}`}
                    >
                      游戏管理
                    </Link>
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, managePages)}
                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${page == managePages ? "text-[#f05011]" : ""}`}
                    >
                      页面管理
                    </Link>
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, manageRecommendations)}
                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${page == manageRecommendations ? "text-[#f05011]" : ""}`}
                    >
                      推荐管理
                    </Link>
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, manageTemplates)}
                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${page == manageTemplates ? "text-[#f05011]" : ""}`}
                    >
                      模板管理
                    </Link>
                    <Link
                      prefetch={false}
                      href={getLinkHref(locale, manageSites)}
                      className={`-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 hover:bg-gray-50 ${page == manageSites ? "text-[#f05011]" : ""}`}
                    >
                      站点管理
                    </Link>
                  </>
                ) : null}
              </div>
              {isMultiLanguage ? (
                <div className="ltr:ml-2 rtl:mr-2 py-4">
                  <Menu as="div" className="relative inline-block text-left z-20">
                    <div>
                      <Menu.Button className="inline-flex w-full justify-center gap-x-1.5 border border-[rgba(255,255,255,0.5)] rounded-md px-3 py-2 text-sm font-semibold hover:border-[rgba(255,255,255,0.9)]">
                        <GlobeAltIcon className="w-5 h-5" />
                        {locale == "default" ? "EN" : locale.toUpperCase()}
                        <ChevronDownIcon className="ltr:-mr-1 rtl:-mr-1 h-5 w-5" aria-hidden="true" />
                      </Menu.Button>
                    </div>
                    <Transition
                      as={Fragment}
                      enter="transition ease-out duration-100"
                      enterFrom="transform opacity-0 scale-95"
                      enterTo="transform opacity-100 scale-100"
                      leave="transition ease-in duration-75"
                      leaveFrom="transform opacity-100 scale-100"
                      leaveTo="transform opacity-0 scale-95"
                    >
                      <Menu.Items className="absolute right-0 z-10 mt-2 w-26 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <div className="py-1">
                          {languages.map((item) => {
                            let hrefValue = `/${item.lang}`;
                            if (page) {
                              hrefValue = `/${item.lang}/${page}`;
                            }
                            return (
                              <Menu.Item key={item.lang}>
                                <Link prefetch={false} href={hrefValue}>
                                  <span className={"text-gray-700 block px-4 py-2 text-sm hover:text-[#2d6ae0]"}>{item.language}</span>
                                </Link>
                              </Menu.Item>
                            );
                          })}
                        </div>
                      </Menu.Items>
                    </Transition>
                  </Menu>
                </div>
              ) : null}
              <div className="relative inline-block text-left text-base font-semibold ltr:ml-2 rtl:mr-2">
                <LoginButton buttonType={userData?.email ? 1 : 0} />
              </div>
            </div>
          </div>
        </Dialog.Panel>
      </Dialog>
    </header>
  );
}
