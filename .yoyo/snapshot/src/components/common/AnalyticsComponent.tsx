'use client'
import { GoogleAnalytics } from "@next/third-parties/google";
import Script from "next/script";
import { useEffect, useRef } from "react";
import { googleAnalyticsOpen, googleAnalyticsId, plausibleAnalyticsOpen, domainNameLowercase, plausibleServerDomain, baiduAnalyticsOpen, baiduAnalyticsId, googleAwOpen, googleAwId, clarityAnalyticsOpen, clarityId } from "~/configs/globalConfig";


export default function AnalyticsComponent() {

  const useCustomEffect = (effect, deps) => {
    const isInitialMount = useRef(true);
    useEffect(() => {
      if (process.env.NODE_ENV === "production" || isInitialMount.current) {
        isInitialMount.current = false;
        return effect();
      }
    }, deps);
  };

  useCustomEffect(() => {
    checkUrlFrom();
    return () => { };
  }, []);

  const checkUrlFrom = () => {
    const urlSearchParams = new URLSearchParams(window.location.search);
    if (urlSearchParams && urlSearchParams.size > 0) {
      localStorage.setItem("url_search_params", urlSearchParams?.toString());
      const utm_campaign = urlSearchParams.get("utm_campaign");
      if (utm_campaign) {
        localStorage.setItem("utm_campaign", utm_campaign);
      }
      const utm_source = urlSearchParams.get("utm_source");
      if (utm_source) {
        localStorage.setItem("utm_source", utm_source);
      }
    }
  };

  return (
    <>
      {googleAnalyticsOpen ? (
        <>
          <GoogleAnalytics gaId={googleAnalyticsId} />
        </>
      ) : null}
      {plausibleAnalyticsOpen ?
        <script defer data-domain={domainNameLowercase} src={`https://${plausibleServerDomain}/js/script.js`}></script>
        : null
      }
      {baiduAnalyticsOpen ? (
        <script
          dangerouslySetInnerHTML={{
            __html: `
              var _hmt = _hmt || [];
              (function() {
                var hm = document.createElement("script");
                hm.src = "https://hm.baidu.com/hm.js?${baiduAnalyticsId}";
                var s = document.getElementsByTagName("script")[0]; 
                s.parentNode.insertBefore(hm, s);
              })();
            `,
          }}
        />
      ) : null}
      {googleAwOpen ? (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${googleAwId}`}
            id="google-ads-script1"
            strategy="afterInteractive"
          />
          <Script
            id="google-ads-script2"
            strategy="afterInteractive"
          >
            {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${googleAwId}');
              `}
          </Script>
        </>
      ) : null}
      {
        clarityAnalyticsOpen ?
          <Script
            id="clarity-script"
            strategy="afterInteractive"
          >
            {`
                (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "${clarityId}");
              `}
          </Script>
          :
          null
      }
    </>
  );
}