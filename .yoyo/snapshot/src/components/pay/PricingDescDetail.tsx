import React from "react";

const PricingDescDetail = ({
  have,
  leftText,
  rightText,
  leftColorBlue = false,
  leftColorRed = false,
  rightColorBlue = false,
  rightColorRed = false,
}) => {
  if (have) {
    return (
      <div className="flex flex-row items-start">
        <div className={`ltr:mr-2 rtl:ml-2 flex ${leftColorBlue ? "text-[#2d6ae0]" : leftColorRed ? "text-red-600" : "text-gray-500"}`}>
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.82291 15.198C8.47774 15.199 8.1399 15.3027 7.84846 15.4969C7.55703 15.6911 7.32392 15.968 7.1761 16.2955C7.02829 16.623 6.9718 16.9878 7.01319 17.3476C7.05457 17.7074 7.19213 18.0476 7.40995 18.3287L12.0534 24.3014C12.219 24.5172 12.4312 24.6885 12.6725 24.8009C12.9137 24.9134 13.177 24.9638 13.4406 24.9479C14.0042 24.9161 14.513 24.5995 14.8375 24.079L24.4831 7.76799C24.4847 7.76528 24.4863 7.76257 24.488 7.75991C24.5785 7.614 24.5492 7.32485 24.3624 7.1432C24.3111 7.09331 24.2506 7.05499 24.1846 7.03058C24.1186 7.00618 24.0486 6.99621 23.9789 7.00129C23.9091 7.00637 23.8411 7.02639 23.7789 7.06013C23.7168 7.09386 23.662 7.14059 23.6177 7.19743C23.6142 7.2019 23.6107 7.2063 23.607 7.21064L13.8792 18.7511C13.8422 18.795 13.7973 18.8308 13.747 18.8563C13.6967 18.8818 13.6421 18.8966 13.5863 18.8998C13.5305 18.9029 13.4747 18.8944 13.4221 18.8747C13.3695 18.8551 13.3211 18.8246 13.2798 18.7852L10.0513 15.7003C9.71603 15.3776 9.27778 15.1984 8.82291 15.198Z"
              fill="currentColor"
            ></path>
          </svg>
        </div>
        <p className={`${rightColorBlue ? "text-[#2d6ae0]" : rightColorRed ? "text-red-600" : "text-gray-500"}`}>
          <span className={`font-bold`}>{leftText}</span>
          <span className={``}> {rightText}</span>
        </p>
      </div>
    );
  } else {
    return (
      <div className="flex flex-row items-start">
        <div className={`ltr:mr-2 rtl:ml-2 flex ${leftColorBlue ? "text-[#2d6ae0]" : leftColorRed ? "text-red-600" : "text-red-600"}`}>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="32" height="32" viewBox="0 0 32 32" strokeWidth="1.5" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" fill="currentColor" />
          </svg>
        </div>
        <p className={`${rightColorBlue ? "text-[#2d6ae0]" : rightColorRed ? "text-red-600" : "text-gray-500"}`}>
          <span className={`font-bold`}>{leftText}</span>
          <span className={``}> {rightText}</span>
        </p>
      </div>
    );
  }
}

export default PricingDescDetail
