"use client";
import { initializePaddle, InitializePaddleOptions, Paddle } from "@paddle/paddle-js";
import { useEffect, useState } from "react";
import { paddleClientToken, paddleSandbox, paddleSellerId } from "~/configs/globalConfig";

export default function usePaddle() {
  const [paddle, setPaddle] = useState<Paddle>();
  useEffect(() => {
    if (paddleSandbox) {
      initializePaddle({
        environment: 'sandbox',
        token: paddleClientToken,
        seller: paddleSellerId,
      } as unknown as InitializePaddleOptions).then((paddleInstance: Paddle | undefined) => {
        if (paddleInstance) {
          setPaddle(paddleInstance);
        }
      });
    } else {
      initializePaddle({
        token: paddleClientToken,
      } as unknown as InitializePaddleOptions).then((paddleInstance: Paddle | undefined) => {
        if (paddleInstance) {
          setPaddle(paddleInstance);
        }
      });
    }
  }, []);

  return paddle;
}
