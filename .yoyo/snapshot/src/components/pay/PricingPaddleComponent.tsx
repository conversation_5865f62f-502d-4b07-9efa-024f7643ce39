'use client'
import React, { useState } from 'react';
import { useCommonContext } from "~/context/common-context";
import LoadingDots from "~/components/LoadingDots";
import { paddle_subscription_priceList } from "~/configs/pay/paddleConfig";
import PricingDescDetail from "~/components/pay/PricingDescDetail";
import { getPricePerString, getPriceString } from "~/configs/pay/payUtil";
import usePaddle from "~/components/pay/usePaddle";
import { getURL } from "~/utils/helpers";
import { getLinkHrefWithoutStartSlash } from "~/utils/buildLink";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { domainNameLowercase, freeGenerateTimes, supportEmail } from '~/configs/globalConfig';

export default function PricingPaddleComponent({
  locale = 'en',
  redirectUrl,
  isPricing = false,
  isIndex = false
}) {

  const [priceIdLoading, setPriceIdLoading] = useState<string>();
  const {
    setShowLoginModal,
    pricingText,
    userData,
    setToastText,
    setShowToastModal,
    setShowPayModal
  } = useCommonContext();

  const paddle = usePaddle();

  const handleCheckout = async (price) => {
    setPriceIdLoading(price.id);
    if (!userData || !userData.user_id) {
      setShowLoginModal(true);
      return
    }
    const user_id = userData.user_id;
    // 检查用户是否订阅
    const response = await fetch(`/api/paddle/checkSubscribe`);
    const res = await response.json();
    if (res.status == 1) {
      setShowLoginModal(true);
      return;
    } else {
      if (res.checkResult?.subscribe_status) {
        setPriceIdLoading("");
        setShowPayModal(false);
        setToastText(pricingText.alreadySubscribed);
        setShowToastModal(true);
        return;
      }
    }
    // 设置支付成功后跳转的页面
    const originUrl = window.location.href;
    localStorage.setItem("successUrl", originUrl);

    // 创建 transaction
    const transactionResponse = await fetch(`/api/paddle/create-transaction`, {
      method: 'POST',
      body: JSON.stringify({
        origin_url: originUrl,
        price_id: price.id
      })
    });
    const transactionData = await transactionResponse.json();
    if (transactionData.status != 0) {
      setPriceIdLoading("");
      setToastText('payment init failed');
      setShowToastModal(true);
      return;
    }
    const transactionId = transactionData.transactionId;

    const successRedirectUrl = getURL() + getLinkHrefWithoutStartSlash(locale, "congratulation");
    const cancelRedirectUrl = getURL() + redirectUrl;

    paddle?.Checkout.open({
      customer: {
        email: userData.email, // email of your current logged in user
      },
      customData: {
        // other custom metadata you want to pass
        user_id: user_id,
        domain: domainNameLowercase
      },
      settings: {
        // settings like successUrl and theme
        successUrl: successRedirectUrl,
        allowLogout: false,
        allowDiscountRemoval: false,
        showAddDiscounts: false,
        theme: 'dark',
      },
      transactionId: transactionId
    });
  }


  if (!paddle_subscription_priceList?.length)
    return (
      <div className={(isPricing ? "" : "background-div") + " flex flex-col items-center"}>
        <div className="max-w-screen-lg grid place-items-center bg-slate-100 text-slate-600 p-4 gap-4">
          <div id="introduction" className="bg-white shadow-md rounded border-slate-200 p-5">
            <div className="text-slate-800 space-y-2 mb-0">
            </div>
          </div>
        </div>
      </div>
    );

  return (
    <section className={(isPricing ? "" : "background-div") + " bg-cover bg-center bg-no-repeat text-white"}>
      <div className="mx-auto w-full 2xl:max-w-7xl px-5 py-4 md:px-6">
        <div className="flex flex-col items-center justify-start">
          <div className="mx-auto mb-12 flex max-w-3xl flex-col items-center">
            {isIndex ? (
              <h2 className="text-4xl font-bold">{pricingText.h1Text}</h2>
            ) : (
              <h1 className="font-inter-tight text-5xl md:text-6xl font-bold text-gray-800 dark:text-transparent dark:bg-clip-text dark:bg-gradient-to-b dark:from-indigo-200 dark:to-gray-200 pb-4">
                {pricingText.h1Text}
              </h1>
            )}
            {isPricing && userData && userData.stripe_customer_id && userData.available_times >= 0 && (!userData.paddle_customer_id || userData.paddle_customer_id == "") ? (
              <div className="mt-8 text-lg">{pricingText.payIntro6}</div>
            ) : null}
          </div>
          <div className="grid w-full grid-cols-1 gap-16 md:grid-cols-3 xl:grid-cols-3 md:gap-4 lg:gap-8">
            {/*$29.9*/}
            <div className="order-1 md:order-2 lg:order-2 xl:order-3 mx-auto flex w-full max-w-[416px] flex-col rounded-xl ring-2 p-2">
              <div className="flex flex-col items-start rounded-tl-xl rounded-tr-xl bg-[#6a63f6] bg-cover bg-center bg-no-repeat p-10 text-white">
                <div className="mb-4 flex flex-row flex-wrap gap-4">
                  <div className="flex items-center gap-1.5 rounded-lg bg-[#ed7470] px-4 py-1.5 text-white">
                    <div className="flex">
                      <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M15.2528 4.59353C15.1098 4.47414 14.9361 4.39736 14.7515 4.37194C14.567 4.34652 14.379 4.37349 14.2091 4.44977L11.0466 5.85602L9.20905 2.54353C9.12123 2.38887 8.99398 2.26026 8.84028 2.17079C8.68657 2.08132 8.5119 2.03418 8.33405 2.03418C8.1562 2.03418 7.98153 2.08132 7.82783 2.17079C7.67412 2.26026 7.54688 2.38887 7.45905 2.54353L5.62155 5.85602L2.45905 4.44977C2.28874 4.37361 2.10052 4.3466 1.91567 4.37181C1.73081 4.39701 1.5567 4.47343 1.413 4.59242C1.2693 4.71141 1.16176 4.86822 1.10252 5.04513C1.04329 5.22205 1.03473 5.412 1.0778 5.59353L2.6653 12.3623C2.69566 12.4933 2.7523 12.6168 2.8318 12.7253C2.9113 12.8338 3.012 12.9251 3.1278 12.9935C3.28458 13.0874 3.46383 13.137 3.64655 13.1373C3.73537 13.1371 3.82373 13.1245 3.90905 13.0998C6.80269 12.2998 9.85916 12.2998 12.7528 13.0998C13.017 13.1692 13.298 13.131 13.5341 12.9935C13.6506 12.926 13.7518 12.835 13.8314 12.7263C13.911 12.6177 13.9672 12.4937 13.9966 12.3623L15.5903 5.59353C15.6329 5.41195 15.6239 5.22208 15.5642 5.04537C15.5046 4.86866 15.3967 4.71215 15.2528 4.59353V4.59353Z"
                          fill="currentColor"
                        ></path>
                      </svg>
                    </div>
                    <p className="text-sm font-bold text-white">{pricingText.popularText}</p>
                  </div>
                </div>
                {isIndex ? (
                  <h3 className="mb-1 text-3xl font-bold md:text-3xl lg:text-4xl">
                    {getPricePerString(paddle_subscription_priceList[0])}
                    <span className={"text-xl"}>/{pricingText.creditText}</span>
                  </h3>
                ) : (
                  <h2 className="mb-1 text-3xl font-bold md:text-3xl lg:text-4xl">
                    {getPricePerString(paddle_subscription_priceList[0])}
                    <span className={"text-xl"}>/{pricingText.creditText}</span>
                  </h2>
                )}
                <div className={"mt-1 mb-2 text-2xl"}>
                  {getPriceString(paddle_subscription_priceList[0])}
                  <span className={"text-xl"}>
                    /{pricingText.monthText} {pricingText.monthlyText}
                  </span>
                </div>
                <a
                  type="button"
                  className="w-full rounded-full bg-white px-6 py-4 text-center font-semibold text-[#6a63f6] cursor-pointer inline-flex justify-center items-center text-sm leading-6 shadow-sm"
                  onClick={() => handleCheckout(paddle_subscription_priceList[0])}
                >
                  {pricingText.buyText}
                  {priceIdLoading && priceIdLoading == paddle_subscription_priceList[0].id && (
                    <i className="flex ltr:pl-2 rtl:pr-2 m-0">
                      <LoadingDots />
                    </i>
                  )}
                </a>
              </div>
              <div className="mt-10 flex flex-col items-start gap-5">
                <PricingDescDetail have={true} leftText={paddle_subscription_priceList[0].availableTimes.toLocaleString()} rightText={pricingText.creditsTextPayYear} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={getPricePerString(paddle_subscription_priceList[0])} rightText={"/ " + pricingText.creditText} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro5} rightText={""} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro1} rightText={""} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro3} rightText={""} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro4} rightText={""} />
              </div>
            </div>
            {/* $9.9 */}
            <div className="order-3 md:order-1 lg:order-1 xl:order-1 mx-auto flex w-full max-w-[416px] flex-col rounded-xl ring-2 p-2">
              <div className="flex w-full flex-col items-start rounded-tl-xl rounded-tr-xl pricing-box p-10">
                <div className="mb-4 rounded-lg bg-[#6a63f6] px-4 py-1.5">
                  <p className="text-sm font-bold text-white">{pricingText.basic}</p>
                </div>
                {isIndex ? (
                  <h3 className="mb-1 text-3xl font-bold md:text-3xl lg:text-4xl">
                    {getPricePerString(paddle_subscription_priceList[1])}
                    <span className={"text-xl"}>/{pricingText.creditText}</span>
                  </h3>
                ) : (
                  <h2 className="mb-1 text-3xl font-bold md:text-3xl lg:text-4xl">
                    {getPricePerString(paddle_subscription_priceList[1])}
                    <span className={"text-xl"}>/{pricingText.creditText}</span>
                  </h2>
                )}
                <div className={"mt-1 mb-2 text-2xl"}>
                  {getPriceString(paddle_subscription_priceList[1])}
                  <span className={"text-xl"}>
                    /{pricingText.monthText} {pricingText.monthlyText}
                  </span>
                </div>
                <a
                  type="button"
                  className="w-full rounded-full bg-[#6a63f6] px-6 py-4 text-center font-semibold text-white cursor-pointer inline-flex justify-center items-center text-sm leading-6 shadow-sm"
                  onClick={() => handleCheckout(paddle_subscription_priceList[1])}
                >
                  {pricingText.buyText}
                  {priceIdLoading && priceIdLoading == paddle_subscription_priceList[1].id && (
                    <i className="flex ltr:pl-2 rtl:pr-2 m-0">
                      <LoadingDots />
                    </i>
                  )}
                </a>
              </div>
              <div className="mt-10 flex flex-col items-start gap-5">
                <PricingDescDetail have={true} leftText={paddle_subscription_priceList[1].availableTimes} rightText={pricingText.creditsTextPay} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={getPricePerString(paddle_subscription_priceList[1])} rightText={"/ " + pricingText.creditText} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro5} rightText={""} rightColorBlue={true} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro1} rightText={""} />
                <PricingDescDetail have={true} leftText={paddle_subscription_priceList[1].downloadTimes} rightText={pricingText.payIntro2} />
                <PricingDescDetail have={true} leftText={pricingText.payIntro4} rightText={""} />
              </div>
            </div>
            {/* Free */}
            <div className="order-4 md:order-4 lg:order-4 xl:order-4 mx-auto hidden md:flex w-full max-w-[416px] flex-col rounded-xl ring-2 p-2">
              <div className="flex w-full flex-col items-start rounded-tl-xl rounded-tr-xl pricing-box p-10">
                <div className="mb-6 rounded-lg pricing-box px-4 py-1.5">
                  <p className="text-sm font-bold s">{pricingText.free}</p>
                </div>
                {isIndex ? (
                  <h3 className="mb-6 text-2xl font-bold md:mb-7 md:text-3xl lg:text-4xl lg:mb-9">{pricingText.free0}</h3>
                ) : (
                  <h2 className="mb-6 text-2xl font-bold md:mb-7 md:text-3xl lg:text-4xl lg:mb-9">{pricingText.free0}</h2>
                )}
                {userData ? (
                  <div className="w-full rounded-full bg-[#6a63f6] px-6 py-4 text-center font-semibold text-white cursor-pointer inline-flex justify-center items-center text-sm leading-6 shadow-sm">
                    {pricingText.buyText}
                  </div>
                ) : (
                  <a
                    type="button"
                    className="w-full rounded-full bg-[#6a63f6] px-6 py-4 mt-2 text-center font-semibold text-white cursor-pointer inline-flex justify-center items-center text-sm leading-6 shadow-sm"
                    onClick={() => setShowLoginModal(true)}
                  >
                    {pricingText.loginToGetText}
                  </a>
                )}
              </div>
              <div className="mt-10 flex flex-col items-start gap-5">
                <PricingDescDetail have={true} leftText={freeGenerateTimes} rightText={pricingText.creditsText} />
                <PricingDescDetail have={true} leftText={pricingText.freeIntro0} rightText={""} />
                <PricingDescDetail have={true} leftText={pricingText.freeIntro3} rightText={""} rightColorBlue={true} />
                <PricingDescDetail have={false} leftText={pricingText.freeIntro1} rightText={""} />
                <PricingDescDetail have={false} leftText={pricingText.freeIntro2} rightText={""} />
              </div>
            </div>

            {/* $1.9 */}
            {!checkAdminUser(userData) ? null : (
              <div className="order-5 md:order-5 lg:order-5 xl:order-5 mx-auto flex w-full max-w-[416px] flex-col rounded-xl ring-2 p-2">
                <div className="flex w-full flex-col items-start rounded-tl-xl rounded-tr-xl pricing-box p-10">
                  <h2 className="mb-1 text-3xl font-bold md:text-3xl lg:text-4xl">{getPriceString(paddle_subscription_priceList[2])}</h2>
                  <a
                    type="button"
                    className="w-full rounded-full bg-[#6a63f6] px-6 py-4 text-center font-semibold text-white cursor-pointer inline-flex justify-center items-center text-sm leading-6 shadow-sm"
                    onClick={() => handleCheckout(paddle_subscription_priceList[2])}
                  >
                    {pricingText.buyText}
                    {priceIdLoading && priceIdLoading == paddle_subscription_priceList[2].id && (
                      <i className="flex ltr:pl-2 rtl:pr-2 m-0">
                        <LoadingDots />
                      </i>
                    )}
                  </a>
                </div>
              </div>
            )}
          </div>
          {isIndex || !isPricing ? null : (
            <p className={"break-words mt-8 text-gray-400 "}>
              {pricingText.contactTipBegin}
              <a href={`mailto:${supportEmail}`} className={"text-blue-500"}>
                {supportEmail}
              </a>
              {pricingText.contactTipEnd}
            </p>
          )}
        </div>
      </div>
    </section>
  );


}
