import {getDb} from "~/libs/db/db";
import {paddleSubscriptionComplete} from "~/servers/pay/manageUserTimes";

const db = getDb();

export const managePaddleSubscription = async (parsedBody) => {
  const event_id = parsedBody.event_id;
  const event_type = parsedBody.event_type;
  const data = parsedBody.data;
  const subscription_id = data.id;
  const subscription_price_id = data.items[0].price.id;
  const subscription_status = data.status;
  const subscription_start_date = data.current_billing_period?.starts_at;
  const subscription_end_date = data.current_billing_period?.ends_at;
  const customer_id = data.customer_id;
  const user_id = data.custom_data?.user_id;
  const data_all = parsedBody;

  // 判断是否有该订阅
  const {rows: existSubscription} = await db.query(`
        SELECT * FROM paddle_user_payment_data where subscription_id=$1 limit 1
  `, [subscription_id]);

  if (existSubscription.length <= 0) {
    // 订阅不存在，新增订阅
    await db.query(`
      insert into paddle_user_payment_data(event_id, event_type, data, subscription_id, subscription_price_id, subscription_status,subscription_start_date, subscription_end_date, customer_id, user_id, data_all) values ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    `, [event_id, event_type, data, subscription_id, subscription_price_id, subscription_status, subscription_start_date, subscription_end_date, customer_id, user_id, data_all]);
    // 新增订阅，需要处理订阅次数
    await paddleSubscriptionComplete(subscription_price_id, subscription_status, user_id, customer_id);
  } else {
    // 订阅存在，更新订阅
    await db.query(`
      update paddle_user_payment_data set event_id=$2, event_type=$3, data=$4, subscription_price_id=$5, subscription_status=$6,subscription_start_date=$7, subscription_end_date=$8, data_all=$9,updated_at=now() where subscription_id=$1
    `, [subscription_id, event_id, event_type, data, subscription_price_id, subscription_status,subscription_start_date, subscription_end_date, data_all]);
    // 判断已存在是否订阅，如果是订阅那么需要判断订阅开始时间是否小于本次开始时间，小于的话是新的一期订阅，就调用处理订阅次数
    const existSub = existSubscription[0];
    if (existSub.subscription_status === 'active' && subscription_status === 'active') {
      // 转时间戳
      const existSubStart = (new Date(existSub.subscription_start_date).getTime());
      // console.log('existSubStart', existSubStart);
      // console.log('new start', new Date(subscription_start_date).getTime());
      if (existSubStart < new Date(subscription_start_date).getTime()) {
        await paddleSubscriptionComplete(subscription_price_id, subscription_status, user_id, customer_id);
      }
    }
    if (existSub.subscription_status != 'active' && subscription_status == 'active') {
      await paddleSubscriptionComplete(subscription_price_id, subscription_status, user_id, customer_id);
    }
    if (subscription_status != 'active') {
      // 取消订阅或其他状态，需要处理订阅次数
      await paddleSubscriptionComplete(subscription_price_id, subscription_status, user_id, customer_id);
    }
  }

}
