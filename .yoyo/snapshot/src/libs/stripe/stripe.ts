import Stripe from "stripe";
import { stripeSecret<PERSON>ey } from "~/configs/globalConfig";

export const stripe = new Stripe(stripeSecretKey, {
  // https://github.com/stripe/stripe-node#configuration
  apiVersion: "2024-06-20",
  // Register this as an official Stripe plugin.
  // https://stripe.com/docs/building-plugins#setappinfo
  appInfo: {
    name: "NEXT-PAY",
    version: "0.1.0",
  },
});
