import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { r2AccessKeyId, r2Bucket, r2Endpoint, r2SecretAccessKey } from "~/configs/globalConfig";

export const R2 = new S3Client({
  endpoint: r2Endpoint,
  credentials: {
    accessKeyId: r2AccessKeyId,
    secretAccessKey: r2SecretAccessKey,
  },
  region: "auto",
});

// 生成预签名 URL
export const generatePresignedUrl = async (objectKey) => {
  const url = await getSignedUrl(
    R2,
    new PutObjectCommand({
      Bucket: r2Bucket,
      Key: objectKey,
    }),
    {
      expiresIn: 60 * 2,
    }
  );
  return url;
};
