import { checkAvailableTime, checkLogin } from "~/configs/globalConfig";
import { getUserByServerSession } from "./common/user";
import { checkSubscribe } from "./pay/subscribe";
import { checkUserTimes } from "./pay/manageUserTimes";

// 检查用户调用 API 的权限
export const checkUser = async () => {
  // 获取用户信息
  const user_info = await getUserByServerSession();

  const result = {
    status: 0,
    user_info: user_info,
    check_result: true,
    msg: "",
    is_vip: false,
  };

  // 如果不需要检查用户登录，则直接返回用户信息
  if (!checkLogin) {
    return result;
  }

  // 如果需要检查用户登录，则检查
  if (!user_info.user_id) {
    result.status = 601;
    result.msg = "Login to continue";
    result.check_result = false;
    result.is_vip = false;
    return result;
  }

  // 如果不需要检查用户订阅状态，则直接返回用户信息
  if (!checkAvailableTime) {
    result.status = 0;
    result.msg = "";
    result.check_result = true;
    result.is_vip = false;

    // 校验用户可用次数
    const check_times = await checkUserTimes(user_info.user_id);
    if (!check_times) {
      result.status = 603;
      result.msg = "not enough times";
      result.check_result = false;
      return result;
    }

    return result;
  }

  // 如果需要检查用户订阅状态，则检查
  const check_subscribe_status = await checkSubscribe(user_info.user_id);
  result.is_vip = check_subscribe_status;

  // 订阅校验通过了，校验用户可用次数
  const check_times = await checkUserTimes(user_info.user_id);
  if (!check_times) {
    result.status = 602;
    result.msg = "Pricing to continue.";
    result.check_result = false;
    return result;
  }

  // 用户可用次数校验通过了，返回用户信息+校验通过标识
  result.status = 0;
  result.msg = "";
  result.check_result = true;
  return result;
};
