import { getDb } from "~/libs/db/db";
import { generateSectionBaseId } from "~/utils/uidUtil";

const db = getDb();

export const addNewSection = async (json: any) => {
  const { section_name, section_type, section_json, section_sort, status, styles } = json;

  // 判断是否存在
  const { rows: existList } = await db.query(
    `
    select * from landing_section where section_name = $1 and is_delete = false
  `,
    [section_name]
  );
  if (existList.length > 0) {
    return {
      code: 400,
      message: `${section_name} 已存在！`,
    };
  } else {
    const base_id = generateSectionBaseId();
    const { rows: result } = await db.query(
      `
      INSERT INTO landing_section (base_id, section_name, section_type, section_json, section_sort, status ,styles) VALUES ($1, $2, $3, $4, $5, $6 ,$7)
    `,
      [base_id, section_name, section_type, section_json, section_sort, status, styles]
    );
    return {
      code: 200,
      message: "添加成功！",
      result: result,
    };
  }
};

export const getSectionList = async (json: any) => {
  const positionPage = json.positionPage;
  const pageSize = 10;
  const skipSize = pageSize * (Number(positionPage) - 1);

  let sqlQuery = `
    SELECT * FROM landing_section
    where is_delete = false $$sqlQueryAppend$$
    ORDER BY section_sort ASC
    LIMIT $1 OFFSET $2
  `;
  let sqlParams = [pageSize, skipSize];
  let sqlAppend = "";

  // 拼接sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlAppend);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  // 查询出页数信息
  const pageDesc = await getTotalPage(json);

  return {
    code: 200,
    message: "",
    resultList: list,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

const getTotalPage = async (json: any) => {
  const pageSize = 10;

  let sqlQuery = `
    SELECT COUNT(1) FROM landing_section
    where is_delete = false $$sqlQueryAppend$$
  `;
  let sqlParams = [];
  let sqlAppend = "";

  // 拼接sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlAppend);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

export const updateSectionById = async (json: any) => {
  const { id, section_name, section_type, section_json, section_sort, status, styles } = json;

  const { rows: result } = await db.query(
    `
    UPDATE landing_section
    SET section_name = $1, section_type = $2, section_json = $3, section_sort = $4, status = $5, styles = $6, updated_at = now()
    WHERE id = $7
  `,
    [section_name, section_type, section_json, section_sort, status, styles, id]
  );

  return {
    code: 200,
    message: "更新成功！",
    result: result,
  };
};

export const changeSectionStatus = async (json: any) => {
  const { id, status } = json;

  const { rows: result } = await db.query(`UPDATE landing_section SET status = $1 WHERE id = $2`, [status, id]);

  return {
    code: 200,
    message: "更新成功！",
    result: result,
  };
};

export const deleteSectionById = async (json: any) => {
  const { id } = json;

  const { rows: result } = await db.query(`UPDATE landing_section SET is_delete = true WHERE id = $1`, [id]);

  return {
    code: 200,
    message: "删除成功！",
    result: result,
  };
};

export const getPublishSectionList = async () => {
  const { rows: list } = await db.query(`
    SELECT * FROM landing_section
    WHERE status = 1 AND is_delete = false
    ORDER BY section_sort ASC
  `);

  return {
    code: 200,
    message: "",
    resultList: list,
  };
};
export const updateSectionStyles = async () => {
  // 获取所有记录
  const { rows: sections } = await db.query(`
    SELECT id, section_json, styles 
    FROM landing_section 
    WHERE is_delete = false
  `);

  // 遍历处理每条记录
  for (const section of sections) {
    try {
      let sectionJson = section.section_json;

      // 如果section_json中包含styles属性
      if (sectionJson && sectionJson.styles) {
        // 提取styles对象
        const styles = sectionJson.styles;
        // 删除section_json中的styles
        delete sectionJson.styles;

        // 更新数据库记录
        await db.query(
          `
          UPDATE landing_section 
          SET section_json = $1, styles = $2
          WHERE id = $3
        `,
          [sectionJson, styles, section.id]
        );
      }
    } catch (error) {
      console.error(`处理记录ID ${section.id} 时出错:`, error);
    }
  }

  return {
    code: 200,
    message: "样式数据迁移完成",
  };
};
