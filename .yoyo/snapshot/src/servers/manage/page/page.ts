import { pagePage } from "~/configs/globalConfig";
import { getDb } from "~/libs/db/db";
import { generatePageBaseId } from "~/utils/uidUtil";

const db = getDb();
export const getPageData = async (page_url: string, status = 1) => {
  let resultList = [];
  if (status === 0) {
    // 查询未发布以及已发布的数据
    const { rows: result } = await db.query(
      `
        SELECT * FROM landing_page WHERE page_url = $1 and is_delete = $2
      `,
      [page_url, false]
    );
    resultList = result;
  } else {
    // 查询已发布的数据
    const { rows: result } = await db.query(
      `
        SELECT * FROM landing_page WHERE page_url = $1 and is_delete = $2 and status = $3
      `,
      [page_url, false, status]
    );
    resultList = result;
  }

  if (resultList.length === 0) {
    return {
      status: 404,
      message: "Page not found",
    };
  }
  const pageData = resultList[0];
  const contentList = await getContentListByPageUrl(pageData.page_url, status);
  return {
    ...pageData,
    contentList,
  };
};

// 根据 page_url,module_type,status 获取 page_data
export const getPageDataByModuleType = async (page_url: string, module_type: string, status: number) => {
  const { rows: resultList } = await db.query(`
    SELECT * FROM landing_page WHERE page_url = $1 and module_type = $2 and status = $3 and is_delete = false
  `, [page_url, module_type, status]);
  const pageData = resultList[0];
  if (!pageData || pageData.length === 0) {
    return {
      status: 404,
      message: "Page not found",
    };
  }
  const contentList = await getContentListByPageUrl(pageData.page_url, status);
  return {
    ...pageData,
    contentList,
  };
};

// 根据 module_type 获取所有已发布的 page 列表
export const getAllPageDataByModuleType = async (module_type: string) => {
  const { rows: resultList } = await db.query(`
    SELECT * FROM landing_page
    WHERE module_type = $1 and is_delete = false and status = 1
    ORDER BY updated_at DESC
  `, [module_type]);
  if (resultList.length === 0) {
    return {
      status: 404,
      message: "Page list not found",
    };
  }
  return resultList;
};

export const getIndexPageData = async () => {
  const { rows: resultList } = await db.query(`
    SELECT * FROM landing_page
    WHERE show_in_index_page = true and is_delete = false and status = 1
    ORDER BY updated_at DESC
    LIMIT 1
  `);
  const pageData = resultList[0];
  if (!pageData) {
    return null;
  }
  let contentList = [];
  if (pageData.page_url) {
    contentList = await getContentListByPageUrl(pageData.page_url);
  }
  return {
    ...pageData,
    contentList,
  };
};

export const getContentListByPageUrl = async (page_url: string, status = 1) => {
  let result = [];
  if (status == 0) {
    // 查询未发布以及已发布的数据
    const { rows: resultList } = await db.query(
      `
      SELECT * FROM landing_content
      WHERE page_url = $1 and is_delete = false
      ORDER BY content_sort ASC
        `,
      [page_url]
    );
    result = resultList;
  } else {
    // 查询已发布的数据
    const { rows: resultList } = await db.query(
      `
      SELECT * FROM landing_content
      WHERE page_url = $1 and is_delete = false and status = $2
      ORDER BY content_sort ASC
        `,
      [page_url, status]
    );
    result = resultList;
  }
  return result;
};

export const getPageList = async (json: any) => {
  const positionPage = json.positionPage;
  const page_url = json.page_url;
  const pageSize = 10;
  const skipSize = pageSize * (Number(positionPage) - 1);

  let sqlQuery = `
    SELECT * FROM landing_page
    where is_delete = false $$sqlQueryAppend$$
    ORDER BY updated_at DESC
    LIMIT $1 OFFSET $2
  `;
  let sqlParams = [pageSize, skipSize];
  let sqlAppend = "";

  if (page_url) {
    sqlParams.push(page_url);
    sqlAppend += ` and page_url = $${sqlParams.length}`;
  }

  // 拼接sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlAppend);

  const { rows: list } = await db.query(sqlQuery, sqlParams);

  // 查询出页数信息
  const pageDesc = await getTotalPage(json);

  return {
    code: 200,
    message: "",
    resultList: list,
    totalPage: pageDesc.totalPage,
    countTotal: pageDesc.countTotal,
  };
};

const getTotalPage = async (json: any) => {
  const pageSize = 10;
  const page_url = json.page_url;

  let sqlQuery = `
    SELECT COUNT(1) FROM landing_page
    where is_delete = false $$sqlQueryAppend$$
  `;
  let sqlParams = [];
  let sqlAppend = "";

  if (page_url) {
    sqlParams.push(page_url);
    sqlAppend += ` and page_url = $${sqlParams.length}`;
  }

  // 拼接sql
  sqlQuery = sqlQuery.replace("$$sqlQueryAppend$$", sqlAppend);

  const { rows: countTotal } = await db.query(sqlQuery, sqlParams);

  const total = countTotal[0].count;

  return {
    totalPage: Math.ceil(total / pageSize),
    countTotal: total,
  };
};

export const addNewPage = async (json: any) => {
  const { page_url, page_title, page_description, page_name, out_link_url, status, page_banner, module_type, page_intro } = json;

  // 判断是否存在
  const { rows: existList } = await db.query(
    `
    select * from landing_page where page_url = $1 and is_delete = false
  `,
    [page_url]
  );
  if (existList.length > 0) {
    return {
      code: 400,
      message: `${page_url} 已存在！`,
    };
  } else {
    const base_id = generatePageBaseId();
    const { rows: result } = await db.query(
      `
      INSERT INTO landing_page (base_id, page_url, page_title, page_description, page_name, out_link_url, status, page_banner, module_type, page_intro) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `,
      [base_id, page_url, page_title, page_description, page_name, out_link_url, status, page_banner, module_type, page_intro]
    );
    return {
      code: 200,
      message: "添加成功！",
      result: result,
    };
  }
};

export const updatePageById = async (json: any) => {
  const { id, page_url, page_title, page_description, page_name, out_link_url, status, page_banner, module_type, page_intro } = json;

  const { rows: result } = await db.query(
    `
    UPDATE landing_page SET page_url = $1, page_title = $2, page_description = $3, page_name = $4, out_link_url = $5, status = $6, page_banner = $7, module_type = $8, page_intro = $9, updated_at = now() WHERE id = $10
  `,
    [page_url, page_title, page_description, page_name, out_link_url, status, page_banner, module_type, page_intro, id]
  );

  return {
    code: 200,
    message: "更新成功！",
    result: result,
  };
};

export const changePageStatus = async (json: any) => {
  const { id, status } = json;

  const { rows: result } = await db.query(`UPDATE landing_page SET status = $1, updated_at = now() WHERE id = $2`, [status, id]);

  return {
    code: 200,
    message: "更新成功！",
    result: result,
  };
};

export const deletePageById = async (json: any) => {
  const { id } = json;

  const { rows: result } = await db.query(`UPDATE landing_page SET is_delete = true WHERE id = $1`, [id]);

  return {
    code: 200,
    message: "删除成功！",
    result: result,
  };
};

export const setToIndexPage = async (json: any) => {
  const { id } = json;

  const { rows: result } = await db.query(
    `
    UPDATE landing_page SET show_in_index_page = true, updated_at = now() WHERE id = $1
  `,
    [id]
  );

  // 更新其他为true的为false
  await db.query(
    `
    UPDATE landing_page SET show_in_index_page = false
    WHERE show_in_index_page = true and id != $1
  `,
    [id]
  );

  return {
    code: 200,
    message: "设置成功！",
    result: result,
  };
};

export const getPublishedPage = async () => {
  const { rows: resultList } = await db.query(
    `
    SELECT * FROM landing_page
    WHERE status = 1 and is_delete = false
    ORDER BY updated_at DESC
  `
  );
  return resultList;
};

export const getPageContentSection = async (page_url: string) => {
  // 查询出page_url对应的 content 并且 left section
  const { rows: resultList } = await db.query(
    `
    select lc.*, ls.section_name, ls.section_type, ls.section_json, ls.section_name,ls.styles from landing_content lc
    left join landing_section ls on ls.base_id = lc.section_base_id
    where lc.page_url = $1
    order by lc.content_sort asc
  `,
    [page_url]
  );
  return resultList;
};
