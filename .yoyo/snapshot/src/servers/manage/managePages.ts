import { getDb } from "~/libs/db/db";

// Add a new function to update page by UID with homepage uniqueness check
export const updatePageByUid = async (uid: string, pageData: any) => {
  const db = getDb(); // Ensure db connection is available

  // Check if the page exists
  const existingPage = await db.query(
    `SELECT * FROM pages WHERE uid = $1`,
    [uid]
  );

  if (existingPage.rows.length === 0) {
    return { code: 404, message: "页面未找到！" };
  }

  const siteUid = existingPage.rows[0].site_uid; // Assume site_uid is available in page data

  // Removed explicit unset logic - rely on database constraint
  // The check for existing homepage logic is also removed from here

  // Build dynamic update query based on provided data
  const fieldsToUpdate = [];
  const queryParams = [uid];
  let paramIndex = 2; // Start index after uid

  if (pageData.hasOwnProperty('public_url')) {
    fieldsToUpdate.push(`public_url = $${paramIndex++}`);
    queryParams.push(pageData.public_url || null);
  }
  if (pageData.hasOwnProperty('is_homepage')) {
    fieldsToUpdate.push(`is_homepage = $${paramIndex++}`);
    queryParams.push(pageData.is_homepage || false);
  }
  if (pageData.hasOwnProperty('json_content')) {
    fieldsToUpdate.push(`json_content = $${paramIndex++}`);
    queryParams.push(JSON.stringify(pageData.json_content) || null); // Assuming json_content is stored as JSON string
  }
   if (pageData.hasOwnProperty('iframe_url')) {
    fieldsToUpdate.push(`iframe_url = $${paramIndex++}`);
    queryParams.push(pageData.iframe_url || null);
  }
  if (pageData.hasOwnProperty('slug')) {
    fieldsToUpdate.push(`slug = $${paramIndex++}`);
    queryParams.push(pageData.slug || '');
  }
  if (pageData.hasOwnProperty('template_key')) {
    fieldsToUpdate.push(`template_key = $${paramIndex++}`);
    queryParams.push(pageData.template_key || null);
  }
  // Add other updatable fields here following the same pattern
  
  if (fieldsToUpdate.length === 0) {
      return { code: 400, message: "没有提供需要更新的字段！" };
  }

  const updateQuery = `
    UPDATE pages
    SET ${fieldsToUpdate.join(', ')}, updated_at = NOW()
    WHERE uid = $1
    RETURNING *;
  `;

  try {
    const { rows: updatedRows } = await db.query(updateQuery, queryParams);
    if (updatedRows.length === 0) {
        // Should not happen if existingPage check passed, but for safety
        console.error("updatePageByUid: Update succeeded but returned no rows."); // Log unexpected
        return { code: 500, message: "更新失败，请重试。" };
    }
    return { code: 200, data: updatedRows[0], message: "页面更新成功！" };
  } catch (error: any) {
    console.error("Error updating page by UID:", error); // Log the specific error

    // Check for specific database unique constraint error
    if (error.constraint === 'unique_homepage_per_domain') {
        console.error("updatePageByUid: Caught homepage uniqueness constraint violation."); // Log specific error
        return { code: 400, message: "该站点已存在首页，不能重复设置。" };
    }
    
    // For other errors, return a generic message
    return { code: 500, message: "更新页面时发生错误！" };
  }
}; 