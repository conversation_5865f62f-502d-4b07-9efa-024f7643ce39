import { getDb } from "~/libs/db/db";
import { generateNewShortUniqueUID } from "~/utils/uidUtil";
import { getUserByServerSession } from "~/servers/common/user";
import { checkAdminUser } from "~/utils/checkWhiteUser";
import { getGameByUid as getGameByUidService, updateGameByUid, deleteGameByUid as deleteGameByUidBase } from "~/servers/game/gameService";

const db = getDb();

export const saveGame = async (gameData: {
  id?: number;
  keyword: string;
  name?: string;
  iframe_url?: string;
  reference_data?: string;
  tags?: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Process tags
  const processedTags = gameData.tags
    ? gameData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    : [];

  if (gameData.id) {
    // Update existing game
    const updateQuery = `
      UPDATE games
      SET keyword = $1, iframe_url = $2, reference_data = $3, tags = $4, updated_at = NOW()
      WHERE id = $5
      RETURNING *;
    `;
    const { rows } = await db.query(updateQuery, [
      gameData.keyword,
      gameData.iframe_url || null,
      gameData.reference_data || null,
      processedTags.length > 0 ? JSON.stringify(processedTags) : null,
      gameData.id
    ]);

    if (rows.length === 0) {
      return { code: 404, message: "Game not found" };
    }

    return { code: 200, data: rows[0], message: "Game updated successfully" };
  } else {
    // Check if keyword already exists
    const checkKeywordQuery = `
      SELECT * FROM games WHERE keyword = $1
    `;
    const { rows: keywordRows } = await db.query(checkKeywordQuery, [gameData.keyword]);
    if (keywordRows.length > 0) {
      return { code: 400, message: "Game keyword already exists" };
    }

    // Generate a unique UID for the new game
    const uid = generateNewShortUniqueUID();

    // Create new game
    const insertQuery = `
      INSERT INTO games (keyword, iframe_url, reference_data, tags, uid)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *;
    `;
    const { rows } = await db.query(insertQuery, [
      gameData.keyword,
      gameData.iframe_url || null,
      gameData.reference_data || null,
      processedTags.length > 0 ? JSON.stringify(processedTags) : null,
      uid // 使用生成的唯一ID
    ]);

    return { code: 200, data: rows[0], message: "Game created successfully" };
  }
};

// New function to save game by UID
export const saveGameByUid = async (gameData: {
  uid: string;
  keyword: string;
  name?: string;
  iframe_url?: string;
  reference_data?: string;
  tags?: string;
}) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Process tags
  const processedTags = gameData.tags
    ? gameData.tags.split(',').map(tag => tag.trim()).filter(Boolean)
    : [];

  try {
    // Check if game exists
    const existingGame = await getGameByUidService(gameData.uid);
    
    if (!existingGame) {
      return { code: 404, message: "Game not found" };
    }
    
    // Update existing game by UID
    const updatedGame = await updateGameByUid(gameData.uid, {
      keyword: gameData.keyword,
      iframe_url: gameData.iframe_url,
      reference_data: gameData.reference_data,
      tags: processedTags.length > 0 ? processedTags : undefined
    });
    
    return { code: 200, data: updatedGame, message: "Game updated successfully" };
  } catch (error) {
    console.error("Error in saveGameByUid:", error);
    return { code: 500, message: "Error updating game" };
  }
};

// Delete a game (actual delete)
export const deleteGame = async (id: number) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  // Perform real delete operation
  const deleteQuery = `
    DELETE FROM games WHERE id = $1
    RETURNING id;
  `;
  const { rows } = await db.query(deleteQuery, [id]);
  
  if (rows.length === 0) {
    return { code: 404, message: "Game not found" };
  }
  
  return { code: 200, message: "Game deleted successfully" };
};

// New function to delete game by UID
export const deleteGameByUid = async (uid: string) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403,
      message: "Unauthorized: Admin access required"
    };
  }

  try {
    const result = await deleteGameByUidBase(uid);
    
    if (!result) {
      return { code: 404, message: "Game not found" };
    }
    
    return { code: 200, message: "Game deleted successfully" };
  } catch (error) {
    console.error("Error in deleteGameByUid:", error);
    return { code: 500, message: "Error deleting game" };
  }
};

// Get game by ID
export const getGameById = async (id: number) => {
  const getQuery = `
    SELECT * FROM games WHERE id = $1
  `;
  const { rows } = await db.query(getQuery, [id]);
  
  if (rows.length === 0) {
    return { code: 404, message: "Game not found" };
  }
  
  return { code: 200, data: rows[0], message: "Game retrieved successfully" };
};

// New function to get game by UID
export const getGameByUid = async (uid: string) => {
  try {
    const game = await getGameByUidService(uid);
    
    if (!game) {
      return { code: 404, message: "Game not found" };
    }
    
    return { code: 200, data: game, message: "Game retrieved successfully" };
  } catch (error) {
    console.error("Error in getGameByUid:", error);
    return { code: 500, message: "Error retrieving game" };
  }
};

// Get all games (with pagination)
export const getGames = async (params: { pageSize?: number, skipSize?: number }) => {
  const user_info = await getUserByServerSession();
  if (!checkAdminUser(user_info)) {
    return {
      code: 403, 
      data: {
        resultList: [],
        totalPage: 0,
        countTotal: 0
      },
      message: "Unauthorized: Admin access required"
    };
  }

  const pageSize = params.pageSize || 10;
  const skipSize = params.skipSize || 0;

  const getQuery = `
    SELECT * FROM games 
    ORDER BY created_at DESC
    LIMIT $1 OFFSET $2
  `;
  const { rows } = await db.query(getQuery, [pageSize, skipSize]);
  
  // Get total count for pagination
  const countQuery = `
    SELECT COUNT(*) FROM games
  `;
  const { rows: countRows } = await db.query(countQuery);
  const total = parseInt(countRows[0].count);
  
  return { 
    code: 200, 
    data: {
      resultList: rows,
      totalPage: Math.ceil(total / pageSize),
      countTotal: total
    }, 
    message: "Games retrieved successfully" 
  };
}; 