import { getDb } from "~/libs/db/db";
import { createPagination, getArrayUrlResult } from "~/utils/handleData";
import { getUserByServerSession } from "~/servers/common/user";
import { discoverPageSize, myPageSize } from "~/configs/globalConfig";

const db = getDb();

// 获取作品详情
export const getWorkDetailByUid = async (uid: string) => {
  const resultsCurrent = await db.query(
    `
      select w.* from works w
        where w.uid=$1 and w.is_delete=$2
        order by w.created_at desc limit 1
  `,
    [uid, false]
  );
  const currentRows = resultsCurrent.rows;
  if (currentRows.length > 0) {
    const currentRow = currentRows[0];
    return currentRow;
  }
  return {
    status: 404,
  };
};

// 获取用户作品列表
export const getWorkListByUserId = async (user_id: string, current_page: string) => {
  const pageSize = myPageSize;
  const skipSize = pageSize * (Number(current_page) - 1);

  const results = await db.query(
    `
    select * from works
      where user_id=$3 and is_delete=$4
      order by created_at desc
      limit $1 offset $2
    `,
    [pageSize, skipSize, user_id, false]
  );
  const works = results.rows;

  const resultInfoList = [];
  if (works.length > 0) {
    for (let i = 0; i < works.length; i++) {
      const currentRow = works[i];
      resultInfoList.push(currentRow);
    }
  }

  const result = {
    status: 0,
    data: resultInfoList,
    pagination: await getPagination(user_id, current_page),
  };

  return result;
};

// 获取分页信息
export const getPagination = async (user_id, page) => {
  const pageSize = myPageSize;
  const results = await db.query(
    `
    select count(1) from works
    where user_id=$1 and is_delete=$2
  `,
    [user_id, false]
  );
  const countTotal = results.rows;

  const total = countTotal[0].count;
  const totalPage = Math.ceil(total / pageSize);

  const result = {
    totalPage: totalPage,
    pagination: createPagination(totalPage, Number(page), 6),
  };
  return result;
};

// 获取公开作品列表
export const getPublicResultList = async (current_page) => {
  const pageSize = discoverPageSize;
  const skipSize = pageSize * (Number(current_page) - 1);

  const results = await db.query(
    `
    select * from works
      where is_public=$3 and status=$4 and is_delete=$5
      order by updated_at desc
      limit $1 offset $2
    `,
    [pageSize, skipSize, true, 1, false]
  );
  const works = results.rows;

  const resultInfoList = [];
  if (works.length > 0) {
    for (let i = 0; i < works.length; i++) {
      const currentRow = works[i];
      resultInfoList.push(currentRow);
    }
    return resultInfoList;
  }

  return [];
};

// 获取最新公开作品列表，首页数据
export const getLatestPublicResultList = async (current_page) => {
  // 首页数据
  const pageSize = 8;
  const skipSize = pageSize * (Number(current_page) - 1);

  const results = await db.query(
    `
    select * from works
    where is_public=$3 and status=$4 and is_delete=$5
    order by updated_at desc
    limit $1 offset $2
  `,
    [pageSize, skipSize, true, 1, false]
  );
  const works = results.rows;

  const resultInfoList = [];
  if (works.length > 0) {
    for (let i = 0; i < works.length; i++) {
      const currentRow = works[i];
      resultInfoList.push(currentRow);
    }
    return resultInfoList;
  }

  return [];
};

// 删除作品
export const deleteWorkByUid = async (uid: string) => {
  if (!uid) {
    return;
  }
  // 通过getUserByServerSession()获取用户信息
  const currentUser = await getUserByServerSession();
  if (!currentUser) {
    throw new Error("not login");
  }

  // 查询作品信息
  const workResult = await db.query(
    `
    SELECT * FROM works
      WHERE uid=$1 and is_delete=$2
    `,
    [uid, false]
  );
  if (workResult.rows.length === 0) {
    return {
      status: 404,
      msg: "work not found",
    };
  }
  const work = workResult.rows[0];
  // 检查当前用户是否是作品的创建者
  if (work.user_id !== currentUser.user_id) {
    return {
      status: 403,
      msg: "no permission",
    };
  }
  await db.query(
    `
    update works set is_delete=$1 where uid=$2
    `,
    [true, uid]
  );
  return {
    status: 0,
    msg: "delete success",
  };
};
