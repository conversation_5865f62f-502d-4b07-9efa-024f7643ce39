import Stripe from "stripe";
import { getDb } from "~/libs/db/db";
import { availableTimes, downloadTimes, getSubscriptionPrice, pricesValue } from "~/configs/pay/stripeConfig";
import { getPaddleSubscriptionPrice } from "~/configs/pay/paddleConfig";
import { freeGenerateTimes, notLoginFreeUseTimes } from "~/configs/globalConfig";
import { isBlockCountry } from "~/utils/handleData";
import { headers } from "next/headers";
import { getPriceByPayProPriceId } from "~/configs/pay/paypro";

const db = getDb();
export const userPayComplete = async (checkoutSession: Stripe.Checkout.Session) => {
  let customer: string;
  if (checkoutSession.customer) {
    customer = String(checkoutSession.customer);
  } else {
    console.log("userPayComplete-error=-=->");
    console.log("checkoutSession-=-=->", checkoutSession);
  }
  const status = checkoutSession.status;
  if (status == "complete") {
    const newTimes = getTimesByCheckout(checkoutSession);
    const newDownloadTimes = getDownloadTimesByCheckout(checkoutSession);
    const results = await db.query(
      `
      SELECT * FROM user_available where stripe_customer_id=$1
      `,
      [customer]
    );
    const origin = results.rows;
    if (origin.length == 0) {
      // insert
      const results = await db.query(
        `
        select * from stripe_customers where stripe_customer_id=$1
        `,
        [customer]
      );
      const customerData = results.rows;
      const data = customerData[0];
      if (data) {
        const user_id = data.user_id;
        const availableResults = await db.query(
          `
          select * from user_available where user_id=$1
          `,
          [user_id]
        );
        const availableData = availableResults.rows;
        if (availableData.length <= 0) {
          // insert
          await db.query(
            `
            insert into user_available(user_id,stripe_customer_id,available_times, download_times)
            values($1, $2, $3, $4)
            `,
            [user_id, customer, newTimes, newDownloadTimes]
          );
        } else {
          // update
          const existTimes = availableData[0].available_times;
          const resultTimes = existTimes + newTimes;
          const existDownloadTimes = availableData[0].download_times;
          let resultDownloadTimes: number;
          if (existDownloadTimes == -1) {
            resultDownloadTimes = -1;
          } else {
            if (newDownloadTimes == -1) {
              resultDownloadTimes = -1;
            } else {
              resultDownloadTimes = existDownloadTimes + newDownloadTimes;
            }
          }
          await db.query(
            `
            update user_available set available_times=$1,download_times=$2,stripe_customer_id=$3,updated_at=now() where user_id=$4
            `,
            [resultTimes, resultDownloadTimes, customer, user_id]
          );
        }
      }
    } else {
      // update
      const existTimes = origin[0].available_times;
      const resultTimes = existTimes + newTimes;
      const existDownloadTimes = origin[0].download_times;
      let resultDownloadTimes: number;
      if (existDownloadTimes == -1) {
        resultDownloadTimes = -1;
      } else {
        if (newDownloadTimes == -1) {
          resultDownloadTimes = -1;
        } else {
          resultDownloadTimes = existDownloadTimes + newDownloadTimes;
        }
      }
      await db.query(
        `
        update user_available set available_times=$1,download_times=$2,stripe_customer_id=$3,updated_at=now() where stripe_customer_id=$4
        `,
        [resultTimes, resultDownloadTimes, customer, customer]
      );
    }
  } else {
    // 不是支付完成
    console.log("pay not complete");
    console.log("checkoutSession-=-->", checkoutSession);
  }
};

const getDownloadTimesByCheckout = (checkoutSession) => {
  // 价格
  const amountTotal = checkoutSession.amount_total;
  let newTimes = 0;
  switch (amountTotal) {
    case pricesValue[0]:
      newTimes = downloadTimes[0];
      break;
    case pricesValue[1]:
      newTimes = downloadTimes[1];
      break;
    case pricesValue[2]:
      newTimes = downloadTimes[2];
      break;
    case pricesValue[3]:
      newTimes = downloadTimes[3];
      break;
    default:
      newTimes = downloadTimes[0];
  }
  return newTimes;
};

const getTimesByCheckout = (checkoutSession) => {
  // 价格
  const amountTotal = checkoutSession.amount_total;
  let newTimes = 0;
  switch (amountTotal) {
    case pricesValue[0]:
      newTimes = availableTimes[0];
      break;
    case pricesValue[1]:
      newTimes = availableTimes[1];
      break;
    case pricesValue[2]:
      newTimes = availableTimes[2];
      break;
    case pricesValue[3]:
      newTimes = availableTimes[3];
      break;
    default:
      newTimes = availableTimes[0];
  }
  return newTimes;
};

export const checkUserTimes = async (user_id) => {
  const results = await db.query(
    `
    select * from user_available where user_id=$1
    `,
    [user_id]
  );
  const result = results.rows;
  if (result.length <= 0) {
    return false;
  }
  const available = result[0];
  const available_times = available.available_times;
  return available_times > 0;
};

export const countDownUserTimes = async (user_id) => {
  const results = await db.query(
    `
    select * from user_available where user_id=$1
    `,
    [user_id]
  );
  const result = results.rows;
  if (result.length > 0) {
    const available = result[0];
    const resultTimes = available.available_times - 1;
    await db.query(
      `
      update user_available set available_times=$1,updated_at=now() where user_id=$2
      `,
      [resultTimes, user_id]
    );
  }
};

export const countDownUserTimesV2 = async (user_id, times) => {
  const result = await db.query(
    `
    update user_available set available_times = available_times - $2,updated_at=now() where user_id=$1
    `,
    [user_id, times]
  );
  console.log("countDownUserTimesV2-=-->", result);
  const { rows: resultList } = await db.query(
    `
    select * from user_available where user_id=$1
    `,
    [user_id]
  );
  if (resultList.length > 0) {
    const available = resultList[0];
    if (available.available_times < 0) {
      await db.query(`update user_available set available_times = 0 where user_id=$1`, [user_id]);
    }
  }
};

export const subscriptionComplete = async (subscription) => {
  let customer: string;
  if (subscription.customer) {
    customer = subscription.customer as string;
  } else {
    console.log("userPayComplete-error=-=->");
    console.log("subscription-=-=->", subscription);
  }
  const status = subscription.status;
  if (status == "active" || status == "trialing") {
    const priceId = subscription.plan.id;
    const subscriptionPrice = getSubscriptionPrice(priceId);
    const newTimes = subscriptionPrice.availableTimes;
    const newDownloadTimes = subscriptionPrice.downloadTimes;

    const results = await db.query(
      `
      SELECT * FROM user_available where stripe_customer_id=$1
      `,
      [customer]
    );
    const origin = results.rows;
    if (origin.length == 0) {
      const results = await db.query(
        `
        select * from stripe_customers where stripe_customer_id=$1
        `,
        [customer]
      );
      const customerData = results.rows;
      const data = customerData[0];
      if (data) {
        const user_id = data.user_id;
        const availableResults = await db.query(
          `
          select * from user_available where user_id=$1
          `,
          [user_id]
        );
        const availableData = availableResults.rows;
        if (availableData.length <= 0) {
          await db.query(
            `
            insert into user_available(user_id,stripe_customer_id,available_times, download_times) values($1, $2, $3, $4)
            `,
            [user_id, customer, newTimes, newDownloadTimes]
          );
        } else {
          await db.query(
            `
            update user_available set available_times=$2,download_times=$3,stripe_customer_id=$4,updated_at=now()
            where user_id=$1
            `,
            [user_id, newTimes, newDownloadTimes, customer]
          );
        }
      }
    } else {
      await db.query(
        `
        update user_available set available_times=$2,download_times=$3,updated_at=now() where stripe_customer_id=$1
        `,
        [customer, newTimes, newDownloadTimes]
      );
    }
  }

  if (status == "canceled") {
    const results = await db.query(
      `
      SELECT * FROM user_available where stripe_customer_id=$1
      `,
      [customer]
    );
    const origin = results.rows;
    if (origin.length > 0 && !origin[0].paddle_customer_id) {
      await db.query(
        `
        update user_available set available_times=$2,download_times=$3,updated_at=now() where stripe_customer_id=$1
        `,
        [customer, 0, 0]
      );
    }
  }
};

export const paddleSubscriptionComplete = async (subscription_price_id, subscription_status, user_id, paddle_customer_id) => {
  if (subscription_status == "active") {
    const subscriptionPrice = getPaddleSubscriptionPrice(subscription_price_id);
    const newTimes = subscriptionPrice.availableTimes;
    const newDownloadTimes = subscriptionPrice.downloadTimes;
    const { rows: origin } = await db.query(
      `
      SELECT * FROM user_available where user_id=$1
      `,
      [user_id]
    );
    if (origin.length == 0) {
      await db.query(
        `
        insert into user_available(user_id, paddle_customer_id, available_times, download_times) values($1, $2, $3, $4)
        `,
        [user_id, paddle_customer_id, newTimes, newDownloadTimes]
      );
    } else {
      await db.query(
        `
        update user_available set available_times=$2,download_times=$3,updated_at=now(),paddle_customer_id=$4 where user_id=$1
        `,
        [user_id, newTimes, newDownloadTimes, paddle_customer_id]
      );
    }
  }
  if (subscription_status == "canceled") {
    await db.query(
      `
      update user_available set available_times=$2,download_times=$3,updated_at=now() where user_id=$1
      `,
      [user_id, 0, 0]
    );
  }
};

// 根据指纹id判断今天用了几次
export const getNotLoginFreeTimesByFingerprint = async (fingerprint) => {
  // 今天
  const currentDate = new Date().toDateString();
  const headerAll = await headers();
  const country = headerAll.get("x-vercel-ip-country");
  if (isBlockCountry(country)) {
    return notLoginFreeUseTimes;
  }

  const pool = getDb();
  const client = await pool.connect();
  try {
    await client.query("BEGIN");
    // 未登录用户，指纹存储在 works 表的 fingerprint 字段
    const { rows: resultList } = await db.query(
      `
      SELECT * FROM works WHERE fingerprint=$1 AND status=1 AND created_at >= $2
      `,
      [fingerprint, currentDate]
    );
    await client.query("COMMIT");
    return resultList.length;
  } catch (e) {
    await client.query("ROLLBACK");
    console.error(e);
  } finally {
    client.release();
  }

  return 0;
};

export const payproOneTimeComplete = async ({ user_id, product_id, customer_id }) => {
  const price = getPriceByPayProPriceId(product_id);
  const newTimes = price.availableTimes;
  const newDownloadTimes = price.downloadTimes;
  const { rows: origin } = await db.query("SELECT * FROM user_available where user_id=$1", [user_id]);
  if (origin.length == 0) {
    await db.query("insert into user_available(user_id, available_times, download_times, paypro_customer_id) values($1, $2, $3, $4)", [user_id, newTimes, newDownloadTimes, customer_id]);
  } else {
    const existAvailableTimes = origin[0].available_times;
    const resultAvailableTimes = existAvailableTimes + newTimes;
    const existDownloadTimes = origin[0].download_times;
    let resultDownloadTimes: number;
    if (existDownloadTimes == -1) {
      resultDownloadTimes = -1;
    } else {
      if (newDownloadTimes == -1) {
        resultDownloadTimes = -1;
      } else {
        resultDownloadTimes = existDownloadTimes + newDownloadTimes;
      }
    }
    await db.query("update user_available set available_times=$2,download_times=$3,updated_at=now(),paypro_customer_id=$4 where user_id=$1", [
      user_id,
      resultAvailableTimes,
      resultDownloadTimes,
      customer_id,
    ]);
  }
};

// 检查并给予免费次数
export const checkAndGiveFreeTimes = async (user_info) => {
  if (user_info.stripe_customer_id || user_info.paddle_customer_id || user_info.paypro_customer_id) {
    return false;
  }

  const headerAll = await headers();
  // const userIp = headerAll.get("x-forwarded-for");
  const userIp = headerAll.get("cf-connecting-ip");
  const user_agent = headerAll.get("user-agent");
  // const country = headerAll.get("x-vercel-ip-country");
  const country = headerAll.get("cf-ipcountry");
  const cityOriginal = headerAll.get("x-vercel-ip-city");
  let city = cityOriginal;
  // URL解码city的字符串
  try {
    city = decodeURI(cityOriginal);
  } catch (e) {
    city = cityOriginal;
  }

  if (isBlockCountry(country)) {
    return false;
  }
  // 判断用户次数
  const existAvailableTimes = user_info.available_times;
  // 用户次数大于 0，则不给予免费次数
  if (existAvailableTimes > 0) {
    return false;
  }
  // 否则 判断用户是否签到

  const currentDate = new Date().toDateString();
  // console.log(currentDate);
  const { rows: resultList } = await db.query(
    `
    SELECT * FROM user_check_in_record WHERE user_id=$1 AND created_at >= $2
    `,
    [user_info.user_id, currentDate]
  );
  if (resultList.length > 0) {
    return false;
  }

  const freeTimes = freeGenerateTimes;

  // 签到
  await db.query(
    `
    INSERT INTO user_check_in_record (user_id, email, add_available_times, user_agent, ip, country, city) 
    VALUES ($1, $2, $3, $4, $5, $6, $7)
  `,
    [user_info.user_id, user_info.email, freeTimes, user_agent, userIp, country, city]
  );

  // 更新用户可用次数
  await db.query(
    `
    UPDATE user_available
    SET available_times = $1 
    WHERE user_id = $2
  `,
    [freeTimes, user_info.user_id]
  );
};
