import { getDb } from "~/libs/db/db";

const db = getDb();

// 检查是否付费的用户，stripe订阅/paddle订阅/付过费的用户会有 stripe_customer_id/paddle_customer_id
export const checkSubscribe = async (user_id) => {
  // 检查 stripe 订阅
  const { rows: resultList } = await db.query(
    `SELECT * FROM stripe_subscriptions where user_id=$1
    `,
    [user_id]
  );
  if (resultList.length > 0) {
    const status = resultList[0].status;
    if (status == "active" || status == "trialing") {
      return true;
    }
  }

  // 检查 paddle 订阅
  const { rows: paddleResult } = await db.query(
    `
    select * from paddle_user_payment_data where user_id=$1 order by created_at desc
    `,
    [user_id]
  );
  if (paddleResult.length > 0) {
    const subscription_status = paddleResult[0].subscription_status;
    if (subscription_status == "active" || subscription_status == "trialing") {
      return true;
    }
  }

  // 检查 是否付费用户
  const { rows: results } = await db.query(
    `
    select * from user_available where user_id=$1
    `,
    [user_id]
  );
  if (results.length > 0) {
    const stripe_customer_id = results[0].stripe_customer_id;
    const paddle_customer_id = results[0].paddle_customer_id;
    const paypro_customer_id = results[0].paypro_customer_id;
    if (stripe_customer_id || paddle_customer_id || paypro_customer_id) {
      return true;
    }
  }
  return false;
};

// 检查 stripe 订阅
export const checkStripeSubscribe = async (user_id) => {
  const { rows: resultList } = await db.query(
    `
    SELECT * FROM stripe_subscriptions where user_id=$1
    `,
    [user_id]
  );
  if (resultList.length > 0) {
    const status = resultList[0].status;
    if (status == "active" || status == "trialing") {
      return true;
    }
  }
  return false;
};

// 检查 paddle 订阅
export const checkPaddleSubscribe = async (user_id) => {
  const { rows: paddleResult } = await db.query(
    `
    select * from paddle_user_payment_data where user_id=$1 order by created_at desc
    `,
    [user_id]
  );
  if (paddleResult.length > 0) {
    const subscription_status = paddleResult[0].subscription_status;
    if (subscription_status == "active" || subscription_status == "trialing") {
      // 检查 next_billed_at 是否有值，没有值就是已经取消订阅了
      const next_billed_at = paddleResult[0].data?.next_billed_at;
      if (next_billed_at) {
        return {
          subscribe_status: true,
          next_billed_at: true,
        };
      } else {
        return {
          subscribe_status: true,
          next_billed_at: false,
        };
      }
    }
  }
  return {
    subscribe_status: false,
    next_billed_at: false,
  };
};

// 检查 paypro 付费
export const checkPayproSubscribe = async (user_id) => {
  const { rows: results } = await db.query(`select * from user_available where user_id=$1;`, [user_id]);
  if (results.length > 0) {
    const paypro_customer_id = results[0].paypro_customer_id;
    const stripe_customer_id = results[0].stripe_customer_id;
    const paddle_customer_id = results[0].paddle_customer_id;
    if (paypro_customer_id || stripe_customer_id || paddle_customer_id) {
      return {
        subscribe_status: true,
      };
    }
  }
};
