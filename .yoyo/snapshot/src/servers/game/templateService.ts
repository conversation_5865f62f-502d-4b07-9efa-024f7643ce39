import { getDb } from "~/libs/db/db";
import { Template } from "~/utils/types/game";
import { generateNewUID } from "~/utils/uidUtil";

// Get all templates
export async function getAllTemplates(): Promise<Template[]> {
  const db = getDb();

  try {
    const result = await db.query(`
      SELECT *
      FROM templates
      ORDER BY id DESC
    `);

    return result.rows;
  } catch (error) {
    console.error("Error fetching all templates:", error);
    throw error;
  }
}

// Get templates with pagination and filtering
export async function getTemplates(page: number = 1, pageSize: number = 10, name?: string): Promise<{ templates: Template[]; total: number }> {
  const db = getDb();
  const offset = (page - 1) * pageSize;

  try {
    let queryParams = [];
    let queryConditions = [];

    // Add name filter if provided
    if (name) {
      queryParams.push(`%${name}%`);
      queryConditions.push(`name ILIKE $${queryParams.length}`);
    }

    // Build the WHERE clause
    const whereClause = queryConditions.length > 0 ? `WHERE ${queryConditions.join(" AND ")}` : "";

    // Count total records
    const countQuery = `
      SELECT COUNT(*) as total
      FROM templates
      ${whereClause}
    `;

    const countResult = await db.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].total);

    // Get paginated results
    queryParams.push(pageSize);
    queryParams.push(offset);

    const templatesQuery = `
      SELECT *
      FROM templates
      ${whereClause}
      ORDER BY id DESC
      LIMIT $${queryParams.length - 1} OFFSET $${queryParams.length}
    `;

    const templatesResult = await db.query(templatesQuery, queryParams);

    return {
      templates: templatesResult.rows,
      total,
    };
  } catch (error) {
    console.error("Error fetching templates:", error);
    throw error;
  }
}

// Get template by ID - 保留但不推荐使用
export async function getTemplateById(id: number): Promise<Template | null> {
  const db = getDb();

  try {
    const result = await db.query(`SELECT * FROM templates WHERE id = $1`, [id]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error fetching template by ID:", error);
    throw error;
  }
}

// Get template by UID - 推荐使用
export async function getTemplateByUid(uid: string): Promise<Template | null> {
  const db = getDb();

  try {
    const result = await db.query(`
      SELECT * FROM templates 
      WHERE uid = $1
    `, [uid]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    console.error("Error fetching template by UID:", error);
    return null;
  }
}

// Create a new template
export async function createTemplate(templateData: { name: string; content: string; preview_url?: string | null }): Promise<Template> {
  const db = getDb();

  try {
    // 生成唯一的uid
    const uid = generateNewUID();
    
    const result = await db.query(
      `INSERT INTO templates(
        uid, name, content, preview_url
      ) VALUES($1, $2, $3, $4) RETURNING *`,
      [uid, templateData.name, templateData.content, templateData.preview_url || null]
    );

    return result.rows[0];
  } catch (error) {
    console.error("Error creating template:", error);
    throw error;
  }
}

// Update a template by ID - 保留但不推荐使用
export async function updateTemplate(
  id: number,
  templateData: {
    name?: string;
    content?: string;
    preview_url?: string | null;
  }
): Promise<Template | null> {
  const db = getDb();

  try {
    // First check if template exists
    const existingTemplate = await getTemplateById(id);

    if (!existingTemplate) {
      return null;
    }

    // Build the update fields
    const updates = [];
    const values = [];

    if (templateData.name !== undefined) {
      updates.push(`name = $${values.length + 1}`);
      values.push(templateData.name);
    }

    if (templateData.content !== undefined) {
      updates.push(`content = $${values.length + 1}`);
      values.push(templateData.content);
    }

    if (templateData.preview_url !== undefined) {
      updates.push(`preview_url = $${values.length + 1}`);
      values.push(templateData.preview_url);
    }

    // Always update the updated_at timestamp
    updates.push(`updated_at = CURRENT_TIMESTAMP`);

    // If no updates, return the existing template
    if (updates.length === 0) {
      return existingTemplate;
    }

    // Add the ID as the last parameter
    values.push(id);

    const result = await db.query(`UPDATE templates SET ${updates.join(", ")} WHERE id = $${values.length} RETURNING *`, values);

    return result.rows[0];
  } catch (error) {
    console.error("Error updating template:", error);
    throw error;
  }
}

// Update a template by UID - 推荐使用
export async function updateTemplateByUid(
  uid: string,
  templateData: {
    name?: string;
    content?: string;
    preview_url?: string | null;
  }
): Promise<Template | null> {
  const db = getDb();

  try {
    // First check if template exists
    const existingTemplate = await getTemplateByUid(uid);

    if (!existingTemplate) {
      return null;
    }

    // Build the update fields
    const updates = [];
    const values = [];

    if (templateData.name !== undefined) {
      updates.push(`name = $${values.length + 1}`);
      values.push(templateData.name);
    }

    if (templateData.content !== undefined) {
      updates.push(`content = $${values.length + 1}`);
      values.push(templateData.content);
    }

    if (templateData.preview_url !== undefined) {
      updates.push(`preview_url = $${values.length + 1}`);
      values.push(templateData.preview_url);
    }

    // Always update the updated_at timestamp
    updates.push(`updated_at = CURRENT_TIMESTAMP`);

    // If no updates, return the existing template
    if (updates.length === 0) {
      return existingTemplate;
    }

    // Add the UID as the last parameter
    values.push(uid);

    const result = await db.query(`UPDATE templates SET ${updates.join(", ")} WHERE uid = $${values.length} RETURNING *`, values);

    return result.rows[0];
  } catch (error) {
    console.error("Error updating template by UID:", error);
    throw error;
  }
}

// Delete a template by ID - 保留但不推荐使用
export async function deleteTemplate(id: number): Promise<boolean> {
  const db = getDb();

  try {
    // First check if template exists
    const existingTemplate = await getTemplateById(id);

    if (!existingTemplate) {
      return false;
    }

    await db.query(`DELETE FROM templates WHERE id = $1`, [id]);

    return true;
  } catch (error) {
    console.error("Error deleting template:", error);
    throw error;
  }
}

// Delete a template by UID - 推荐使用
export async function deleteTemplateByUid(uid: string): Promise<boolean> {
  const db = getDb();

  try {
    // First check if template exists
    const existingTemplate = await getTemplateByUid(uid);

    if (!existingTemplate) {
      return false;
    }

    await db.query(`DELETE FROM templates WHERE uid = $1`, [uid]);

    return true;
  } catch (error) {
    console.error("Error deleting template by UID:", error);
    throw error;
  }
}
