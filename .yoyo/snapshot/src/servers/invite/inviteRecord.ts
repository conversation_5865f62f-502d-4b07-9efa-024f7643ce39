import { getDb } from "~/libs/db/db";
import { getUserByServerSession } from "../common/user";
import { inviteAddTimes, inviteRegisterAddTimes, registerGenerateInviteAddTimes, shareFacebookAddTimes, shareTwitterAddTimes } from "~/configs/globalConfig";

const db = getDb();

// 获取邀请记录列表，根据当前用户信息获取
export const getInviteRecordList = async () => {
  const user_info = await getUserByServerSession();
  if (!user_info.user_id) {
    return [];
  }
  const user_id = user_info.user_id;
  const { rows: inviteRecordList } = await db.query(
    `
    SELECT ir.*, ui.name AS invite_user_name 
    FROM invite_record ir
    LEFT JOIN invite_code ic ON ic.code = ir.invite_code
    LEFT JOIN user_info ui ON ui.user_id = ir.invite_user_id
    WHERE ic.user_id = $1
      AND ir.is_delete = false
    ORDER BY ir.created_at DESC
    `,
    [user_id]
  );
  if (inviteRecordList.length > 0) {
    inviteRecordList.map((item) => {
      // 截取前边5位，并追加***
      item.invite_user_id = item.invite_user_id.slice(0, 5) + "***";
    });
  }
  return inviteRecordList;
};

// 传入邀请码，新增邀请记录
export const addInviteRecord = async (inviteCode, invite_user_info) => {
  const invite_user_id = invite_user_info.user_id;

  // 判断邀请码是不是当前用户的
  const { rows: inviteCodeList } = await db.query(
    `
    select * from invite_code where code = $1 and user_id = $2
    `,
    [inviteCode, invite_user_id]
  );
  if (inviteCodeList.length > 0) {
    // 邀请码是当前用户的, 不能邀请自己, 不记录
    return false;
  }

  // 判断邀请记录是否已存在
  const { rows: inviteRecordList } = await db.query(
    `
    select * from invite_record where invite_user_id = $1 and is_delete = false
    `,
    [invite_user_id]
  );
  if (inviteRecordList.length > 0) {
    // 邀请记录已存在, 不记录
    return false;
  }

  // 记录邀请记录
  await db.query(
    `
    insert into invite_record (invite_code, invite_user_id, register_add_times) values ($1, $2, $3)
    `,
    [inviteCode, invite_user_id, inviteAddTimes]
  );
  return true;
};

// 检查邀请码并增加可用次数
export const checkRegisterAndAddAvailableTimes = async (inviteCode, current_user_info) => {
  // 被邀请人 user_id
  const current_user_id = current_user_info.user_id;

  // 判断用户注册时间，如果注册时间在当前时间1分钟内，增加可用次数
  const existTime = new Date(current_user_info?.created_at).getTime();
  const currentTime = new Date().getTime();
  const resultTime = (currentTime - existTime) / 1000;
  if (resultTime >= 1 * 60) {
    // 注册时间超过1分钟，不增加可用次数
    return false;
  }

  // 判断并添加邀请记录
  const check = await addInviteRecord(inviteCode, current_user_info);
  if (!check) {
    return false;
  }

  // -=-=-=-=-=-=-=-=-=-=-=-=-=-=-=增加可用次数-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=

  // 根据邀请码查询邀请人信息
  const { rows: inviteCodeList } = await db.query(
    `
      select ic.*, ui.is_banned from invite_code ic
        left join user_info ui on ui.user_id = ic.user_id
      where ic.code = $1 and ic.is_delete = false;
    `,
    [inviteCode]
  );
  if (inviteCodeList.length <= 0) {
    // 邀请码不存在, 不增加可用次数
    return false;
  }
  // 邀请人 user_id
  const invite_user_id = inviteCodeList[0].user_id;
  const is_banned = inviteCodeList[0].is_banned;
  // 查询邀请人是否被封号
  if (!is_banned) {
    // 增加邀请人可用次数
    await db.query(
      `
      update user_available
        set updated_at = now(), available_times = available_times + $2
        where user_id = $1
      `,
      [invite_user_id, inviteAddTimes]
    );
  }

  // 增加被邀请人可用次数
  const currentAddTimes = inviteRegisterAddTimes;
  await db.query(
    `
    update user_available
      set updated_at = now(), available_times = available_times + $2
      where user_id = $1
    `,
    [current_user_id, currentAddTimes]
  );
  return true;
};

// 检查用户是否是受邀请的,并在首次生成视频时给邀请者增加可用次数
export const checkInvitedAndAddTimesForInviter = async (userId: string) => {
  // 检查用户是否是受邀请的
  const { rows: inviteRecords } = await db.query(
    `
    SELECT * FROM invite_record 
      WHERE invite_user_id = $1 AND used = false
    `,
    [userId]
  );

  if (inviteRecords.length === 0) {
    // 用户不是受邀请的或已经给邀请者增加过次数
    return false;
  }

  const inviteRecord = inviteRecords[0];
  const inviterUserId = inviteRecord.user_id;
  const inviteCode = inviteRecord.invite_code;
  // 查询 invite_code 表，获取邀请码对应的 user_id
  const { rows: inviteCodeRows } = await db.query(
    `
      select ic.*, ui.is_banned from invite_code ic
        left join user_info ui on ui.user_id = ic.user_id
        where ic.code = $1 and ic.is_delete = false;
    `,
    [inviteCode]
  );

  if (inviteCodeRows.length === 0) {
    // 邀请码不存在
    return false;
  }

  const inviterId = inviteCodeRows[0].user_id;
  const is_banned = inviteCodeRows[0].is_banned;
  const inviteAddTimes = registerGenerateInviteAddTimes;
  if (!is_banned) {
    // 增加邀请者的可用次数
    await db.query(
      `
      UPDATE user_available
        SET updated_at = NOW(), available_times = available_times + $2
        WHERE user_id = $1
      `,
      [inviterId, inviteAddTimes]
    );
  }

  // 更新invite_record表,标记已经给邀请者增加过次数
  await db.query(
    `
    UPDATE invite_record
      SET used_add_times = $2, updated_at = NOW(), used = true
      WHERE invite_user_id = $1
    `,
    [userId, inviteAddTimes]
  );

  return true;
};

// 分享到了twitter
export const addAvailableTimesAndRecordTwitter = async () => {
  // 被邀请人信息
  const current_user_info = await getUserByServerSession();
  if (!current_user_info.user_id) {
    return false;
  }

  // 判断用户是否被封号
  if (current_user_info?.is_banned) {
    return false;
  }

  const current_user_id = current_user_info.user_id;

  // 判断用户此前是否分享过
  const { rows: inviteCodeList } = await db.query(
    `
      select * from invite_code where user_id = $1 and invited_twitter = true
    `,
    [current_user_id]
  );
  if (inviteCodeList.length > 0) {
    // 用户此前已分享过
    return {
      code: 400,
      message: "You have already shared to twitter",
    };
  }

  // 增加可用次数
  const addTimes = shareTwitterAddTimes;
  await db.query(
    `
      update user_available set available_times = available_times + $1 where user_id = $2
    `,
    [addTimes, current_user_id]
  );

  // 记录分享到twitter
  await db.query(
    `
      update invite_code set invited_twitter = true where user_id = $1
    `,
    [current_user_id]
  );
  return true;
};

// 分享到了 facebook
export const addAvailableTimesAndRecordFacebook = async () => {
  // 被邀请人信息
  const current_user_info = await getUserByServerSession();
  if (!current_user_info.user_id) {
    return false;
  }

  // 判断用户是否被封号
  if (current_user_info?.is_banned) {
    return false;
  }

  const current_user_id = current_user_info.user_id;

  // 判断用户此前是否分享过
  const { rows: inviteCodeList } = await db.query(
    `
      select * from invite_code where user_id = $1 and invited_facebook = true
    `,
    [current_user_id]
  );
  if (inviteCodeList.length > 0) {
    // 用户此前已分享过
    return {
      code: 400,
      message: "You have already shared to facebook",
    };
  }

  // 增加可用次数
  const addTimes = shareFacebookAddTimes;
  await db.query(
    `
      update user_available set available_times = available_times + $1 where user_id = $2
    `,
    [addTimes, current_user_id]
  );

  // 记录分享到facebook
  await db.query(
    `
      update invite_code set invited_facebook = true where user_id = $1
    `,
    [current_user_id]
  );
  return true;
};
