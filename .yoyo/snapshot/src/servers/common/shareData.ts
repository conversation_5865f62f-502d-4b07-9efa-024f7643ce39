import { shareDataDomainName, useShareDatabase } from "~/configs/globalConfig";
import { getShareDb } from "~/libs/db/shareDataDB";

const shareDb = getShareDb();

// 获取 footer 展示的链接列表
export const getFooterLink = async () => {
  const domain = shareDataDomainName;

  if (!useShareDatabase) {
    return [];
  }

  const { rows } = await shareDb.query(
    `
    select * from footer_link
      where domain=$1 and is_show=$2 and is_delete=$3
      order by updated_at asc
  `,
    [domain, true, false]
  );
  if (rows.length > 0) {
    return rows;
  }
  return [];
};
