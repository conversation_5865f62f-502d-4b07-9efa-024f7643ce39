/* General body and layout styles */
body {
    font-family: 'Arial', sans-serif; /* Example font */
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f3f4f6; /* Tailwind gray-100 */
    color: #1f2937; /* Tailwind gray-800 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .container {
    max-width: 1280px; /* Max width similar to max-w-screen-xl */
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  
  /* Page Title (H1) styles */
  .page-h1 {
    font-size: 2.5rem; /* Similar to text-4xl md:text-5xl */
    font-weight: bold;
    text-align: center;
    margin-bottom: 3rem; /* Similar to mb-12 */
    color: #111827; /* Tailwind gray-900 */
  }
  
  /* Section styles */
  .section {
    margin-bottom: 3rem; /* Similar to mb-12 */
    padding: 1.5rem; /* Similar to p-6 */
    background-color: #ffffff; /* Tailwind bg-white */
    border-radius: 0.5rem; /* Similar to rounded-lg */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1); /* Tailwind shadow-md */
  }
  
  /* Section H2 styles */
  .section-h2 {
    font-size: 1.875rem; /* Similar to text-3xl */
    font-weight: bold;
    margin-bottom: 1.5rem; /* Similar to mb-6 */
    color: #1f2937; /* Tailwind gray-900 */
    text-align: center; /* Assuming h2 is centered based on previous template */
  }
  
  /* General section content styles */
  .section-content {
    line-height: 1.6; /* Similar to leading-relaxed */
    color: #4b5563; /* Tailwind gray-600 */
  }
  
  /* Specific section content styles if needed (e.g., grid layout) */
  .features-content,
  .how-to-play-content,
  .tips-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem; /* Similar to gap-8 */
  }
  
  @media (min-width: 768px) { /* md */
    .features-content,
    .how-to-play-content,
    .tips-content {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  /* Add more responsive breakpoints if needed */
  
  
  /* Styles for list items generated by handleSection */
  .section-item {
    margin-bottom: 2rem; /* Similar to mb-8 */
    padding: 1.5rem; /* Similar to p-6 */
    background-color: #ffffff; /* Tailwind bg-white */
    border-radius: 0.5rem; /* Similar to rounded-lg */
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* Tailwind shadow-sm */
    transition: box-shadow 0.3s ease-in-out; /* Similar to transition-shadow */
  }
  
  .section-item:hover {
     box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1); /* Similar to hover:shadow-md */
  }
  
  .section-item-h3 {
    font-size: 1.25rem; /* Similar to text-xl */
    font-weight: 600; /* Similar to font-semibold */
    margin-bottom: 0.75rem; /* Similar to mb-3 */
    color: #1f2937; /* Tailwind gray-800 */
  }
  
  .section-item-content {
    color: #4b5563; /* Tailwind gray-600 */
    line-height: 1.6; /* Similar to leading-relaxed */
  }
  
  /* Styles for simple paragraphs generated by handleSection */
  .section-paragraph {
     margin-bottom: 1rem; /* Similar to mb-4 */
     padding: 1rem; /* Similar to p-4 */
     background-color: #ffffff; /* Tailwind bg-white */
     border-radius: 0.25rem; /* Similar to rounded-lg */
     box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* Tailwind shadow-sm */
  }
  
  .section-paragraph-content {
     color: #4b5563; /* Tailwind gray-600 */
  }
  
  
  /* Iframe styles */
  .game-iframe-element {
    width: 100%;
    max-width: 1024px; /* Similar to max-w-screen-lg */
    margin-left: auto;
    margin-right: auto;
    aspect-ratio: 16 / 9; /* Similar to aspect-video */
    border-radius: 0.5rem; /* Similar to rounded-lg */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1); /* Similar to shadow-xl */
    min-height: 600px; /* Similar to min-h-[600px] */
    border: none; /* Assuming no border from frameborder="0" */
    display: block; /* Ensure it takes up block space */
  }
  
  /* CTA styles */
  .cta-section {
    text-align: center;
    padding-top: 3rem; /* Similar to py-8, adjust as needed */
    padding-bottom: 3rem;
    background-color: white; /* Example background */
    color: white;
  }
  
  .cta-section .section-h2 {
    color: #1f2937; /* H2 color override for dark background */
  }
  
  .cta-content-text {
    font-size: 1.125rem; /* Similar to text-lg */
    margin-bottom: 2rem; /* Similar to mb-8 */
  }
  
  .cta-button-container {
    margin-top: 2rem; /* Space above button */
  }
  
  .cta-button {
    display: inline-block;
    background-color: #3b82f6; /* Tailwind blue-600 */
    color: white;
    padding: 1rem 2rem; /* Similar to px-8 py-4 */
    border-radius: 0.5rem; /* Similar to rounded-lg */
    font-size: 1.125rem; /* Similar to text-lg */
    font-weight: 600; /* Similar to font-semibold */
    text-decoration: none; /* Remove underline from link */
    transition: all 0.2s ease-in-out; /* Similar to transform hover:scale-105 transition-all duration-200 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1); /* Similar to shadow-lg */
  }
  
  .cta-button:hover {
    background-color: #2563eb; /* Tailwind hover:bg-blue-700 */
    transform: scale(1.05); /* Similar to hover:scale-105 */
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1); /* Similar to hover:shadow-xl */
  }
  
  /* Add styles for share buttons and comments section if needed */
  
  /* Basic responsive adjustments */
  @media (min-width: 640px) { /* sm */
    .container {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }
  
  @media (min-width: 768px) { /* md */
    .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
    .section-h2 {
      font-size: 2.25rem; /* Adjust h2 size for medium screens */
    }
  }
  
  @media (min-width: 1024px) { /* lg */
    .container {
      padding-left: 3rem;
      padding-right: 3rem;
    }
    .page-h1 {
      font-size: 3rem; /* Adjust h1 size for large screens */
    }
  }

  /* 推荐区域整体容器 */
  .recommended-games-section {
    margin-top: 20px; /* 或其他合适的间距 */
    margin-bottom: 20px;
  }

  /* 每个推荐区域的容器 */
  .recommendation-zone {
    margin-bottom: 20px; /* 每个推荐区域之间的间距 */
  }

  /* 推荐区域的标题 (区域名称) */
  .recommendation-zone-title {
    font-size: 1.5em; /* 或其他合适的字体大小 */
    margin-bottom: 10px;
    border-bottom: 1px solid #eee; /* 可选的下划线 */
    padding-bottom: 5px;
  }

  /* 单个推荐区域内游戏列表的容器 */
  .recommendation-game-list {
    display: flex; /* 使用 flexbox 或 grid 实现横向排列 */
    overflow-x: auto; /* 如果游戏太多，可以考虑横向滚动 */
    gap: 15px; /* 游戏卡片之间的间距 */
    padding-bottom: 10px; /* 避免阴影或边框被裁切 */
  }

  /* 单个游戏推荐卡片 */
  .recommended-game-item {
    flex: 0 0 auto; /* 不放大，不缩小，基础宽度由内容决定 */
    width: 150px; /* 或其他合适的卡片宽度 */
    text-align: center;
    text-decoration: none; /* 移除链接下划线 */
    color: inherit; /* 继承父元素颜色 */
    transition: transform 0.2s ease-in-out; /* 鼠标悬停效果 */
  }

  .recommended-game-item:hover {
    transform: translateY(-5px); /* 鼠标悬停时微小上移 */
  }

  /* 游戏封面图片 */
  .recommended-game-cover {
    width: 100%; /* 图片宽度占满卡片 */
    height: 100px; /* 或固定高度，确保图片大小一致 */
    object-fit: cover; /* 保持图片比例并填充 */
    border-radius: 8px; /* 圆角 */
    margin-bottom: 5px;
  }

  /* 游戏标题 */
  .recommended-game-title {
    font-size: 0.9em; /* 或其他合适的字体大小 */
    white-space: nowrap; /* 标题不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    margin: 0; /* 移除默认边距 */
  }