-- 评论表
create table comments
(
    id                  bigint generated by default as identity
        primary key,
    created_at          timestamp with time zone default now() not null,
    updated_at          timestamp with time zone default now() not null,
    uid                 varchar not null,
    page_uid            varchar,
    parent_comment_uid  varchar,
    user_id             varchar not null,
    content             text not null,
    is_delete           boolean                  default false not null,
    is_approved         boolean                  default true not null,
    user_name           varchar,
    user_image          varchar,
    reply_count         integer                  default 0 not null
);

comment on table comments is '评论表';

comment on column comments.id is '自增ID';
comment on column comments.created_at is '创建时间';
comment on column comments.updated_at is '更新时间';
comment on column comments.uid is '评论唯一标识符';
comment on column comments.page_uid is '关联页面ID，对应pages表的uid字段';
comment on column comments.parent_comment_uid is '父评论ID，用于回复功能，为空表示顶级评论';
comment on column comments.user_id is '用户ID，对应user_info表的user_id字段';
comment on column comments.content is '评论内容';
comment on column comments.is_delete is '是否删除，默认false';
comment on column comments.is_approved is '是否审核通过，默认true';
comment on column comments.user_name is '用户姓名缓存，提高查询性能';
comment on column comments.user_image is '用户头像缓存，提高查询性能';
comment on column comments.reply_count is '回复数量缓存，提高查询性能';

-- 创建索引，遵循现有索引命名规范
create index comments_uid_index
    on comments (uid);

create index comments_page_uid_index
    on comments (page_uid);

create index comments_created_at_index
    on comments (created_at desc);

create index comments_page_uid_created_at_index
    on comments (page_uid, created_at desc);

create index comments_parent_comment_uid_index
    on comments (parent_comment_uid);

create index comments_user_id_index
    on comments (user_id);

create index comments_page_uid_is_delete_index
    on comments (page_uid, is_delete);

create index comments_page_uid_is_delete_created_at_index
    on comments (page_uid, is_delete, created_at desc);

create index comments_user_id_created_at_index
    on comments (user_id, created_at desc);

-- 为了支持审核功能的索引
create index comments_is_approved_created_at_index
    on comments (is_approved, created_at desc);
