-- auto-generated definition
create table games
(
    id             bigint generated by default as identity
        primary key,
    created_at     timestamp with time zone default now() not null,
    updated_at     timestamp with time zone default now() not null,
    uid            varchar,
    keyword        varchar,
    slug           varchar,
    iframe_url     varchar,
    reference_data text,
    tags           json,
    is_delete      boolean                  default false
);

comment on table games is 'games table';

comment on column games.id is 'sequence id';

comment on column games.created_at is 'create time';

comment on column games.updated_at is 'update time';

comment on column games.uid is 'game uuid，需要代码中生成';

comment on column games.keyword is '游戏关键词';

comment on column games.slug is '游戏URL专用slug，唯一且可编辑';

comment on column games.iframe_url is '游戏iframe地址';

comment on column games.reference_data is '参考资料，用于LLM生成';

comment on column games.tags is '(JSON数组, e.g., ["action", "rpg"])，便于分类和筛选';

create index games_uid_index
    on games (uid);

create index games_uid_is_delete_index
    on games (uid, is_delete);

create index games_keyword_index
    on games (keyword);

create index games_created_at_index
    on games (created_at desc);

create unique index games_slug_unique
    on games (slug);
    