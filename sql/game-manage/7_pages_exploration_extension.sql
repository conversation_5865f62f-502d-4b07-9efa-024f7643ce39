-- 扩展 pages 表以支持发现页面功能

BEGIN;

-- 添加发现页面标识和排序字段
ALTER TABLE pages ADD COLUMN IF NOT EXISTS is_exploration_page BOOLEAN DEFAULT false;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS exploration_sort_by VARCHAR DEFAULT 'newest';

-- 添加字段注释
COMMENT ON COLUMN pages.is_exploration_page IS '是否为发现页面';
COMMENT ON COLUMN pages.exploration_sort_by IS '发现页面默认排序方式: newest, popular';

-- 创建发现页面相关索引
CREATE INDEX IF NOT EXISTS idx_pages_exploration
    ON pages (domain, is_exploration_page, status)
    WHERE is_exploration_page = true;

COMMIT;
