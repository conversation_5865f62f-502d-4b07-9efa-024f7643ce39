-- auto-generated definition
create table sites
(
    id                     bigint generated by default as identity
        primary key,
    created_at             timestamp with time zone default now() not null,
    updated_at             timestamp with time zone default now() not null,
    uid                    varchar,
    name                   varchar,
    domain                 varchar
        constraint unique_domain
            unique,
    config                 json,
    default_template_key   varchar,
    default_llm_model      varchar,
    default_llm_prompt_key varchar
);

comment on table sites is 'sites table';

comment on column sites.id is 'sequence id';

comment on column sites.created_at is 'create time';

comment on column sites.updated_at is 'update time';

comment on column sites.uid is 'site uuid，需要代码中生成';

comment on column sites.name is '站点名称';

comment on column sites.domain is '站点域名';

comment on column sites.config is '站点配置';

comment on column sites.default_template_key is '默认模板KEY';

comment on column sites.default_llm_model is '默认LLM模型标识';

comment on column sites.default_llm_prompt_key is '默认Prompt标识';

create index sites_uid_index
    on sites (uid);

create index sites_created_at_index
    on sites (created_at desc);

