-- auto-generated definition
create table pages
(
    id                  bigint generated by default as identity
        primary key,
    created_at          timestamp with time zone default now() not null,
    updated_at          timestamp with time zone default now() not null,
    uid                 varchar,
    game_uid            varchar,
    site_uid            varchar,
    template_key        varchar,
    title               varchar,
    slug                varchar,
    json_content        json,
    json_schema_version varchar,
    llm_model_used      varchar,
    llm_prompt_key_used varchar,
    iframe_url          varchar,
    public_url          varchar,
    status              integer                  default 0,
    domain              varchar,
    is_homepage         boolean                  default false
);

comment on table pages is 'pages table';

comment on column pages.id is 'sequence id';

comment on column pages.created_at is 'create time';

comment on column pages.updated_at is 'update time';

comment on column pages.uid is 'page uuid，需要代码中生成';

comment on column pages.game_uid is '关联游戏ID';

comment on column pages.site_uid is '关联站点ID';

comment on column pages.template_key is '使用的模板KEY';

comment on column pages.title is '页面标题';

comment on column pages.slug is '页面路径片段';

comment on column pages.json_content is '生成的文案';

comment on column pages.json_schema_version is 'JSON结构版本号';

comment on column pages.llm_model_used is '生成时使用的LLM模型标识';

comment on column pages.llm_prompt_key_used is '生成时使用的Prompt标识';

comment on column pages.iframe_url is '当前页面的游戏iframe地址';

comment on column pages.public_url is '最终发布的完整URL';

comment on column pages.status is '状态';

create index idx_pages_domain_homepage
    on pages (domain, is_homepage);

create unique index unique_homepage_per_domain
    on pages (domain)
    where (is_homepage = true);

create index pages_domain_index
    on pages (domain);

create index pages_domain_status_index
    on pages (domain, status);

