-- auto-generated definition
create table templates
(
    id          bigint generated by default as identity
        primary key,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    uid         varchar,
    name        varchar,
    content     text,
    preview_url varchar
);

comment on table templates is 'templates table';

comment on column templates.id is 'sequence id';

comment on column templates.created_at is 'create time';

comment on column templates.updated_at is 'update time';

comment on column templates.uid is 'template uuid，需要代码中生成';

comment on column templates.name is '模板名称';

comment on column templates.content is '模板内容';

comment on column templates.preview_url is '预览图URL';

create index templates_uid_index
    on templates (uid);

