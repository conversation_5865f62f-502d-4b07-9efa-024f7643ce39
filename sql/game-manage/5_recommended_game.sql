-- auto-generated definition
create table recommended_games
(
    id                  bigint generated by default as identity
        primary key,
    created_at          timestamp with time zone default now() not null,
    updated_at          timestamp with time zone default now() not null,
    uid                 varchar,
    site_uid            varchar,
    page_uid            varchar,
    recommendation_zone varchar,
    title               varchar,
    description         varchar,
    cover_img_url       varchar,
    jump_url            varchar,
    sort_order          integer
);

comment on table recommended_games is 'recommended_games table';

comment on column recommended_games.id is 'sequence id';

comment on column recommended_games.created_at is 'create time';

comment on column recommended_games.updated_at is 'update time';

comment on column recommended_games.uid is 'recommended_game uuid，需要代码中生成';

comment on column recommended_games.site_uid is '所属站点ID';

comment on column recommended_games.page_uid is '所属落地页ID';

comment on column recommended_games.recommendation_zone is '推荐区标识';

comment on column recommended_games.title is '游戏标题';

comment on column recommended_games.description is '简要描述';

comment on column recommended_games.cover_img_url is '推荐卡片封面图';

comment on column recommended_games.jump_url is '跳转目标url';

comment on column recommended_games.sort_order is '排序权重';

create index recommended_games_created_at_index
    on recommended_games (created_at desc);

create index recommended_games_site_uid_index
    on recommended_games (site_uid);

create index recommended_games_site_uid_sort_order_index
    on recommended_games (site_uid, sort_order);

