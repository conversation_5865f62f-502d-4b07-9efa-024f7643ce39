create table landing_section
(
    id                  bigint generated by default as identity
        primary key,
    created_at          timestamp with time zone default now() not null,
    updated_at          timestamp with time zone default now() not null,
    base_id             varchar,
    language            varchar                  default 'en'::character varying,
    section_url         varchar,
    section_name        varchar,
    section_title       varchar,
    section_description varchar,
    section_intro       varchar,
    section_icon        varchar,
    section_css         varchar,
    section_prompt      varchar,
    page_prompt         varchar,
    section_name_zh     varchar,
    create_user_id      varchar,
    status              integer                  default 0     not null,
    is_delete           boolean                  default false not null,
    is_origin           boolean                  default true  not null,
    section_type        varchar,
    section_sort        integer,
    section_json        json,
    styles              json
);


create index landing_section_status_index
    on landing_section (status desc);

create index landing_section_status_section_sort_index
    on landing_section (status desc, section_sort asc);

create index landing_section_status_updated_at_index
    on landing_section (status desc, updated_at desc);

create index landing_section_section_name_index
    on landing_section (section_name);

create index landing_section_section_name_is_delete_index
    on landing_section (section_name, is_delete);

