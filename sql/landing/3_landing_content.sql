create table landing_content
(
    id               bigint generated by default as identity
        primary key,
    created_at       timestamp with time zone default now() not null,
    updated_at       timestamp with time zone default now() not null,
    language         varchar                  default 'en'::character varying,
    page_base_id     varchar,
    page_url         varchar,
    section_base_id  varchar,
    section_url      varchar,
    section_name     varchar,
    content_base_id  varchar,
    content_markdown varchar,
    content_html     varchar,
    content_json     jsonb,
    content_type     varchar,
    content_sort     integer,
    create_user_id   varchar,
    status           integer                  default 0     not null,
    is_delete        boolean                  default false not null,
    is_origin        boolean                  default true  not null,
    content_styles   json
);


create index landing_content_page_url_content_sort_index
    on landing_content (page_url, content_sort);

create index landing_content_page_url_status_index
    on landing_content (page_url asc, status desc);

create index landing_content_page_url_status_is_delete_index
    on landing_content (page_url asc, status desc, is_delete asc);

create index landing_content_content_base_id_index
    on landing_content (content_base_id);

create index landing_content_content_sort_index
    on landing_content (content_sort);

create index landing_content_page_url_is_delete_index
    on landing_content (page_url, is_delete);

