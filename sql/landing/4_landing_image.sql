create table landing_image
(
    id             bigint generated by default as identity
        primary key,
    uid            varchar                                not null,
    created_at     timestamp with time zone default now() not null,
    updated_at     timestamp with time zone default now() not null,
    image_url      varchar                                not null,
    image_type     varchar                                not null,
    image_name     varchar,
    create_user_id varchar,
    is_delete      boolean                  default false not null
);

comment on column landing_image.image_type is 'icon 或 painting';

create index landing_image_created_at_is_delete_index
    on landing_image (created_at desc, is_delete asc);

create index landing_image_image_type_index
    on landing_image (image_type);

create index landing_image_image_type_is_delete_index
    on landing_image (image_type, is_delete);

create index landing_image_image_name_index
    on landing_image (image_name);

