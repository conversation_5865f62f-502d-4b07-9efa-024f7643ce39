create table landing_page
(
    id                 bigint generated by default as identity
        primary key,
    created_at         timestamp with time zone default now() not null,
    updated_at         timestamp with time zone default now() not null,
    base_id            varchar,
    language           varchar                  default 'en'::character varying,
    page_url           varchar,
    page_name          varchar,
    page_title         varchar,
    page_description   varchar,
    page_intro         varchar,
    page_icon          varchar,
    page_logo          varchar,
    page_banner        varchar,
    out_link_url       varchar,
    create_user_id     varchar,
    status             integer                  default 0     not null,
    is_delete          boolean                  default false not null,
    is_origin          boolean                  default true  not null,
    show_in_index_page boolean                  default false not null,
    module_type        varchar
);
comment on column landing_page.module_type is '所属模块类型';

create index landing_page_created_at_is_delete_status_index
    on landing_page (created_at desc, is_delete asc, status asc);

create index landing_page_module_type_index
    on landing_page (module_type);

create index landing_page_page_url_index
    on landing_page (page_url);

create index landing_page_page_url_is_delete_index
    on landing_page (page_url, is_delete);

create index landing_page_show_in_index_page_index
    on landing_page (show_in_index_page desc);

