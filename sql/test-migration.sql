-- 测试环境数据库扩展脚本

BEGIN;

-- 1. 扩展 pages 表
ALTER TABLE pages ADD COLUMN IF NOT EXISTS play_count INTEGER DEFAULT 0;
ALTER TABLE pages ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0;

-- 2. 创建性能索引
CREATE INDEX IF NOT EXISTS idx_pages_popularity 
    ON pages (site_uid, status, play_count DESC, comment_count DESC);
CREATE INDEX IF NOT EXISTS idx_pages_latest 
    ON pages (site_uid, status, created_at DESC);

-- 为热度排序优化的复合索引
CREATE INDEX IF NOT EXISTS idx_pages_stats_lookup 
ON pages (site_uid, slug, status) 
INCLUDE (play_count, comment_count);

-- 为推荐游戏查询优化的索引
CREATE INDEX IF NOT EXISTS idx_recommended_games_site_sort 
ON recommended_games (site_uid, created_at DESC);

-- 支持热度评分计算的索引
CREATE INDEX IF NOT EXISTS idx_pages_popularity_calc
ON pages (site_uid, status) 
INCLUDE (play_count, comment_count, slug)
WHERE status = 1;

-- 支持JOIN操作的索引
CREATE INDEX IF NOT EXISTS idx_pages_slug_lookup
ON pages (site_uid, slug, status)
WHERE status = 1;

-- 推荐游戏URL提取优化索引
CREATE INDEX IF NOT EXISTS idx_recommended_games_url_pattern
ON recommended_games (site_uid, jump_url)
WHERE jump_url LIKE '%/game/%';

COMMIT;
