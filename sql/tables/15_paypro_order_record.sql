-- auto-generated definition
create table paypro_order_record
(
    id                          bigint generated by default as identity
        primary key,
    order_id                    varchar,
    order_status                varchar,
    created_at                  timestamp with time zone default now() not null,
    customer_id                 varchar,
    user_id                     varchar,
    customer_email              varchar,
    product_id                  varchar,
    data_all                    jsonb,
    customer_country_code       varchar,
    customer_country_code_by_ip varchar,
    product_price               bigint
);

comment on table paypro_order_record is 'paypro订单记录表';

comment on column paypro_order_record.id is '自增id';

comment on column paypro_order_record.order_id is '订单id';

comment on column paypro_order_record.order_status is '订单状态';

comment on column paypro_order_record.created_at is '创建时间';

comment on column paypro_order_record.customer_id is '客户id';

comment on column paypro_order_record.user_id is '用户id';

comment on column paypro_order_record.customer_email is '客户邮箱';

comment on column paypro_order_record.product_id is '产品 id';

comment on column paypro_order_record.data_all is 'webhook所有数据';


create index paypro_order_record_customer_id_index
    on paypro_order_record (customer_id);

create index paypro_order_record_order_id_created_at_index
    on paypro_order_record (order_id asc, created_at desc);

create index paypro_order_record_order_id_index
    on paypro_order_record (order_id);

create index paypro_order_record_user_id_index
    on paypro_order_record (user_id);

