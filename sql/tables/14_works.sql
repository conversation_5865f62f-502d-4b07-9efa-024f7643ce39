create table works
(
    id          bigint generated by default as identity
        primary key,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    uid         varchar,
    is_public   boolean                  default false,
    status      integer,
    user_id     varchar,
    is_delete   boolean                  default false,
    input_data  jsonb,
    output_data jsonb,
    result_data jsonb,
    user_agent  varchar,
    generator   varchar,
    task_id     varchar,
    fingerprint varchar
);

comment on table works is 'works';

comment on column works.id is 'sequence id';

comment on column works.created_at is 'create time';

comment on column works.updated_at is 'update time';

comment on column works.uid is 'uid';

comment on column works.is_public is 'is_public';

comment on column works.status is 'status';

comment on column works.user_id is 'user_id';

comment on column works.input_data is '入参数据';

comment on column works.output_data is '收到的结果数据';

comment on column works.result_data is '处理后的结果数据';

comment on column works.user_agent is 'user_agent';

comment on column works.task_id is '任务id';

comment on column works.fingerprint is '指纹id';

create index works_uid_index
    on works (uid);

create index works_updated_at_index
    on works (updated_at desc);

create index works_user_id_created_at_index
    on works (user_id asc, created_at desc);

create index works_user_id_is_delete_index
    on works (user_id, is_delete);

