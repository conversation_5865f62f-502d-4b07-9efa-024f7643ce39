create table paddle_webhook_record
(
    created_at      timestamp with time zone default now() not null,
    event_id        varchar,
    event_type      varchar,
    occurred_at     timestamp with time zone,
    notification_id varchar,
    id              varchar,
    status          varchar,
    customer_id     varchar,
    user_id         varchar,
    data            jsonb,
    origin          varchar
);

comment on column paddle_webhook_record.origin is '已知的有（ api：新订阅；subscription_recurring：续订';

create index paddle_webhook_record_user_id_index
    on paddle_webhook_record (user_id);

create index paddle_webhook_record_customer_id_index
    on paddle_webhook_record (customer_id);

