create table stripe_order_record
(
    id                 bigint generated by default as identity
        primary key,
    created_at         timestamp with time zone default now() not null,
    stripe_customer_id varchar,
    checkout_session   text,
    status         varchar,
    card_address_country varchar
);

comment on table stripe_order_record is '支付完成的记录表';

comment on column stripe_order_record.id is '自增id';

comment on column stripe_order_record.created_at is '创建时间';

comment on column stripe_order_record.stripe_customer_id is 'stripe用户id';

comment on column stripe_order_record.checkout_session is '支付完成记录';


create index stripe_order_record_stripe_customer_id_index
    on stripe_order_record (stripe_customer_id);

