create table invite_code
(
    id               bigint generated by default as identity
        primary key,
    user_id          varchar,
    code             varchar,
    created_at       timestamp with time zone default now() not null,
    updated_at       timestamp with time zone default now() not null,
    is_delete        boolean                  default false not null,
    invited_twitter  boolean                  default false not null,
    invited_facebook boolean                  default false not null
);

comment on table invite_code is '邀请码记录表';

comment on column invite_code.user_id is '邀请用户id';

comment on column invite_code.code is '邀请码';

comment on column invite_code.created_at is '创建时间';

comment on column invite_code.updated_at is '更新时间';

comment on column invite_code.is_delete is '是否删除，默认不删除';

comment on column invite_code.invited_twitter is '是否邀请twitter';

comment on column invite_code.invited_facebook is '是否邀请facebook';


create index invite_code_code_index
    on invite_code (code);

create index invite_code_user_id_index
    on invite_code (user_id);

create index invite_code_user_id_is_delete_index
    on invite_code (user_id, is_delete);

