create table user_info
(
    id            bigint generated by default as identity
        primary key,
    created_at    timestamp with time zone default now() not null,
    updated_at    timestamp with time zone default now() not null,
    user_id       varchar,
    name          varchar,
    email         varchar,
    image         varchar,
    last_login_ip varchar,
    login_from    varchar,
    user_agent    varchar,
    country       varchar,
    city          varchar,
    is_delete     boolean                  default false not null,
    role          varchar                  default 'user'::character varying,
    is_banned     boolean                  default false not null,
    register_client_type   varchar                  default 'web'::character varying,
    last_login_client_type varchar                  default 'web'::character varying,
    utm_source varchar,
    utm_campaign varchar,
    url_search_params varchar
);

comment on table user_info is 'user info table';

comment on column user_info.id is 'sequence id';

comment on column user_info.created_at is 'create time';

comment on column user_info.updated_at is 'update time';

comment on column user_info.user_id is 'user uuid';

comment on column user_info.name is 'user name';

comment on column user_info.email is 'user email';

comment on column user_info.image is 'user avatar path';

comment on column user_info.last_login_ip is 'user last login ip';

comment on column user_info.login_from is 'login_from';

comment on column user_info.user_agent is 'user_agent';

comment on column user_info.country is 'country code';

comment on column user_info.city is 'city';

comment on column user_info.is_delete is '是否删除';

comment on column user_info.role is '用户角色（user：普通用户，admin：超级管理员）';

comment on column user_info.is_banned is '是否封号';


create index user_info_user_id_index
    on user_info (user_id);

create index user_info_email_index
    on user_info (email);

