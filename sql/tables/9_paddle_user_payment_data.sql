create table paddle_user_payment_data
(
    id                      bigint generated by default as identity
        primary key,
    created_at              timestamp with time zone default now() not null,
    updated_at              timestamp with time zone default now() not null,
    event_id                varchar,
    event_type              varchar,
    data                    jsonb,
    subscription_id         varchar,
    subscription_price_id   varchar,
    subscription_status     varchar,
    subscription_start_date timestamp with time zone,
    subscription_end_date   timestamp with time zone,
    customer_id             varchar,
    user_id                 varchar,
    data_all                jsonb
);

comment on table paddle_user_payment_data is 'paddle 用户付费数据';


create index paddle_user_payment_data_user_id_index
    on paddle_user_payment_data (user_id);

create index paddle_user_payment_data_user_id_created_at_index
    on paddle_user_payment_data (user_id asc, created_at desc);

