create table blog
(
    id                  bigint generated by default as identity
        primary key,
    uid                 varchar,
    title               varchar,
    description         varchar,
    content_title       varchar,
    content_markdown    text,
    language            varchar,
    user_id             varchar,
    is_public           boolean                  default true,
    is_delete           boolean                  default false,
    created_at          timestamp with time zone default now() not null,
    updated_at          timestamp with time zone default now() not null,
    generator           varchar,
    blog_url            varchar,
    content_description varchar,
    content_banner      varchar,
    form_title          varchar,
    form_description    varchar
);

comment on table blog is 'blog table';

comment on column blog.id is 'sequence id';

comment on column blog.uid is 'uid';

comment on column blog.title is 'title';

comment on column blog.description is 'description';

comment on column blog.content_title is 'content_title';

comment on column blog.content_markdown is 'content_markdown';

comment on column blog.language is '语言';

comment on column blog.user_id is 'user_id';

comment on column blog.is_public is '是否公开，默认true';

comment on column blog.is_delete is '是否删除，默认false';

comment on column blog.created_at is 'created_at';

comment on column blog.updated_at is 'updated_at';

comment on column blog.blog_url is 'url';

comment on column blog.content_description is 'content_description';

comment on column blog.content_banner is 'content_banner';

comment on column blog.form_title is 'form_title';

comment on column blog.form_description is 'form_description';

create index blog_created_at_index
    on blog (created_at desc);

create index blog_created_at_is_delete_index
    on blog (created_at desc, is_delete asc);

create index blog_created_at_is_public_index
    on blog (created_at desc, is_public asc);

create index blog_created_at_is_public_is_delete_index
    on blog (created_at desc, is_public asc, is_delete asc);

create index blog_uid_index
    on blog (uid);

create index blog_generator_index
    on blog (generator);

