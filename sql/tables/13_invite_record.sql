create table invite_record
(
    id                 bigint generated by default as identity
        primary key,
    invite_code        varchar                                not null,
    invite_user_id     varchar                                not null,
    created_at         timestamp with time zone default now() not null,
    updated_at         timestamp with time zone default now() not null,
    used               boolean                  default false not null,
    paid               boolean                  default false not null,
    is_delete          boolean                  default false not null,
    used_add_times     integer,
    paid_add_times     integer,
    register_add_times integer
);

comment on table invite_record is '邀请记录表';

comment on column invite_record.invite_code is '邀请码';

comment on column invite_record.invite_user_id is '被邀请user_id';

comment on column invite_record.created_at is '创建时间';

comment on column invite_record.updated_at is '更新时间';

comment on column invite_record.used is '是否使用过服务，即生成过';

comment on column invite_record.paid is '是否付过费';

comment on column invite_record.is_delete is '是否删除';

comment on column invite_record.used_add_times is '用过增加次数';

comment on column invite_record.paid_add_times is '支付增加次数';

comment on column invite_record.register_add_times is '注册增加次数';


create index invite_record_invite_code_index
    on invite_record (invite_code);

create index invite_record_invite_user_id_index
    on invite_record (invite_user_id);

