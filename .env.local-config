#--------------------------------------------------------------------------------------------------------
# Site Base Config
#--------------------------------------------------------------------------------------------------------
NEXT_PUBLIC_SUPPORT_EMAIL=""
NEXT_PUBLIC_SITE_NAME_CONFIG="Tiny Fun"
NEXT_PUBLIC_POLICY_EFFECTIVE_DATE="2025-06-19"
NEXT_PUBLIC_COPYRIGHT=""
NEXT_PUBLIC_DOMAIN_NAME_LOWERCASE=localhost
NEXT_PUBLIC_DOMAIN_NAME=localhost
NEXT_PUBLIC_SITE_URL=http://localhost

# 是否使用数据库
NEXT_PUBLIC_USE_DATABASE=0

# 是否使用共享数据库
NEXT_PUBLIC_USE_SHARE_DATABASE=0

#--------------------------------------------------------------------------------------------------------
# postgres config
POSTGRES_URL="***************************************************************************************"

#--------------------------------------------------------------------------------------------------------
# Google auth config
# 0 代表不检查登录，则登录相关的按钮也不展示出来，1代表要检查
NEXT_PUBLIC_CHECK_GOOGLE_LOGIN=0
# google client id-=-=-=-=-=-=-=-->-------->每个项目不一样，需要更改
NEXT_PUBLIC_GOOGLE_CLIENT_ID="855823423359-mv24o4kp6g3uu3ms8v7o7r382vlkrcve.apps.googleusercontent.com"
# google secret id-=-=-=-=-=-=-=-->-------->每个项目不一样，需要更改
GOOGLE_SECRET_ID="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"

#--------------------------------------------------------------------------------------------------------
# NEXTAUTH config       -=-=-=-=-=-=-=-=-=->-=-=-=-=-=-=-=-=-=->每个项目不一样，需要更改
NEXTAUTH_URL=http://localhost
# create command: openssl rand -base64 32
NEXTAUTH_SECRET="VcDQbEATU+TmObjiMwdXHfKRfulttCUyrln4PHZLuIA="

#--------------------------------------------------------------------------------------------------------
# Analytics ID
# Google
NEXT_PUBLIC_GOOGLE_OPEN=0
NEXT_PUBLIC_GOOGLE_TAG_ID=G-
# baidu
NEXT_PUBLIC_BAIDU_OPEN=0
NEXT_PUBLIC_BAIDU_ID=
# Plausible
NEXT_PUBLIC_PLAUSIBLE_OPEN=0
NEXT_PUBLIC_PLAUSIBLE_SERVER_DOMAIN=click.pageview.click
# clarity
NEXT_PUBLIC_CLARITY_OPEN=0
NEXT_PUBLIC_CLARITY_ID=

#--------------------------------------------------------------------------------------------------------
# Google ads ID
# 展示 Google 广告
NEXT_PUBLIC_GOOGLE_ADS_OPEN=0
NEXT_PUBLIC_GOOGLE_ADS_ID=ca-pub-
# Google 广告投放
NEXT_PUBLIC_GOOGLE_AW_OPEN=0
NEXT_PUBLIC_GOOGLE_AW_ID=AW-

#--------------------------------------------------------------------------------------------------------
# 支付相关配置
# 0 代表不检查支付，则支付页面也不展示出来，1代表要检查
NEXT_PUBLIC_CHECK_AVAILABLE_TIME=0

# 一次性付费0 或 订阅付费1
NEXT_PUBLIC_PRICING_TYPE=0

# 支付通道 0:stripe 1:paddle 4: paypro
NEXT_PUBLIC_PRICING_WAY=4

# stripe config
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# paddle config
NEXT_PUBLIC_PADDLE_SANDBOX=true
NEXT_PUBLIC_PADDLE_CLIENT_TOKEN=test_9d2f930d4ddb8754112a6445d57
NEXT_PUBLIC_PADDLE_SELLER_ID=21437
PADDLE_WEBHOOK_SECRET=pdl_ntfset_01j3ypna73p82kjjybdzrtnb0r_TkY0uOAahlUrrlr34IPwI72TA2DbQh2q
PADDLE_API_KEY=5b14dc9c88f788135d5e5eb404df983983faf3e4292fcaaa18

# paypro config
NEXT_PUBLIC_PAYPRO_SANDBOX=true
PAYPRO_VENDOR_ACCOUNT_ID='168751'
PAYPRO_SECRET_KEY='JspMEzVBhA'
NEXT_PUBLIC_PAYPRO_SECRET_KEY='JspMEzVBhA'
PAYPRO_VALIDATION_KEY='?aZ?Z7yWOGzNOyXjjcRHnQ2Trtpn97'

#--------------------------------------------------------------------------------------------------------
# cloudflare R2 config                           =-=-=-=-=-=-=-=-=->每个项目不一样，需要更改
NEXT_PUBLIC_STORAGE_URL=https://s.sprunkigame.com
STORAGE_DOMAIN=s.sprunkigame.com
R2_BUCKET=sprunkigame-com
R2_TOKEN_VALUE=22HhNBxoTtxKptGRTi5zt3x6ZYbFXfqF3lXdI1Rx
R2_ACCESS_KEY_ID=79ae9ceda651746ca71e5eeedf2a95e1
R2_SECRET_ACCESS_KEY=0194bb16c3fa12841fe00af14daa24e8020447f94adafb5e487e988f442ad462
R2_ACCOUNT_ID=55c26e04f655177fd0b0cd1be8dfb8b0
R2_ENDPOINT=https://55c26e04f655177fd0b0cd1be8dfb8b0.r2.cloudflarestorage.com

#--------------------------------------------------------------------------------------------------------
# openai config
OPENAI_API_KEY=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
OPENAI_API_BASE_URL=https://openrouter.ai/api
OPENAI_API_MODEL=openai/gpt-4o-mini
OPENROUTER_CONFIG_SITE_URL=
OPENROUTER_CONFIG_SITE_NAME=

#--------------------------------------------------------------------------------------------------------
# replicate config
REPLICATE_API_TOKEN=
REPLICATE_API_VERSION=

#--------------------------------------------------------------------------------------------------------
# 企业微信通知url
NOTIFY_QIYE_WEIXIN_URL="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8d8a6465-cc3d-49ab-889b-cbc4956f0937"

#--------------------------------------------------------------------------------------------------------
# WEBHOOK config，一般就是当前域名接收回调结果，本地调试时可以用 ngrok 代理 https://dashboard.ngrok.com/
HANDLE_WEBHOOK=https://4a8a-199-15-78-251.ngrok-free.app

#--------------------------------------------------------------------------------------------------------
# 保存文件的 Worker
SAVE_FILE_WORKER="https://upload-file-to-r2-v3-test-0311.qiayue9582.workers.dev"
