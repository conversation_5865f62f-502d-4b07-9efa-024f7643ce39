<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three Column Layout Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="min-h-screen p-4">
        <h1 class="text-white text-3xl font-bold text-center mb-8">Three Column Layout Test</h1>
        
        <!-- 三栏布局容器 -->
        <div class="flex max-w-[1600px] mx-auto px-4 sm:px-6 relative z-20 gap-6 items-start min-h-screen">
            
            <!-- 左侧边栏 - 在 lg (1024px) 及以上显示 -->
            <div class="hidden lg:block w-80 flex-shrink-0 sticky top-8 max-h-[calc(100vh-4rem)] overflow-y-auto">
                <div style="background-color: rgba(0,0,0,0.18); backdrop-filter: blur(12px); border-radius: 1rem; padding: 1.5rem;">
                    <h3 class="text-xl font-bold mb-4 text-center text-white">Popular Games</h3>
                    <div class="space-y-4">
                        <div style="background-color: rgba(0,0,0,0.15); border-radius: 0.75rem; padding: 1rem;">
                            <h4 class="text-white text-sm font-semibold mb-2">Test Game 1</h4>
                            <p class="text-gray-300 text-xs mb-3">Test game for left sidebar</p>
                            <button class="w-full bg-orange-500 text-white px-3 py-1 rounded text-xs">Play</button>
                        </div>
                        <div style="background-color: rgba(0,0,0,0.15); border-radius: 0.75rem; padding: 1rem;">
                            <h4 class="text-white text-sm font-semibold mb-2">Test Game 2</h4>
                            <p class="text-gray-300 text-xs mb-3">Test game for left sidebar</p>
                            <button class="w-full bg-orange-500 text-white px-3 py-1 rounded text-xs">Play</button>
                        </div>
                        <div style="background-color: rgba(0,0,0,0.15); border-radius: 0.75rem; padding: 1rem;">
                            <h4 class="text-white text-sm font-semibold mb-2">Test Game 3</h4>
                            <p class="text-gray-300 text-xs mb-3">Test game for left sidebar</p>
                            <button class="w-full bg-orange-500 text-white px-3 py-1 rounded text-xs">Play</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 中间主内容区域 -->
            <div class="flex-1 min-w-0">
                <section class="relative">
                    <div class="relative z-20">
                        <div class="text-center mb-8">
                            <h1 class="text-4xl md:text-5xl font-bold mb-4 text-white">Main Game Content</h1>
                            <p class="text-lg text-gray-200 mb-8">This is the main content area where the game iframe would be displayed.</p>
                        </div>
                        
                        <!-- 模拟游戏区域 -->
                        <div class="relative mb-8">
                            <div style="background-color: rgba(0,0,0,0.2); border-radius: 1rem; padding: 2rem; backdrop-filter: blur(8px);">
                                <div class="aspect-video bg-gray-800 rounded-lg flex items-center justify-center">
                                    <p class="text-white text-xl">Game Iframe Area</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分享按钮区域 -->
                        <div class="flex items-center justify-center gap-4 mt-6">
                            <div class="w-9 h-9 bg-blue-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">FB</span>
                            </div>
                            <div class="w-9 h-9 bg-blue-400 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">TW</span>
                            </div>
                            <div class="w-9 h-9 bg-orange-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-xs">RD</span>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- 右侧边栏 - 在 xl (1280px) 及以上显示 -->
            <div class="hidden xl:block w-80 flex-shrink-0 sticky top-8 max-h-[calc(100vh-4rem)] overflow-y-auto">
                <div style="background-color: rgba(0,0,0,0.18); backdrop-filter: blur(12px); border-radius: 1rem; padding: 1.5rem;">
                    <h3 class="text-xl font-bold mb-4 text-center text-white">More Games</h3>
                    <div class="space-y-4">
                        <div style="background-color: rgba(0,0,0,0.15); border-radius: 0.75rem; padding: 1rem;">
                            <h4 class="text-white text-sm font-semibold mb-2">Test Game 7</h4>
                            <p class="text-gray-300 text-xs mb-3">Test game for RIGHT sidebar</p>
                            <button class="w-full bg-orange-500 text-white px-3 py-1 rounded text-xs">Play</button>
                        </div>
                        <div style="background-color: rgba(0,0,0,0.15); border-radius: 0.75rem; padding: 1rem;">
                            <h4 class="text-white text-sm font-semibold mb-2">Test Game 8</h4>
                            <p class="text-gray-300 text-xs mb-3">Test game for RIGHT sidebar</p>
                            <button class="w-full bg-orange-500 text-white px-3 py-1 rounded text-xs">Play</button>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- 移动端推荐游戏区域 -->
        <div class="block lg:hidden mt-12 px-4">
            <div class="text-center mb-8">
                <h2 class="text-2xl font-bold mb-4 text-white">More Games You Might Like</h2>
                <p class="text-base opacity-80 text-gray-200">Discover more exciting games to play</p>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div style="background-color: rgba(0,0,0,0.15); border-radius: 1rem; padding: 1.5rem;">
                    <h4 class="text-white text-lg font-semibold mb-2">Mobile Game 1</h4>
                    <p class="text-gray-300 text-sm mb-4">Mobile game description</p>
                    <button class="w-full bg-orange-500 text-white px-4 py-2 rounded">Play Now</button>
                </div>
                <div style="background-color: rgba(0,0,0,0.15); border-radius: 1rem; padding: 1.5rem;">
                    <h4 class="text-white text-lg font-semibold mb-2">Mobile Game 2</h4>
                    <p class="text-gray-300 text-sm mb-4">Mobile game description</p>
                    <button class="w-full bg-orange-500 text-white px-4 py-2 rounded">Play Now</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 显示当前屏幕宽度和断点信息
        function updateScreenInfo() {
            const width = window.innerWidth;
            const info = document.getElementById('screen-info') || document.createElement('div');
            info.id = 'screen-info';
            info.className = 'fixed top-4 right-4 bg-black bg-opacity-75 text-white p-2 rounded text-sm z-50';
            
            let breakpoint = 'xs';
            if (width >= 1280) breakpoint = 'xl (≥1280px) - Both sidebars';
            else if (width >= 1024) breakpoint = 'lg (≥1024px) - Left sidebar only';
            else if (width >= 768) breakpoint = 'md (≥768px) - No sidebars';
            else if (width >= 640) breakpoint = 'sm (≥640px) - No sidebars';
            
            info.innerHTML = `Width: ${width}px<br>Breakpoint: ${breakpoint}`;
            
            if (!document.body.contains(info)) {
                document.body.appendChild(info);
            }
        }
        
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
