---
type: "always_apply"
---

现在执行任务！进行编写代码，在现有项目的基础上，不要乱改代码，只执行这个步骤，要遵循开发计划，最佳实践的，写代码之前先看一看是否已经有相关实现，不要重复，也不要反复创建新文件，。注意一个文件代码行数不得超过 300 行，超过了要使用SOLID 原则拆分，

🔒 三不原则：
1. 不改架构/依赖/无关文件
2. 不做计划外/重复功能
3. 不创新文件/相似逻辑

🔍 执行铁律：
① 写码前必查 → 现有实现 & 文件存在性
② 决策三步走 → 读文档→看计划→对代码 → 出方案 → 选SOLID最优解→执行修改
③ 变更仅限当前需求范围

🛡️ 防御性检查：
▷ 验证三刀：
  ① SOLID合规性扫描 
  ③ 技术债检测 → 不引入新债务
   ▷ 黄金符：✅SOLID+✅KISS+✅DRY+✅YAGNI+✅LoD+




— 任务边界 —
• 仅落地当前需求；禁止触碰架构、依赖、无关文件  
• 禁止重复/计划外功能；禁止冗余新文件或相似逻辑  

— 行动准则 —
1. 预检  
   ① 读需求 & 迭代计划  
   ② 搜索代码库：确认是否已有实现/文件  
2. 方案  
   • 评估影响面 → 拆分任务 → 选择最佳 SOLID 方案  
3. 实施  
• 单文件 ≤300 行，超出即拆分  
• 保持团队代码风格（Lint/Formatter 自动化）  
• 必写单元测试 & 更新集成测试  
4. 校验  
✅ SOLID✅ KISS ✅ DRY ✅ YAGNI ✅ LoD  
+ 代码扫描：静态分析 / 安全 / 技债  
+ 性能基准：无显著回退  
5. 提交  
   • PR 说明：变更点、设计要点、测试覆盖、回滚指引  
   • CI/CD 全绿后合并  

— 必备保障 —
• 日志等级与异常链保持一致  
• 变更可回滚、可复现  
• 所有接口/DB 迁移向后兼容  

立即执行；任何偏离视为无效。



==============================

“现在根据以下原则和步骤，在现有项目的基础上完成指定任务。请务必遵循开发计划和最佳实践。在编写代码之前，请CoT思考并扫描代码确认是否已存在相关实现，避免重复工作和创建不必要的文件。

🔒 **三不原则 (安全红线):**
1.  **不改动架构/依赖/无关文件:** 仅修改与当前任务直接相关的文件。无关文件示例：配置文件、构建脚本等。
2.  **不做计划外/重复功能:**  严格按照需求文档执行，避免引入未经批准的功能。需求来源：[文档版本/需求ID]
3.  **不创新文件/相似逻辑:**  除非现有模块完全无法满足需求，且经过评审确认，否则禁止创建新文件或编写重复逻辑。

🛠️ **执行步骤:**
1.  **需求分析:**  [CoT: 需求理解] 详细分析任务需 
2.  **环境评估:**  [CoT: 环境评估] 扫描项目代码，查找相关实现和文件。用 Mermaid 明确代码文件依赖关系
3.  **方案设计:**  [CoT: 方案设计] 设计代码修改方案，并说明理由。
4.  **代码实现:**  [CoT: 代码实现] 了解代码中需要改动的代码中函数，方法的调用链 用 Mermaid 语法，然后执行任务！进行编写代码！！。注意一个文件代码行数不得超过 300 行，超过了要使用SOLID 原则拆分，
5.  **代码审查:**  进行代码审查，调用链 用 Mermaid方法进行审查，确保代码质量。

🛡️ **防御性检查:**
! 每次输出必须包含以下验证标记：
-   ✅ **需求对齐:** [关键词扩展系统详细需求.md/需求ID]
-   ✅ **计划阶段:** [当前开发阶段]
-   ✅ **CoT 思路:**  清晰地描述上述 Chain-of-Thought 的每个步骤的思考过程。 
-   ✅ **代码扫描:** [相关类/文件列表]
-   ✅ **SOLID 原则应用:** [说明应用了哪些 SOLID 原则，以及如何应用]


==============================

🧠 **Chain-of-Thought (CoT) 思考链:**
1.  **[需求理解]**  详细解读任务需求，明确输入、输出和约束条件。需求来源：[文档版本/需求ID]
2.  **[环境评估]**  审视现有项目代码，利用代码扫描工具（如 Cursor 的代码扫描或 `grep` 命令）查找相关实现和文件。
3.  **[方案设计]**  基于需求和现有代码，设计最优解决方案。优先考虑复用现有代码，遵循 SOLID 原则，并明确说明选择的 SOLID 原则。
4.  **[代码实现]**  编写代码，确保代码风格一致、注释清晰，并易于理解和维护。
5.  **[代码测试]**  编写单元测试或其他测试用例，验证代码的正确性。
6.  **[结果验证]**  检查代码是否符合需求，是否遵循了三不原则，以及是否满足 SOLID 原则。

🛠️ **执行步骤:**
1.  **需求分析:**  [CoT: 需求理解] 详细分析任务需求。
2.  **环境评估:**  [CoT: 环境评估] 扫描项目代码，查找相关实现和文件。
3.  **方案设计:**  [CoT: 方案设计] 设计代码修改方案，并说明理由。
4.  **代码实现:**  [CoT: 代码实现] 编写代码。
5.  **代码审查:**  进行代码审查，确保代码质量。


🛡️ **防御性检查:**
! 每次输出必须包含以下验证标记：
-   ✅ **需求对齐:** [文档版本/需求ID]
-   ✅ **计划阶段:** [当前开发阶段]
-   ✅ **CoT 思路:**  清晰地描述上述 Chain-of-Thought 的每个步骤的思考过程。
-   ✅ **代码扫描:** [相关类/文件列表]
-   ✅ **SOLID 原则应用:** [说明应用了哪些 SOLID 原则，以及如何应用]
-   ✅ **分支检查:** [当前代码修改位于 `dev` 分支]




==============================


5W2H 解构需求，推荐 3个mvp，选择一个符合当前项目规范的 mvp 执行  ▷ 黄金符：✅SOLID+✅KISS+✅DRY+12-Factor 




现在阅读开发计划，开发进度，然后想一想下一步做什么，先不要写代码，先想一想怎么做，要符合最佳实践开发，遵循开发文档，在现有的需求，构架上进行,


5W2H 解构需求，推荐 1 个 mvp 的 ▷ 黄金符：✅SOLID+✅KISS+✅DRY+12-Factor +YAGNI 

你的任务是：阅读本项目文档需求 开发进度，然后思考下一步的编程任务应该做什么。 请以最佳实践为指导，遵循开发文档，并在现有架构上进行开发。 请使用 Chain-of-Thought (CoT) 方法进行思考和分析。

❏ 阶段1：文档对齐
▷ 提取开发文档中相关章节 [§x.x]
▷ 确认当前项目阶段里程碑 [Phase X]
▷ 必须遵守的技术规范

❏ 阶段2：代码勘探
▷ 扫描现有代码库（相关模块/类/函数）
❏ 阶段3：任务原子化
▷ 识别 Features: 将需求分解为多个逻辑上独立的、manageable 的功能模块 (Features)。
细化 Tasks: 对于每个 Feature，列出实现它所需的一系列具体的、可执行的开发任务 (Tasks)。确保每个 Task 是清晰且可操作的。
▷ 每个任务满足：
  ✅ 单一职责原则
  ✅ 可独立验证

❏ 阶段4：最佳实践验证
 生成唯一方案必须满足：
  ✅ 零修改无关文件
  ✅ 复用率
  ✅ 文档映射率100%

▷ 验证三刀：
  ① SOLID合规性扫描 
  ③ 技术债检测 → 不引入新债务
   ▷ 黄金符：✅SOLID+✅KISS+✅DRY+✅YAGNI+✅LoD+





核心任务： 基于提供的项目信息，规划出下一步最合适的编程任务。

关键指令：

严禁编码： 你的任务是 思考和规划，目前 绝对不要 编写任何实际代码。

信息输入： 请先仔细阅读并理解以下信息源：

最新的 开发计划 (Development Plan)

当前的 开发进度报告 (Progress Report)

相关的 项目需求文档 (Requirement Documents)

现有 代码库的相关部分 (Relevant parts of the codebase)

指导原则与约束：

遵循最佳实践： 严格遵循软件工程最佳实践进行规划。

文档驱动： 规划必须与 开发文档 保持一致。

架构兼容： 规划需在 现有架构 基础上进行，避免破坏性改动。

方法论应用： 运用 5W2H 解构需求，思考推荐 1 个 MVP 范围的任务。

质量核心（黄金符）： 规划的任务设计应内在地倾向于满足 ✅SOLID + ✅KISS + ✅DRY + ✅12-Factor + ✅YAGNI + ✅LoD (迪米特法则)。

思考与规划框架（必须遵循）：

请使用 Chain-of-Thought (CoT) 方法，并严格按照以下 四个阶段 进行结构化思考和分析：

❏ 阶段1：文档与上下文对齐 (Document & Context Alignment)
* ▷ 提取关键信息： 从开发计划、进度和需求文档中，提取与【下一步潜在任务】最相关的章节、目标或 [§x.x]。
* ▷ 确认当前阶段： 明确项目当前所处的开发阶段/里程碑 [Phase X]。
* ▷ 识别核心规范： 列出规划下一步任务时必须遵守的关键技术规范或约束。

❏ 阶段2：代码勘探与现状分析 (Code Exploration & Status Quo Analysis)
* ▷ 扫描相关代码： 基于阶段1的理解，扫描现有代码库，识别与【下一步潜在任务】相关的模块、类、函数或接口。
* ▷ 评估集成点： 分析新任务如何在现有代码结构中集成。

❏ 阶段3：任务原子化与定义 (Task Atomization & Definition)
* ▷ 识别核心 Feature： 基于需求和现状，识别并定义出逻辑上独立的【下一步核心功能模块 (Feature)】。
* ▷ 细化为 Tasks： 将识别出的 Feature 分解为一系列具体的、可操作的开发任务 (Tasks)。
* ▷ 确保任务质量： 每个 Task 必须满足：
* ✅ 单一职责原则 (Single Responsibility Principle)
* ✅ 可独立验证 (Independently Verifiable)

❏ 阶段4：最佳实践符合性与可行性验证 (Best Practice Compliance & Feasibility Validation)
* ▷ 生成唯一推荐计划： 汇总思考，明确提出【一个】推荐的下一步任务计划（包含分解后的 Tasks 列表）。
* ▷ 验证计划质量： 确保此推荐计划满足以下硬性要求：
* ✅ 零无关文件修改 (Zero unrelated file modifications): 计划实施时不应触及无关文件。
* ✅ 高复用性潜力 (High Reusability Potential): 设计应考虑代码复用。
* ✅ 文档完全映射 (100% Document Mapping): 计划中的每个 Task 应能追溯到文档中的需求或规范。
* ▷ 执行最终检查（“验证三刀”）： 对推荐计划进行最终审视：
* ① SOLID 合规性扫描： 检查计划的设计是否符合 SOLID 原则。
* ② 技术债预检： 评估计划实施是否会引入新的技术债（目标：不引入）。
* ③ 黄金符再确认： 再次确认计划是否倾向于满足 ✅SOLID+✅KISS+✅DRY+✅YAGNI+✅LoD。

输出要求：

请清晰地展示你的 Chain-of-Thought (CoT) 分析过程。

按照上述 四个阶段 的结构，详细报告每个阶段的发现和结论。

最终明确提出 推荐的下一步任务计划（包含原子化的 Task 列表）。

重要： 在我明确表示 “批准计划，可以继续” 或给出进一步指示之前，请勿 进行任何超出此规划范围的操作。




==============================
你可以将时序图视为项目的设计说明书，将调用链/源代码视为具体的施工记录或材料清单。要让 AI 既懂设计意图又懂具体实现，两者结合使用通常是最佳策略，能够达到最清晰、歧义最小的理解效果。



Guide 文档   sequenceDiagram（时序图）



现在完成了这个小结的代码，你需要更新开发进度，测试好功能是否可用，都正常使用了，进行提交 github 的操作，遵循最佳实践的做法！.gitignore 已经创建了

🔢 更新流：
1. 测功能 → 主路径+边界case
2. 更进度 → [✓里程碑] [更日志]
3. 发提交 → feat/fix(模块) 

go web gin SoC 目录结构的最佳实践 
根据需求写一个详细的Guide 文档，包括《Database Blueprint》内容需要包含表结构设计、字段说明、关系说明、结构演进建议、表与功能模块的映射关系等。 用这个提示词可以生成很详细的开发文档，

==============================

  修复三律：1️⃣ 精：复杂度≤原方案80%，2️⃣ 准：直击根本原因 ，3️⃣ 净：0技术债务(SonarQube✔️)，⚙️ 三步走：① 溯源 → 函数，打印方法调用链→ 错误触发路径  ② 拆解 → 给出3个SOLID++方案  
   验证三刀：
    ① SOLID合规性扫描 
    ③ 技术债检测 → 不引入新债务
     ▷ 黄金符：✅SOLID+✅KISS+✅DRY+✅YAGNI+✅LoD+




【专业精简版 · 缺陷排查与修复指令】

==================== 任务目标 ====================
在既有代码库中定位并根治指定 BUG，保证：
• 复杂度 ≤ 原实现 80%  
• 命中根因，非表象  
• 修复后 SonarQube 0 新技术债

==================== 修 复 三 律 🔧 ====================
1️⃣ 精 —— 方案精简，复杂度压缩 ≥20%  
2️⃣ 准 —— 直击 Root Cause，非绕路修补  
3️⃣ 净 —— 修复不留债，静态扫描 0 Issue

==================== 思 维 链 · 三 步 走 ====================
Step 1 溯源  
  ① 复现：构造最小可复现用例  
  ② 打点：开启详细日志 / 调试器  
  ③ 构建调用链：函数→模块→系统，捕获错误触发路径

Step 2 拆解  
  • 基于 Root Cause 给出 3 套 SOLID++ 方案  
  • 对比复杂度、影响面、回归风险 → 选最优

Step 3 实施  
  • 精修代码；单文件 ≤300 行，必要时按 SOLID 拆分  
  • 编写/更新单元 + 集成测试；确保 100% 复现用例通过  
  • 更新文档 / 变更日志

==================== 验 证 三 刀 🛡️ ====================
① SOLID 合规扫描  
② KISS / DRY / YAGNI / LoD 全面自检  
③ 技术债扫描（SonarQube）—— 不引入新债务

==================== 提 交 要 求 ====================
• Pull Request 附：  
  – Root Cause 说明 + 调用链截图/日志  
  – 选型理由 + 复杂度对比  
  – 测试覆盖报告 + 回滚指引  
• CI/CD 全绿后合并

立即执行；任何偏离视为无效。





【Bug‑Fix 专用硬核指令 · 精炼版】

🎯 任务唯一目标：定位 ➜ 修复 ➜ 验证 指定 BUG

一、修复三律  
1️⃣ 精：复杂度 ≤ 原实现 80%  
2️⃣ 准：直击 Root Cause  
3️⃣ 净：0 新技术债（Sonar / Static‑Scan ✅）

二、思维链 3‑Step  
1. Root‑Cause Trace  
• 复现缺陷 → Fail‑Fast Log  
• Call‑Stack Audit，锁定 Impact Radius≤1 模块  
2. SOLID++ Tri‑Option Matrix  
• 提供 3 份 Diff‑Minimize Patch  
• 评估复杂度 / 回归风险 → 选 Idempotent Fix  
3. Zero‑Debt Gate  
• 单元＋集成测试 → Regression‑Safe  
• 静态扫描 → Zero‑Debt  
• Perf 基线 → Perf‑Neutral  
 CI 绿灯 → Rollback‑Ready

三、交付物 Checklist  
☑️ 根因与调用栈截图  
☑️ 选定 Patch Diff  
☑️ 测试覆盖 & 性能报告  
☑️ 回滚指引

—— 将本指令直接投喂 AI，即可确保专注 BUG 修复并满足全部质量门槛。



export GEMINI_API_KEY=AIzaSyAj3AZhjszzQBfmPVbtmuvEkkQxIottBDE

GEMINI_API_KEY=AIzaSyDUZbUUDh7yFKvZdpgD_i1ouba5RZJJ0Y0


  ⚙️ CDD模式: {DomainModel}+{OpenAPI}  
  → 设计: ✅SOLID+KISS+DRY+YAGNI+LoD  
  → 特性: 幂等API✅ + ACID✅ + Prometheus埋点✅  
  → 质量门: 契约冲突⛔自动阻断 → 用❗Clarify追问  
  → 交付: Self-documenting + SemVer     ③ 技术债检测 → 不引入新债务
     ▷ 黄金符：✅SOLID+✅KISS+✅DRY+✅YAGNI+✅LoD+


  graph TD
    A[修复三律] --> B[精<br>复杂度≤80%]
    A --> C[准<br>直击根因]
    A --> D[净<br>0技术债务]
    
    E[⚙️ 三步走] --> F[溯源<br>错误触发路径]
    E --> G[拆解<br>SOLID++方案]
    E --> H[执行<br>验证闭环]
    
    B --> I[• 策略模式替换分支<br>• Stream API重构]
    C --> J[• APM调用链追踪<br>• 根因影响矩阵]
    D --> K[• SonarQube质量门<br>• 安全热修复A级]
    
    G --> L[• S单一职责<br>• O开放封闭<br>• 可观测埋点]




**核心要求:**
*   **内部严格遵循:** CoT 驱动，按步骤分析。**必须**找到根因，**必须明确列出导致错误的函数/方法调用链和触发路径**。**必须**严格应用设计原则 (✅SOLID, ✅KISS, ✅DRY, ✅YAGNI, ✅LoD)。
*   **简洁输出:** 每步仅输出核心结果和关键决策。详细思考过程保留在内部。若遇不确定性，必须提问。

---

**内部执行步骤 (AI 需严格遵循):**

1.  **分析/溯源:** 理解错误 -> **明确识别并记录导致错误的函数/方法调用链及具体触发路径** -> 基于此定位并阐述**根本原因**。 (遇歧义先提问)
2.  **方案设计:** 基于根因设计方案 -> **显式说明如何应用各设计原则 (✅SOLID...)** -> 评估影响/风险。
3.  **代码实现:** 编码 -> **确保代码严格体现设计原则** -> 添加必要注释。
4.  **验证/解释:** 检查代码逻辑/预期行为 -> **确认严格符合设计原则** -> 准备解释。

---

**输出格式 (每步提供):**

*   ✅ **阶段:** [当前执行步骤，例如：1. 分析/溯源]
*   ✅ **关键发现/决策:** [简述本步核心结论，**如：根本原因总结；识别出的关键调用链/错误路径概要；** 方案选择理由；关键代码改动点；验证结果]
*   **[核心产出物]:** [根据阶段提供：**(若适用)详细调用链/路径**；方案描述；**修复后的代码片段**；修复解释]

---

### 📋 分析需求：
**为AI代码助手提供完整执行流程，便于理解代码逻辑和定位Bug**

### ⚙️ 输出格式：

**① 前端调用链**
```
用户操作 → 组件事件 → API调用 → 请求参数
├─ 触发组件：[组件名]
├─ 事件处理：[函数名] 
├─ HTTP请求：[方法+URL+参数]
└─ 错误处理：[异常捕获逻辑]
```

**② 后端执行链**  
```
路由接收 → 中间件 → 控制器 → 服务层 → 数据层
├─ 路由定义：[路径+方法+处理器]
├─ 中间件链：[认证+权限+参数验证]
├─ 业务逻辑：[服务类+核心方法]
├─ 数据操作：[模型+SQL+缓存]
└─ 响应处理：[数据格式+状态码]
```

**③ 关键节点**
- **入参验证**：参数类型+必填项+格式校验
- **权限检查**：角色验证+资源权限
- **业务逻辑**：核心算法+数据处理
- **异常处理**：错误类型+回滚机制
- **性能节点**：数据库查询+缓存策略

### 🎯 补充建议：
- **数据流向**：请求→处理→响应的数据变化
- **依赖关系**：外部服务+第三方接口调用
- **状态管理**：会话+缓存+数据库状态变更

**使用方式**：提供代码 → 按此格式输出完整调用链分析
